/**
 * 💩🎉TurdParty🎉💩 Centralized Service URL Management
 * 
 * This utility provides a canonical way to access service URLs across all
 * TurdParty components. It reads from the central config/service-urls.json
 * file to ensure consistency.
 */

import serviceConfig from '../config/service-urls.json';

/**
 * Service URL Manager
 * Provides methods to get service URLs based on environment
 */
class ServiceURLManager {
    constructor() {
        this.config = serviceConfig;
        this.environment = this.detectEnvironment();
    }

    /**
     * Detect the current environment
     * @returns {string} Environment name
     */
    detectEnvironment() {
        // Check environment variables first
        if (typeof process !== 'undefined' && process.env) {
            const envVar = process.env.TURDPARTY_ENV || process.env.NODE_ENV;
            if (envVar) {
                // Map common NODE_ENV values to our environments
                const envMap = {
                    'development': 'development',
                    'dev': 'development',
                    'staging': 'staging',
                    'stage': 'staging',
                    'production': 'production',
                    'prod': 'production',
                    'local': 'local'
                };
                return envMap[envVar.toLowerCase()] || 'development';
            }
        }

        // Check browser location for development
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname;
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                // Check if using Traefik domains
                if (hostname.includes('turdparty.localhost')) {
                    return 'development';
                }
                return 'local';
            }
            if (hostname.includes('staging')) {
                return 'staging';
            }
            if (hostname.includes('turdparty.com')) {
                return 'production';
            }
        }

        // Default to development for Traefik setup
        return 'development';
    }

    /**
     * Get the current environment configuration
     * @returns {object} Environment configuration
     */
    getEnvironmentConfig() {
        return this.config.environments[this.environment];
    }

    /**
     * Build a service URL
     * @param {string} serviceName - Name of the service
     * @param {boolean} includeHealth - Include health endpoint
     * @param {string} customPath - Custom path to append
     * @returns {string} Complete service URL
     */
    getServiceURL(serviceName, includeHealth = false, customPath = '') {
        const envConfig = this.getEnvironmentConfig();
        const serviceConfig = envConfig.services[serviceName];

        if (!serviceConfig) {
            throw new Error(`Service '${serviceName}' not found in environment '${this.environment}'`);
        }

        let url = envConfig.protocol + '://';

        // Build hostname
        if (serviceConfig.subdomain) {
            url += serviceConfig.subdomain + '.' + envConfig.domain;
        } else {
            url += envConfig.domain;
        }

        // Add port if specified
        if (serviceConfig.port) {
            url += ':' + serviceConfig.port;
        }

        // Add service path
        if (serviceConfig.path) {
            url += serviceConfig.path;
        }

        // Add health endpoint if requested
        if (includeHealth && serviceConfig.health_endpoint) {
            url += serviceConfig.health_endpoint;
        }

        // Add custom path
        if (customPath) {
            // Ensure custom path starts with /
            if (!customPath.startsWith('/')) {
                customPath = '/' + customPath;
            }
            url += customPath;
        }

        return url;
    }

    /**
     * Get API endpoint URL
     * @param {string} endpoint - Endpoint path from api_endpoints config
     * @param {object} params - Parameters to replace in endpoint path
     * @returns {string} Complete API endpoint URL
     */
    getAPIEndpoint(endpoint, params = {}) {
        const apiEndpoints = this.config.api_endpoints;
        
        // Navigate through nested endpoint structure
        const endpointPath = endpoint.split('.').reduce((obj, key) => {
            return obj && obj[key];
        }, apiEndpoints);

        if (!endpointPath) {
            throw new Error(`API endpoint '${endpoint}' not found`);
        }

        // Replace parameters in the endpoint path
        let finalPath = endpointPath;
        Object.entries(params).forEach(([key, value]) => {
            finalPath = finalPath.replace(`{${key}}`, value);
        });

        // Get API service base URL
        const apiBaseURL = this.getServiceURL('api');
        
        // Remove /api/v1 from base URL if endpoint already includes it
        if (finalPath.startsWith('/api/v1') && apiBaseURL.includes('/api/v1')) {
            return apiBaseURL.replace('/api/v1', '') + finalPath;
        }
        
        return apiBaseURL + finalPath;
    }

    /**
     * Get relative API endpoint (for same-origin requests)
     * @param {string} endpoint - Endpoint path from api_endpoints config
     * @param {object} params - Parameters to replace in endpoint path
     * @returns {string} Relative API endpoint path
     */
    getRelativeAPIEndpoint(endpoint, params = {}) {
        const apiEndpoints = this.config.api_endpoints;
        
        // Navigate through nested endpoint structure
        const endpointPath = endpoint.split('.').reduce((obj, key) => {
            return obj && obj[key];
        }, apiEndpoints);

        if (!endpointPath) {
            throw new Error(`API endpoint '${endpoint}' not found`);
        }

        // Replace parameters in the endpoint path
        let finalPath = endpointPath;
        Object.entries(params).forEach(([key, value]) => {
            finalPath = finalPath.replace(`{${key}}`, value);
        });

        return finalPath;
    }

    /**
     * Get all service URLs for the current environment
     * @param {boolean} includeHealth - Include health endpoints
     * @returns {object} Object with service names as keys and URLs as values
     */
    getAllServiceURLs(includeHealth = false) {
        const envConfig = this.getEnvironmentConfig();
        const urls = {};

        Object.keys(envConfig.services).forEach(serviceName => {
            urls[serviceName] = this.getServiceURL(serviceName, includeHealth);
        });

        return urls;
    }

    /**
     * Get MinIO bucket name
     * @param {string} bucketType - Type of bucket (uploads, processed, reports, backups)
     * @returns {string} Bucket name
     */
    getMinioBucket(bucketType) {
        const bucket = this.config.minio_buckets[bucketType];
        if (!bucket) {
            throw new Error(`MinIO bucket type '${bucketType}' not found`);
        }
        return bucket;
    }

    /**
     * Get Elasticsearch index pattern
     * @param {string} indexType - Type of index (logs, metrics, vm_data, file_metadata)
     * @returns {string} Index pattern
     */
    getElasticsearchIndex(indexType) {
        const index = this.config.elasticsearch_indices[indexType];
        if (!index) {
            throw new Error(`Elasticsearch index type '${indexType}' not found`);
        }
        return index;
    }

    /**
     * Switch to a different environment
     * @param {string} newEnvironment - Environment to switch to
     */
    switchEnvironment(newEnvironment) {
        if (!this.config.environments[newEnvironment]) {
            throw new Error(`Environment '${newEnvironment}' not found`);
        }
        this.environment = newEnvironment;
    }

    /**
     * Get current environment name
     * @returns {string} Current environment
     */
    getCurrentEnvironment() {
        return this.environment;
    }
}

// Create singleton instance
const serviceURLManager = new ServiceURLManager();

// Export both the class and singleton instance
export { ServiceURLManager, serviceURLManager as default };

// CommonJS compatibility
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ServiceURLManager, default: serviceURLManager };
}
