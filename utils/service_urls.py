"""
💩🎉TurdParty🎉💩 Centralized Service URL Management

This utility provides a canonical way to access service URLs across all
TurdParty components. It reads from the central config/service-urls.json
file to ensure consistency.
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional, Any
from urllib.parse import urljoin


class ServiceURLManager:
    """
    Service URL Manager
    Provides methods to get service URLs based on environment
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ServiceURLManager
        
        Args:
            config_path: Optional path to service-urls.json file
        """
        if config_path is None:
            # Default to config/service-urls.json relative to project root
            project_root = Path(__file__).parent.parent
            config_path = project_root / "config" / "service-urls.json"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.environment = self._detect_environment()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Service URLs config not found at {self.config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in service URLs config: {e}")
    
    def _detect_environment(self) -> str:
        """
        Detect the current environment
        
        Returns:
            Environment name
        """
        # Check environment variables first
        env_var = os.getenv('TURDPARTY_ENV') or os.getenv('ENVIRONMENT')
        if env_var:
            # Map common environment values
            env_map = {
                'development': 'development',
                'dev': 'development',
                'staging': 'staging',
                'stage': 'staging',
                'production': 'production',
                'prod': 'production',
                'local': 'local'
            }
            return env_map.get(env_var.lower(), 'development')
        
        # Check for Docker environment
        if os.path.exists('/.dockerenv'):
            return 'development'  # Default for containerized environment
        
        # Default to development for Traefik setup
        return 'development'
    
    def get_environment_config(self) -> Dict[str, Any]:
        """
        Get the current environment configuration
        
        Returns:
            Environment configuration dictionary
        """
        return self.config['environments'][self.environment]
    
    def get_service_url(self, service_name: str, include_health: bool = False, 
                       custom_path: str = '') -> str:
        """
        Build a service URL
        
        Args:
            service_name: Name of the service
            include_health: Include health endpoint
            custom_path: Custom path to append
            
        Returns:
            Complete service URL
        """
        env_config = self.get_environment_config()
        service_config = env_config['services'].get(service_name)
        
        if not service_config:
            raise ValueError(f"Service '{service_name}' not found in environment '{self.environment}'")
        
        url = f"{env_config['protocol']}://"
        
        # Build hostname
        if service_config['subdomain']:
            url += f"{service_config['subdomain']}.{env_config['domain']}"
        else:
            url += env_config['domain']
        
        # Add port if specified
        if service_config['port']:
            url += f":{service_config['port']}"
        
        # Add service path
        if service_config['path']:
            url += service_config['path']
        
        # Add health endpoint if requested
        if include_health and service_config['health_endpoint']:
            url += service_config['health_endpoint']
        
        # Add custom path
        if custom_path:
            # Ensure custom path starts with /
            if not custom_path.startswith('/'):
                custom_path = '/' + custom_path
            url += custom_path
        
        return url
    
    def get_api_endpoint(self, endpoint: str, params: Optional[Dict[str, str]] = None) -> str:
        """
        Get API endpoint URL
        
        Args:
            endpoint: Endpoint path from api_endpoints config (e.g., 'files.upload')
            params: Parameters to replace in endpoint path
            
        Returns:
            Complete API endpoint URL
        """
        if params is None:
            params = {}
        
        api_endpoints = self.config['api_endpoints']
        
        # Navigate through nested endpoint structure
        endpoint_path = api_endpoints
        for key in endpoint.split('.'):
            endpoint_path = endpoint_path.get(key)
            if endpoint_path is None:
                raise ValueError(f"API endpoint '{endpoint}' not found")
        
        # Replace parameters in the endpoint path
        final_path = endpoint_path
        for key, value in params.items():
            final_path = final_path.replace(f"{{{key}}}", str(value))
        
        # Get API service base URL
        api_base_url = self.get_service_url('api')
        
        # Remove /api/v1 from base URL if endpoint already includes it
        if final_path.startswith('/api/v1') and '/api/v1' in api_base_url:
            api_base_url = api_base_url.replace('/api/v1', '')
        
        return urljoin(api_base_url, final_path)
    
    def get_relative_api_endpoint(self, endpoint: str, params: Optional[Dict[str, str]] = None) -> str:
        """
        Get relative API endpoint (for same-origin requests)
        
        Args:
            endpoint: Endpoint path from api_endpoints config
            params: Parameters to replace in endpoint path
            
        Returns:
            Relative API endpoint path
        """
        if params is None:
            params = {}
        
        api_endpoints = self.config['api_endpoints']
        
        # Navigate through nested endpoint structure
        endpoint_path = api_endpoints
        for key in endpoint.split('.'):
            endpoint_path = endpoint_path.get(key)
            if endpoint_path is None:
                raise ValueError(f"API endpoint '{endpoint}' not found")
        
        # Replace parameters in the endpoint path
        final_path = endpoint_path
        for key, value in params.items():
            final_path = final_path.replace(f"{{{key}}}", str(value))
        
        return final_path
    
    def get_all_service_urls(self, include_health: bool = False) -> Dict[str, str]:
        """
        Get all service URLs for the current environment
        
        Args:
            include_health: Include health endpoints
            
        Returns:
            Dictionary with service names as keys and URLs as values
        """
        env_config = self.get_environment_config()
        urls = {}
        
        for service_name in env_config['services'].keys():
            urls[service_name] = self.get_service_url(service_name, include_health)
        
        return urls
    
    def get_minio_bucket(self, bucket_type: str) -> str:
        """
        Get MinIO bucket name
        
        Args:
            bucket_type: Type of bucket (uploads, processed, reports, backups)
            
        Returns:
            Bucket name
        """
        bucket = self.config['minio_buckets'].get(bucket_type)
        if not bucket:
            raise ValueError(f"MinIO bucket type '{bucket_type}' not found")
        return bucket
    
    def get_elasticsearch_index(self, index_type: str) -> str:
        """
        Get Elasticsearch index pattern
        
        Args:
            index_type: Type of index (logs, metrics, vm_data, file_metadata)
            
        Returns:
            Index pattern
        """
        index = self.config['elasticsearch_indices'].get(index_type)
        if not index:
            raise ValueError(f"Elasticsearch index type '{index_type}' not found")
        return index
    
    def switch_environment(self, new_environment: str) -> None:
        """
        Switch to a different environment
        
        Args:
            new_environment: Environment to switch to
        """
        if new_environment not in self.config['environments']:
            raise ValueError(f"Environment '{new_environment}' not found")
        self.environment = new_environment
    
    def get_current_environment(self) -> str:
        """
        Get current environment name
        
        Returns:
            Current environment
        """
        return self.environment


# Create singleton instance
_service_url_manager = None

def get_service_url_manager() -> ServiceURLManager:
    """Get singleton instance of ServiceURLManager"""
    global _service_url_manager
    if _service_url_manager is None:
        _service_url_manager = ServiceURLManager()
    return _service_url_manager


# Convenience functions for common operations
def get_service_url(service_name: str, include_health: bool = False, custom_path: str = '') -> str:
    """Get service URL using singleton manager"""
    return get_service_url_manager().get_service_url(service_name, include_health, custom_path)


def get_api_endpoint(endpoint: str, params: Optional[Dict[str, str]] = None) -> str:
    """Get API endpoint URL using singleton manager"""
    return get_service_url_manager().get_api_endpoint(endpoint, params)


def get_relative_api_endpoint(endpoint: str, params: Optional[Dict[str, str]] = None) -> str:
    """Get relative API endpoint using singleton manager"""
    return get_service_url_manager().get_relative_api_endpoint(endpoint, params)
