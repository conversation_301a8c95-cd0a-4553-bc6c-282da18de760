# 💩🎉TurdParty🎉💩 Centralized URL Management

## 🎯 **Overview**

TurdParty uses a centralized URL management system to ensure consistency across all services and environments. This eliminates hardcoded localhost URLs and provides a single source of truth for service configuration.

## 📁 **Configuration Structure**

### **Central Configuration**
- **File**: `config/service-urls.json`
- **Purpose**: Canonical source for all service URLs and endpoints
- **Environments**: `development`, `staging`, `production`, `local`

### **Generated Configuration**
- **Directory**: `generated-config/`
- **Generated by**: `scripts/generate-service-config.py`
- **Files per environment**:
  - `service-config.js` - JavaScript/TypeScript configuration
  - `service_config.py` - Python configuration
  - `.env` - Environment variables

## 🔧 **Usage**

### **JavaScript/TypeScript**

```javascript
// Import the utility
import serviceURLManager from './utils/service-urls.js';

// Get service URLs
const apiURL = serviceURLManager.getServiceURL('api');
const frontendURL = serviceURLManager.getServiceURL('frontend', true); // with health endpoint

// Get API endpoints
const uploadEndpoint = serviceURLManager.getRelativeAPIEndpoint('files.upload');
const vmStatusEndpoint = serviceURLManager.getAPIEndpoint('vms.status', { vm_id: '123' });

// Get MinIO bucket names
const uploadsBucket = serviceURLManager.getMinioBucket('uploads');

// Get Elasticsearch indices
const logsIndex = serviceURLManager.getElasticsearchIndex('logs');
```

### **Python**

```python
# Import the utility
from utils.service_urls import get_service_url_manager, get_service_url, get_api_endpoint

# Get service URLs
api_url = get_service_url('api')
frontend_url = get_service_url('frontend', include_health=True)

# Get API endpoints
upload_endpoint = get_api_endpoint('files.upload')
vm_status_endpoint = get_api_endpoint('vms.status', {'vm_id': '123'})

# Using the manager directly
manager = get_service_url_manager()
uploads_bucket = manager.get_minio_bucket('uploads')
logs_index = manager.get_elasticsearch_index('logs')
```

### **Environment Variables**

```bash
# Source the generated .env file
source generated-config/development/.env

# Use environment variables
echo $API_URL
echo $FRONTEND_URL
echo $MINIO_BUCKET_UPLOADS
```

## 🌍 **Environment Detection**

The system automatically detects the environment based on:

1. **Environment Variables**: `TURDPARTY_ENV`, `NODE_ENV`, `ENVIRONMENT`
2. **Browser Location**: Domain-based detection for frontend
3. **Docker Environment**: Containerized environment detection
4. **Default**: Falls back to `development` for Traefik setup

## 🏗️ **Environment Configurations**

### **Development** (Traefik)
- **Domain**: `turdparty.localhost`
- **Protocol**: `http`
- **Services**: `api.turdparty.localhost`, `frontend.turdparty.localhost`, etc.

### **Staging**
- **Domain**: `staging.turdparty.dev`
- **Protocol**: `https`
- **Services**: `api.staging.turdparty.dev`, `app.staging.turdparty.dev`, etc.

### **Production**
- **Domain**: `turdparty.com`
- **Protocol**: `https`
- **Services**: `api.turdparty.com`, `app.turdparty.com`, etc.

### **Local** (Direct ports)
- **Domain**: `localhost`
- **Protocol**: `http`
- **Services**: `localhost:8000`, `localhost:3000`, etc.

## 🔄 **Regenerating Configuration**

When you update `config/service-urls.json`, regenerate the configuration files:

```bash
# Regenerate all environment configurations
python scripts/generate-service-config.py

# The script will update all files in generated-config/
```

## 📝 **API Endpoints**

The system includes predefined API endpoints:

### **Files**
- `files.upload` → `/api/v1/files/upload`
- `files.download` → `/api/v1/files/{file_id}/download`
- `files.metadata` → `/api/v1/files/{file_id}`
- `files.list` → `/api/v1/files`

### **VMs**
- `vms.create` → `/api/v1/vms/`
- `vms.status` → `/api/v1/vms/{vm_id}`
- `vms.list` → `/api/v1/vms`
- `vms.delete` → `/api/v1/vms/{vm_id}`
- `vms.inject` → `/api/v1/vms/{vm_id}/inject`
- `vms.execute` → `/api/v1/vms/{vm_id}/execute`
- `vms.logs` → `/api/v1/vms/{vm_id}/logs`

### **Health**
- `health.system` → `/health`
- `health.elasticsearch` → `/api/v1/health/elasticsearch`
- `health.minio` → `/api/v1/health/minio`
- `health.database` → `/api/v1/health/database`

### **Reporting**
- `reporting.binary_report` → `/api/v1/reports/binary/{file_uuid}`
- `reporting.installation_footprint` → `/api/v1/reports/binary/{file_uuid}/footprint`
- `reporting.runtime_behavior` → `/api/v1/reports/binary/{file_uuid}/runtime`

## 🗄️ **Storage Configuration**

### **MinIO Buckets**
- `uploads` → `turdparty-uploads`
- `processed` → `turdparty-processed`
- `reports` → `turdparty-reports`
- `backups` → `turdparty-backups`

### **Elasticsearch Indices**
- `logs` → `turdparty-logs-*`
- `metrics` → `turdparty-metrics-*`
- `vm_data` → `turdparty-vm-*`
- `file_metadata` → `turdparty-files-*`

## 🚀 **Best Practices**

1. **Always use relative URLs** for same-origin requests
2. **Use the centralized utilities** instead of hardcoding URLs
3. **Regenerate configuration** after updating `service-urls.json`
4. **Test in all environments** before deploying changes
5. **Use environment detection** rather than manual configuration

## 🔧 **Migration Guide**

To migrate existing hardcoded URLs:

1. **Identify hardcoded URLs** in your code
2. **Replace with utility calls**:
   ```javascript
   // Before
   const apiUrl = 'http://localhost:8000/api/v1';
   
   // After
   const apiUrl = serviceURLManager.getRelativeAPIEndpoint('health.system').replace('/health', '') + '/api/v1';
   ```
3. **Test in development environment**
4. **Verify Traefik routing works**
5. **Deploy to staging/production**

## 🎉 **Benefits**

- ✅ **Consistency**: Single source of truth for all URLs
- ✅ **Environment-aware**: Automatic environment detection
- ✅ **Traefik-compatible**: Works with reverse proxy setup
- ✅ **Type-safe**: Generated configurations with proper typing
- ✅ **Maintainable**: Easy to update and deploy changes
- ✅ **Flexible**: Supports multiple deployment scenarios
