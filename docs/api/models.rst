💩🎉TurdParty🎉💩 API Data Models
=====================================

This section documents all data models used in the 💩🎉TurdParty🎉💩 API, including request/response schemas, validation rules, and example payloads.

The API models are organized in the ``services.api.src.models`` package and provide comprehensive
data validation, serialization, and documentation for all API endpoints.

VM Management Models
--------------------

VMCreateRequest
~~~~~~~~~~~~~~~

Model for creating a new virtual machine.

.. code-block:: python

   class VMCreateRequest(BaseModel):
       name: str = Field(..., min_length=1, max_length=100)
       template: str = Field(..., description="VM template identifier")
       vm_type: Literal["docker", "vagrant"] = Field(..., description="Type of VM to create")
       memory_mb: int = Field(default=1024, ge=128, le=32768)
       cpus: int = Field(default=1, ge=1, le=16)
       domain: str = Field(..., pattern="^TurdParty$")
       environment: Optional[Dict[str, str]] = Field(default_factory=dict)
       volumes: Optional[List[VolumeMount]] = Field(default_factory=list)
       network_config: Optional[NetworkConfig] = None

**Field Descriptions:**

.. list-table::
   :widths: 20 20 60
   :header-rows: 1

   * - Field
     - Type
     - Description
   * - ``name``
     - string
     - Unique name for the VM (1-100 characters)
   * - ``template``
     - string
     - Template identifier (e.g., "ubuntu:20.04")
   * - ``vm_type``
     - enum
     - VM type: "docker" or "vagrant"
   * - ``memory_mb``
     - integer
     - Memory allocation in MB (128-32768)
   * - ``cpus``
     - integer
     - CPU core count (1-16)
   * - ``domain``
     - string
     - Must be "TurdParty"
   * - ``environment``
     - object
     - Environment variables as key-value pairs
   * - ``volumes``
     - array
     - Volume mount configurations
   * - ``network_config``
     - object
     - Network configuration settings

**Example:**

.. code-block:: json

   {
       "name": "malware-analysis-vm",
       "template": "ubuntu:20.04",
       "vm_type": "docker",
       "memory_mb": 2048,
       "cpus": 4,
       "domain": "TurdParty",
       "environment": {
           "ANALYSIS_MODE": "dynamic",
           "TIMEOUT": "300"
       },
       "volumes": [
           {
               "host_path": "/tmp/samples",
               "container_path": "/analysis/samples",
               "mode": "ro"
           }
       ]
   }

VMResponse
~~~~~~~~~~

Response model for VM operations.

.. code-block:: python

   class VMResponse(BaseModel):
       vm_id: str = Field(..., description="Unique VM identifier")
       name: str
       status: VMStatus
       vm_type: str
       template: str
       memory_mb: int
       cpus: int
       created_at: datetime
       updated_at: datetime
       domain: str
       container_id: Optional[str] = None
       network_info: Optional[NetworkInfo] = None
       metrics: Optional[VMMetrics] = None

**Status Values:**

.. list-table::
   :widths: 20 80
   :header-rows: 1

   * - Status
     - Description
   * - ``creating``
     - VM is being created
   * - ``starting``
     - VM is starting up
   * - ``running``
     - VM is running normally
   * - ``stopping``
     - VM is shutting down
   * - ``stopped``
     - VM is stopped
   * - ``error``
     - VM encountered an error
   * - ``unknown``
     - VM status cannot be determined

VolumeMount
~~~~~~~~~~~

Configuration for mounting volumes in VMs.

.. code-block:: python

   class VolumeMount(BaseModel):
       host_path: str = Field(..., description="Path on host system")
       container_path: str = Field(..., description="Path inside VM")
       mode: Literal["ro", "rw"] = Field(default="rw", description="Mount mode")

NetworkConfig
~~~~~~~~~~~~~

Network configuration for VMs.

.. code-block:: python

   class NetworkConfig(BaseModel):
       network_mode: Literal["bridge", "host", "none", "isolated"] = "bridge"
       port_mappings: Optional[List[PortMapping]] = None
       dns_servers: Optional[List[str]] = None
       enable_internet: bool = True

PortMapping
~~~~~~~~~~~

Port mapping configuration.

.. code-block:: python

   class PortMapping(BaseModel):
       host_port: int = Field(..., ge=1, le=65535)
       container_port: int = Field(..., ge=1, le=65535)
       protocol: Literal["tcp", "udp"] = "tcp"

WebSocket Models
----------------

MetricsData
~~~~~~~~~~~

Real-time VM metrics data.

.. code-block:: python

   class MetricsData(BaseModel):
       timestamp: datetime
       vm_id: str
       cpu_percent: float = Field(..., ge=0, le=100)
       memory_percent: float = Field(..., ge=0, le=100)
       disk_io: DiskIOMetrics
       network_io: NetworkIOMetrics
       processes: List[ProcessInfo]

DiskIOMetrics
~~~~~~~~~~~~~

Disk I/O performance metrics.

.. code-block:: python

   class DiskIOMetrics(BaseModel):
       read_bytes: int = Field(..., ge=0)
       write_bytes: int = Field(..., ge=0)
       read_ops: int = Field(..., ge=0)
       write_ops: int = Field(..., ge=0)

NetworkIOMetrics
~~~~~~~~~~~~~~~~

Network I/O performance metrics.

.. code-block:: python

   class NetworkIOMetrics(BaseModel):
       bytes_sent: int = Field(..., ge=0)
       bytes_received: int = Field(..., ge=0)
       packets_sent: int = Field(..., ge=0)
       packets_received: int = Field(..., ge=0)

ProcessInfo
~~~~~~~~~~~

Information about running processes.

.. code-block:: python

   class ProcessInfo(BaseModel):
       pid: int = Field(..., ge=1)
       name: str
       cpu_percent: float = Field(..., ge=0)
       memory_mb: float = Field(..., ge=0)
       status: str
       create_time: datetime

CommandRequest
~~~~~~~~~~~~~~

WebSocket command execution request.

.. code-block:: python

   class CommandRequest(BaseModel):
       command: str = Field(..., min_length=1)
       working_directory: Optional[str] = None
       environment: Optional[Dict[str, str]] = None
       timeout: Optional[int] = Field(default=30, ge=1, le=3600)

CommandResponse
~~~~~~~~~~~~~~~

WebSocket command execution response.

.. code-block:: python

   class CommandResponse(BaseModel):
       type: Literal["output", "error", "complete"]
       output: Optional[str] = None
       exit_code: Optional[int] = None
       is_complete: bool = False
       timestamp: datetime

File Operation Models
---------------------

FileUploadRequest
~~~~~~~~~~~~~~~~~

File upload initiation request.

.. code-block:: python

   class FileUploadRequest(BaseModel):
       filename: str = Field(..., min_length=1)
       size: int = Field(..., ge=0)
       destination_path: str = Field(..., min_length=1)
       overwrite: bool = False
       permissions: Optional[str] = Field(default="644", pattern=r"^[0-7]{3}$")

FileUploadProgress
~~~~~~~~~~~~~~~~~~

File upload progress notification.

.. code-block:: python

   class FileUploadProgress(BaseModel):
       type: Literal["progress", "complete", "error"]
       bytes_uploaded: int = Field(..., ge=0)
       total_bytes: int = Field(..., ge=0)
       percentage: float = Field(..., ge=0, le=100)
       error_message: Optional[str] = None

FileWatchEvent
~~~~~~~~~~~~~~

File system watch event.

.. code-block:: python

   class FileWatchEvent(BaseModel):
       type: Literal["created", "modified", "deleted", "moved"]
       path: str
       timestamp: datetime
       size: Optional[int] = None
       is_directory: bool = False

Error Models
------------

APIError
~~~~~~~~

Standard API error response.

.. code-block:: python

   class APIError(BaseModel):
       error: ErrorDetail

ErrorDetail
~~~~~~~~~~~

Detailed error information.

.. code-block:: python

   class ErrorDetail(BaseModel):
       code: str = Field(..., description="Error code identifier")
       message: str = Field(..., description="Human-readable error message")
       details: Optional[Dict[str, Any]] = None
       timestamp: datetime = Field(default_factory=datetime.utcnow)
       request_id: Optional[str] = None

**Common Error Codes:**

.. list-table::
   :widths: 25 75
   :header-rows: 1

   * - Code
     - Description
   * - ``VM_NOT_FOUND``
     - Specified VM does not exist
   * - ``VM_ALREADY_EXISTS``
     - VM with the same name already exists
   * - ``INSUFFICIENT_RESOURCES``
     - Not enough system resources available
   * - ``INVALID_TEMPLATE``
     - Specified template is not available
   * - ``VALIDATION_ERROR``
     - Request data validation failed
   * - ``PERMISSION_DENIED``
     - Insufficient permissions for operation
   * - ``INTERNAL_ERROR``
     - Unexpected server error occurred

ValidationError
~~~~~~~~~~~~~~~

Validation error with field-specific details.

.. code-block:: python

   class ValidationError(BaseModel):
       error: str = "validation_error"
       message: str = "Request validation failed"
       details: List[ValidationErrorDetail]

ValidationErrorDetail
~~~~~~~~~~~~~~~~~~~~~

Individual field validation error.

.. code-block:: python

   class ValidationErrorDetail(BaseModel):
       field: str = Field(..., description="Field name that failed validation")
       message: str = Field(..., description="Validation error message")
       value: Any = Field(..., description="Invalid value provided")

System Models
-------------

HealthResponse
~~~~~~~~~~~~~~

System health check response.

.. code-block:: python

   class HealthResponse(BaseModel):
       status: Literal["healthy", "degraded", "unhealthy"]
       timestamp: datetime
       version: str
       uptime: int = Field(..., description="Uptime in seconds")
       services: Dict[str, ServiceHealth]

ServiceHealth
~~~~~~~~~~~~~

Individual service health status.

.. code-block:: python

   class ServiceHealth(BaseModel):
       status: Literal["healthy", "degraded", "unhealthy"]
       response_time_ms: Optional[float] = None
       last_check: datetime
       error_message: Optional[str] = None

SystemStatus
~~~~~~~~~~~~

Comprehensive system status information.

.. code-block:: python

   class SystemStatus(BaseModel):
       system: SystemResources
       vms: VMStatistics
       docker: DockerInfo
       vagrant: VagrantInfo

SystemResources
~~~~~~~~~~~~~~~

System resource information.

.. code-block:: python

   class SystemResources(BaseModel):
       cpu_count: int
       memory_total_gb: float
       memory_available_gb: float
       disk_total_gb: float
       disk_available_gb: float
       load_average: List[float]

VMStatistics
~~~~~~~~~~~~

VM usage statistics.

.. code-block:: python

   class VMStatistics(BaseModel):
       total: int
       running: int
       stopped: int
       error: int
       creating: int

Template Models
---------------

VMTemplate
~~~~~~~~~~

VM template information.

.. code-block:: python

   class VMTemplate(BaseModel):
       name: str
       type: Literal["docker", "vagrant"]
       description: str
       size_mb: int
       tags: List[str]
       tools: List[str]
       default_config: DefaultVMConfig

DefaultVMConfig
~~~~~~~~~~~~~~~

Default configuration for VM templates.

.. code-block:: python

   class DefaultVMConfig(BaseModel):
       memory_mb: int
       cpus: int
       disk_gb: int
       network_mode: str = "bridge"

Usage Examples
--------------

**Creating a VM with Full Configuration:**

.. code-block:: json

   {
       "name": "advanced-analysis-vm",
       "template": "kali:latest",
       "vm_type": "docker",
       "memory_mb": 4096,
       "cpus": 4,
       "domain": "TurdParty",
       "environment": {
           "DISPLAY": ":0",
           "ANALYSIS_TIMEOUT": "1800",
           "ENABLE_X11": "true"
       },
       "volumes": [
           {
               "host_path": "/samples",
               "container_path": "/analysis/samples",
               "mode": "ro"
           },
           {
               "host_path": "/results",
               "container_path": "/analysis/results",
               "mode": "rw"
           }
       ],
       "network_config": {
           "network_mode": "isolated",
           "enable_internet": false,
           "dns_servers": ["*******", "*******"]
       }
   }

**WebSocket Command Execution:**

.. code-block:: json

   {
       "command": "python3 /analysis/malware_scanner.py --input /samples/malware.exe",
       "working_directory": "/analysis",
       "environment": {
           "PYTHONPATH": "/analysis/lib",
           "ANALYSIS_MODE": "deep"
       },
       "timeout": 600
   }

**File Upload with Progress Tracking:**

.. code-block:: json

   {
       "filename": "suspicious_file.exe",
       "size": 2048576,
       "destination_path": "/analysis/samples/suspicious_file.exe",
       "overwrite": true,
       "permissions": "644"
   }
