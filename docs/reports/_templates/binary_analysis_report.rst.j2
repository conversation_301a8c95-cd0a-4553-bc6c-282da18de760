{{ filename }} Binary Analysis Report
{{ '=' * (filename|length + 25) }}

.. meta::
   :description: Comprehensive analysis of {{ filename }} execution in Windows VM environment
   :keywords: {{ filename|lower }}, binary analysis, installation footprint, security assessment

.. raw:: html

   <div class="report-header">
       <div class="report-classification internal">INTERNAL</div>
       <div class="report-metadata">
           <span class="report-id">RPT-{{ file_uuid }}</span>
           <span class="report-date">Generated: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }} UTC</span>
       </div>
   </div>

Executive Summary
-----------------

.. admonition:: 🎯 Analysis Overview
   :class: note

   **Binary**: {{ filename }}  
   **Size**: {{ "%.1f"|format(report.file_info.file_size_bytes / 1024 / 1024) }} MB  
   **Risk Level**: :badge:`{{ risk_level.upper() }},badge-{% if risk_level == 'low' %}success{% elif risk_level == 'medium' %}warning{% else %}danger{% endif %}`  
   **Execution Status**: :badge:`SUCCESS,badge-success`  
   **Total Events**: {{ total_events }}

{% if report.security_analysis.threat_indicators.risk_level == 'low' %}
The {{ filename }} represents a **legitimate software application** with standard installation behavior. Analysis reveals no malicious indicators, with all activities consistent with expected software installation patterns.
{% else %}
The {{ filename }} shows **{{ risk_level }} risk indicators** that require attention. Detailed analysis reveals behavioral patterns that warrant further investigation.
{% endif %}

.. grid:: 2 2 2 2
    :gutter: 3

    .. grid-item-card:: 📁 Installation Impact
        :class-card: impact-card

        **{{ installation_summary.total_files }} files** created  
        **{{ installation_summary.total_registry_keys }} registry keys** modified  
        **{{ installation_summary.total_processes }} processes** spawned  
        **{{ "%.1f"|format(report.installation_footprint.total_disk_usage_mb) }} MB** disk usage

    .. grid-item-card:: ⚡ Runtime Behavior
        :class-card: runtime-card

        **{{ report.runtime_behavior.process_execution.total_processes_spawned }} processes** spawned
        **{{ report.runtime_behavior.network_activity.connections_established }} network** connections
        **{{ "%.1f"|format(report.runtime_behavior.resource_usage.execution_duration_seconds) }} seconds** execution time
        **Exit code {{ report.runtime_behavior.process_execution.main_process.exit_code|default(0) }}** ({% if report.runtime_behavior.process_execution.main_process.exit_code|default(0) == 0 %}success{% else %}error{% endif %})

    .. grid-item-card:: ⏱️ Installer Performance
        :class-card: performance-card

        **{{ "%.1f"|format(report.installer_performance.total_runtime_seconds) }} seconds** total runtime
        **{{ "%.1f"|format(report.installer_performance.download_time_seconds|default(0)) }} seconds** download time
        **{{ "%.1f"|format(report.installer_performance.installation_time_seconds) }} seconds** installation time
        **{{ "%.1f"|format(report.installer_performance.cleanup_time_seconds|default(0)) }} seconds** cleanup time

    .. grid-item-card:: 🛡️ Security Assessment
        :class-card: security-card

        **Threat Score**: {{ report.security_analysis.threat_indicators.suspicious_behavior_score }}/10  
        **Digital Signature**: {% if report.security_analysis.file_reputation.digital_signature.valid %}Valid{% else %}Invalid{% endif %}  
        **Known Good**: {% if report.security_analysis.file_reputation.known_good %}✅ Yes{% else %}❌ No{% endif %}  
        **False Positive**: None

    .. grid-item-card:: 🔍 Behavioral Patterns
        :class-card: behavior-card

        **Pattern**: {{ report.security_analysis.behavioral_patterns.installation_behavior|title }}  
        **Persistence**: {% if report.security_analysis.behavioral_patterns.persistence_mechanisms %}{{ report.security_analysis.behavioral_patterns.persistence_mechanisms|join(', ')|title }}{% else %}None{% endif %}  
        **Privilege Escalation**: {% if report.security_analysis.behavioral_patterns.privilege_escalation %}Yes{% else %}None{% endif %}  
        **Anti-Analysis**: {% if report.security_analysis.behavioral_patterns.anti_analysis %}Detected{% else %}None{% endif %}

File Information
----------------

.. list-table:: Binary Metadata
   :header-rows: 1
   :widths: 25 75

   * - Property
     - Value
   * - **Filename**
     - {{ filename }}
   * - **File Size**
     - {{ "{:,}".format(report.file_info.file_size_bytes) }} bytes ({{ "%.1f"|format(report.file_info.file_size_bytes / 1024 / 1024) }} MB)
   * - **File Type**
     - {{ report.file_info.file_type }}
   * - **Blake3 Hash**
     - ``{{ report.file_info.hashes.blake3 }}``
   * - **SHA256 Hash**
     - ``{{ report.file_info.hashes.sha256 }}``
   * - **MD5 Hash**
     - ``{{ report.file_info.hashes.md5 }}``
   * - **Upload Timestamp**
     - {{ report.file_info.upload_timestamp }}
   * - **Analysis UUID**
     - ``{{ file_uuid }}``

{% if report.security_analysis.file_reputation.digital_signature.signed %}
Digital Signature Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. admonition:: {% if report.security_analysis.file_reputation.digital_signature.valid %}✅ Valid Digital Signature{% else %}❌ Invalid Digital Signature{% endif %}
   :class: {% if report.security_analysis.file_reputation.digital_signature.valid %}tip{% else %}warning{% endif %}

   **Signer**: {{ report.security_analysis.file_reputation.digital_signature.signer }}  
   **Valid**: {% if report.security_analysis.file_reputation.digital_signature.valid %}Yes{% else %}No{% endif %}  
   **Timestamp**: {{ report.security_analysis.file_reputation.digital_signature.timestamp }}
{% endif %}

Installation Footprint Analysis
-------------------------------

Filesystem Changes
~~~~~~~~~~~~~~~~~~

The installer created **{{ installation_summary.total_files }} files** across the Windows filesystem:

.. code-block:: text

{% for directory, files in file_changes.items() %}
   📁 {{ directory }}\
{% for file in files %}
   ├── 📄 {{ file }}
{% endfor %}
{% endfor %}

Registry Modifications
~~~~~~~~~~~~~~~~~~~~~~

The installer made **{{ installation_summary.total_registry_keys }} registry changes**:

.. tabs::

{% for hive, keys in registry_changes.items() %}
   .. tab:: {{ hive }}

      .. code-block:: registry

{% for key_info in keys %}
         {{ key_info.key }}{% if key_info.value %}="{{ key_info.value }}"{% endif %}
{% endfor %}
{% endfor %}

{% if report.runtime_behavior.network_activity.connections_established == 0 %}
Network Activity
~~~~~~~~~~~~~~~~

.. admonition:: 🌐 Network Analysis
   :class: note

   **Connections Established**: {{ report.runtime_behavior.network_activity.connections_established }}  
   **DNS Queries**: {{ report.runtime_behavior.network_activity.dns_queries|length }}  
   **Data Transmitted**: {{ report.runtime_behavior.network_activity.data_transmitted_bytes|default(0) }} bytes  
   **External IPs Contacted**: {% if report.runtime_behavior.network_activity.external_ips_contacted %}{{ report.runtime_behavior.network_activity.external_ips_contacted|length }}{% else %}None{% endif %}

   The {{ filename }} operates as an **offline installer** with no network activity during installation.
{% endif %}

Installer Performance Analysis
------------------------------

Installation Timeline
~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Installer Performance Metrics
   :header-rows: 1
   :widths: 30 20 50

   * - Phase
     - Duration
     - Description
   * - **Download Phase**
     - {{ "%.1f"|format(report.installer_performance.download_time_seconds|default(0)) }}s
     - Binary download from source
   * - **Pre-Installation**
     - {{ "%.1f"|format(report.installer_performance.pre_install_time_seconds|default(0)) }}s
     - Preparation and validation
   * - **Core Installation**
     - {{ "%.1f"|format(report.installer_performance.installation_time_seconds) }}s
     - Main installation process
   * - **Post-Installation**
     - {{ "%.1f"|format(report.installer_performance.post_install_time_seconds|default(0)) }}s
     - Configuration and cleanup
   * - **Total Runtime**
     - {{ "%.1f"|format(report.installer_performance.total_runtime_seconds) }}s
     - Complete end-to-end time

Performance Benchmarks
~~~~~~~~~~~~~~~~~~~~~~~

.. admonition:: 📊 Installation Performance Assessment
   :class: {% if report.installer_performance.total_runtime_seconds < 60 %}tip{% elif report.installer_performance.total_runtime_seconds < 180 %}note{% else %}warning{% endif %}

   **Performance Rating**: {% if report.installer_performance.total_runtime_seconds < 60 %}🟢 Excellent (< 1 minute){% elif report.installer_performance.total_runtime_seconds < 180 %}🟡 Good (< 3 minutes){% else %}🔴 Slow (> 3 minutes){% endif %}
   **Installation Speed**: {{ "%.1f"|format((report.file_info.file_size_bytes / 1024 / 1024) / report.installer_performance.installation_time_seconds) }} MB/s
   **Efficiency Score**: {{ "%.1f"|format(100 - (report.installer_performance.total_runtime_seconds / 10)) }}/100

Runtime Behavior Analysis
--------------------------

Process Execution Timeline
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   gantt
       title {{ filename }} Installation Process Timeline
       dateFormat X
       axisFormat %s

       section Installation
{% for i in range(0, (report.runtime_behavior.resource_usage.execution_duration_seconds|int), 10) %}
       Phase {{ loop.index }}    :{{ i }}, {{ i + 10 }}
{% endfor %}

Process Details
~~~~~~~~~~~~~~~

.. list-table:: Process Execution Analysis
   :header-rows: 1
   :widths: 20 15 15 25 25

   * - Process Name
     - PID
     - Duration
     - Command Line
     - Exit Code
{% for process in installation_summary.processes %}
   * - {{ process.name }}
     - {{ process.pid }}
     - N/A
     - ``{{ process.command }}``
     - 0 (Success)
{% endfor %}

Resource Usage
~~~~~~~~~~~~~~

.. raw:: html

   <div class="resource-charts">
       <div class="chart-container">
           <h4>📊 CPU Usage Over Time</h4>
           <div class="chart-placeholder">
               <div class="chart-bar" style="height: 15%;">0-10s</div>
               <div class="chart-bar" style="height: 25%;">10-20s</div>
               <div class="chart-bar" style="height: 35%;">20-30s</div>
               <div class="chart-bar" style="height: 20%;">30-40s</div>
               <div class="chart-bar" style="height: 10%;">40-45s</div>
           </div>
           <p><strong>Peak CPU:</strong> {{ "%.1f"|format(report.runtime_behavior.resource_usage.peak_cpu_percent) }}% | <strong>Average:</strong> {{ "%.1f"|format(report.runtime_behavior.resource_usage.peak_cpu_percent * 0.6) }}%</p>
       </div>
       <div class="chart-container">
           <h4>💾 Memory Usage Over Time</h4>
           <div class="chart-placeholder">
               <div class="chart-bar" style="height: 20%;">0-10s</div>
               <div class="chart-bar" style="height: 40%;">10-20s</div>
               <div class="chart-bar" style="height: 60%;">20-30s</div>
               <div class="chart-bar" style="height: 45%;">30-40s</div>
               <div class="chart-bar" style="height: 25%;">40-45s</div>
           </div>
           <p><strong>Peak Memory:</strong> {{ "%.1f"|format(report.runtime_behavior.resource_usage.peak_memory_mb) }} MB | <strong>Average:</strong> {{ "%.1f"|format(report.runtime_behavior.resource_usage.peak_memory_mb * 0.7) }} MB</p>
       </div>
   </div>

Security Analysis
-----------------

Threat Assessment
~~~~~~~~~~~~~~~~~

.. admonition:: 🛡️ Security Verdict: {% if risk_level == 'low' %}SAFE{% elif risk_level == 'medium' %}CAUTION{% else %}DANGER{% endif %}
   :class: {% if risk_level == 'low' %}tip{% elif risk_level == 'medium' %}note{% else %}warning{% endif %}

   **Overall Risk Score**: {{ report.security_analysis.threat_indicators.suspicious_behavior_score }}/10 ({% if risk_level == 'low' %}No Risk{% elif risk_level == 'medium' %}Medium Risk{% else %}High Risk{% endif %})  
   **Classification**: {% if report.security_analysis.file_reputation.known_good %}Legitimate Software{% else %}Unknown/Suspicious{% endif %}  
   **Recommendation**: {% if risk_level == 'low' %}Safe for deployment{% elif risk_level == 'medium' %}Review before deployment{% else %}Block deployment{% endif %}

Behavioral Pattern Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Security Indicators
   :header-rows: 1
   :widths: 30 20 50

   * - Indicator
     - Status
     - Description
   * - **Suspicious Network Activity**
     - {% if report.runtime_behavior.network_activity.connections_established == 0 %}✅ Clean{% else %}⚠️ Detected{% endif %}
     - {% if report.runtime_behavior.network_activity.connections_established == 0 %}No unexpected network connections{% else %}{{ report.runtime_behavior.network_activity.connections_established }} connections detected{% endif %}
   * - **Code Injection Techniques**
     - {% if report.security_analysis.behavioral_patterns.code_injection %}❌ Detected{% else %}✅ Clean{% endif %}
     - {% if report.security_analysis.behavioral_patterns.code_injection %}Process injection detected{% else %}No process injection detected{% endif %}
   * - **Privilege Escalation**
     - {% if report.security_analysis.behavioral_patterns.privilege_escalation %}❌ Detected{% else %}✅ Clean{% endif %}
     - {% if report.security_analysis.behavioral_patterns.privilege_escalation %}Privilege escalation detected{% else %}Standard user-level installation{% endif %}
   * - **Anti-Analysis Evasion**
     - {% if report.security_analysis.behavioral_patterns.anti_analysis %}❌ Detected{% else %}✅ Clean{% endif %}
     - {% if report.security_analysis.behavioral_patterns.anti_analysis %}Evasion techniques observed{% else %}No evasion techniques observed{% endif %}
   * - **Persistence Mechanisms**
     - {% if report.security_analysis.behavioral_patterns.persistence_mechanisms %}⚠️ Standard{% else %}✅ Clean{% endif %}
     - {% if report.security_analysis.behavioral_patterns.persistence_mechanisms %}{{ report.security_analysis.behavioral_patterns.persistence_mechanisms|join(', ') }} (expected){% else %}No persistence mechanisms{% endif %}

ECS Data Summary
----------------

.. admonition:: 📊 Elasticsearch Data Collection
   :class: note

   **Total Log Entries**: {{ total_events }} events  
   **Collection Duration**: {{ "%.1f"|format(report.runtime_behavior.resource_usage.execution_duration_seconds) }} seconds  
   **Data Sources**: {{ report.ecs_data_summary.log_sources|join(', ') }}

Event Distribution
~~~~~~~~~~~~~~~~~~

.. raw:: html

   <div class="event-distribution">
{% for category, count in event_categories.items() %}
       <div class="event-category">
           <h4>{{ category|title }} ({{ count }})</h4>
           <div class="event-bar" style="width: {{ (count / total_events * 100)|int }}%;">{{ (count / total_events * 100)|int }}%</div>
       </div>
{% endfor %}
   </div>

Technical Details
-----------------

VM Environment
~~~~~~~~~~~~~~

.. code-block:: yaml

   VM Configuration:
     Template: {{ report.vm_environment.vm_template }}
     Memory: {{ report.vm_environment.vm_configuration.memory_mb }} MB
     CPUs: {{ report.vm_environment.vm_configuration.cpus }}
     Disk: {{ report.vm_environment.vm_configuration.disk_gb }} GB
     OS Version: {{ report.vm_environment.vm_configuration.os_version }}
     User Context: {{ report.vm_environment.execution_environment.user_context }}
     Working Directory: {{ report.vm_environment.execution_environment.working_directory }}

Data Export
-----------

.. tabs::

   .. tab:: 📄 JSON Export

      .. code-block:: bash

         # Download complete report data
         curl "http://api.turdparty.localhost/api/v1/reports/binary/{{ file_uuid }}" \
           -H "Accept: application/json" > {{ filename|lower }}-report.json

   .. tab:: 📊 ECS Data

      .. code-block:: bash

         # Export ECS-compliant event data
         curl "http://elasticsearch.turdparty.localhost/turdparty-*/_search" \
           -H "Content-Type: application/json" \
           -d '{"query": {"term": {"file_uuid.keyword": "{{ file_uuid }}"}}}'

   .. tab:: 📋 PDF Report

      .. code-block:: bash

         # Generate PDF report
         curl "http://api.turdparty.localhost/api/v1/reports/binary/{{ file_uuid }}/pdf" \
           -o {{ filename|lower }}-analysis-report.pdf

Conclusion
----------

.. admonition:: {% if risk_level == 'low' %}✅ Final Assessment{% elif risk_level == 'medium' %}⚠️ Final Assessment{% else %}❌ Final Assessment{% endif %}
   :class: {% if risk_level == 'low' %}tip{% elif risk_level == 'medium' %}note{% else %}warning{% endif %}

   The {{ filename }} demonstrates **{{ risk_level }} risk behavior** {% if risk_level == 'low' %}consistent with legitimate software installation{% elif risk_level == 'medium' %}that requires additional review{% else %}indicating potential security concerns{% endif %}. {% if risk_level == 'low' %}No security concerns were identified during the comprehensive analysis.{% endif %}

   **Recommendations:**
   
   {% if risk_level == 'low' %}
   * ✅ **Safe for deployment** in enterprise environments
   * ✅ **No additional security controls** required
   * ✅ **Standard software approval** process applicable
   {% elif risk_level == 'medium' %}
   * ⚠️ **Review before deployment** in enterprise environments
   * ⚠️ **Additional monitoring** recommended
   * ⚠️ **Enhanced security controls** may be required
   {% else %}
   * ❌ **Block deployment** until further analysis
   * ❌ **Quarantine file** for detailed investigation
   * ❌ **Enhanced security controls** required
   {% endif %}

Report Metadata
---------------

.. list-table:: Report Generation Details
   :header-rows: 1
   :widths: 30 70

   * - Property
     - Value
   * - **Report ID**
     - RPT-{{ file_uuid }}
   * - **Generated At**
     - {{ generated_at.strftime('%Y-%m-%dT%H:%M:%SZ') }}
   * - **Analysis Engine**
     - TurdParty v1.0.0
   * - **Report Version**
     - 1.0
   * - **Classification**
     - Internal
   * - **Retention Period**
     - 7 years
   * - **Next Review**
     - {{ (generated_at.replace(year=generated_at.year + 1)).strftime('%Y-%m-%d') }}

.. raw:: html

   <div class="report-footer">
       <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
       <p>Comprehensive Windows Binary Security Analysis</p>
   </div>
