Notepadpp Binary Analysis Report
==================================

.. meta::
   :description: Comprehensive analysis of npp.8.5.8.Installer.x64.exe execution in Windows VM environment
   :keywords: notepadpp, binary analysis, installation footprint, security assessment

.. raw:: html

   <div class="report-header">
       <div class="report-classification internal">INTERNAL</div>
       <div class="report-metadata">
           <span class="report-id">RPT-fd885114-5cbf-4a64-86ea-0ccf0918664d</span>
           <span class="report-date">Generated: 2025-06-12 11:52:13 UTC</span>
       </div>
   </div>

Executive Summary
-----------------

.. admonition:: 🎯 Analysis Overview
   :class: note

   **Binary**: npp.8.5.8.Installer.x64.exe  
   **Size**: 4.6 MB  
   **Risk Level**: :badge:`LOW,badge-success`  
   **Execution Status**: :badge:`SUCCESS,badge-success`  
   **Total Events**: 28

The notepadpp represents a **legitimate software application** with standard installation behavior. Analysis reveals no malicious indicators, with all activities consistent with expected software installation patterns.

.. grid:: 2 2 2 2
    :gutter: 3

    .. grid-item-card:: 📁 Installation Impact
        :class-card: impact-card

        **18 files** created  
        **8 registry keys** modified  
        **2 processes** spawned  
        **22.9 MB** disk usage

    .. grid-item-card:: ⚡ Runtime Behavior
        :class-card: runtime-card

        **2 processes** spawned  
        **0 network** connections  
        **54.0 seconds** execution time  
        **Exit code 0** (success)

    .. grid-item-card:: 🛡️ Security Assessment
        :class-card: security-card

        **Threat Score**: 0/10  
        **Digital Signature**: Valid  
        **Known Good**: ✅ Yes  
        **False Positive**: None

    .. grid-item-card:: 🔍 Behavioral Patterns
        :class-card: behavior-card

        **Pattern**: Standard Installer  
        **Persistence**: Registry Entries  
        **Privilege Escalation**: None  
        **Anti-Analysis**: None

File Information
----------------

.. list-table:: Binary Metadata
   :header-rows: 1
   :widths: 25 75

   * - Property
     - Value
   * - **Filename**
     - npp.8.5.8.Installer.x64.exe
   * - **File Size**
     - 4,796,432 bytes (4.6 MB)
   * - **File Type**
     - PE32+ executable (GUI) x86-64, for MS Windows
   * - **Blake3 Hash**
     - ``56faf67075acaf186801d5e0a6690b6b5d952e94e13e13991a24844c0b0f3392``
   * - **SHA256 Hash**
     - ``ae8cbe56d5824e71069db350cb5235232ce665ecc64a2261930eb7a0c0d58173``
   * - **MD5 Hash**
     - ``b3bd5b49dc41b07a311ca3ee8ce2ce2d``
   * - **Upload Timestamp**
     - 2025-06-12T09:51:53.778778Z
   * - **Analysis UUID**
     - ``fd885114-5cbf-4a64-86ea-0ccf0918664d``

Installation Footprint Analysis
-------------------------------

Filesystem Changes
~~~~~~~~~~~~~~~~~~

The installer created **18 files** across the Windows filesystem:

.. code-block:: text

   📁 C:\Program Files\Notepadpp\
   ├── 📄 notepadpp.exe
   ├── 📄 config.json
   ├── 📄 file_3.dll
   ├── 📄 file_4.dll
   ├── 📄 file_5.dll
   ├── 📄 file_6.dll
   ├── 📄 file_7.dll
   ├── 📄 file_8.dll
   ├── 📄 file_9.dll
   ├── 📄 file_10.dll
   ├── 📄 file_11.dll
   ├── 📄 file_12.dll
   ├── 📄 file_13.dll
   ├── 📄 file_14.dll
   ├── 📄 file_15.dll
   ├── 📄 file_16.dll
   ├── 📄 file_17.dll

   📁 C:\Users\<USER>\Desktop\
   ├── 📄 Notepadpp.lnk


Registry Modifications
~~~~~~~~~~~~~~~~~~~~~~

The installer made **8 registry changes**:

.. code-block:: registry

   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp="notepadpp_value_0"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_1="notepadpp_value_1"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_2="notepadpp_value_2"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_3="notepadpp_value_3"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_4="notepadpp_value_4"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_5="notepadpp_value_5"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_6="notepadpp_value_6"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepadpp\key_7="notepadpp_value_7"

Runtime Behavior Analysis
--------------------------

Process Execution Details
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Process Execution Analysis
   :header-rows: 1
   :widths: 30 15 55

   * - Process Name
     - PID
     - Command Line
   * - npp.8.6.Installer.x64.exe
     - 3000
     - ``npp.8.6.Installer.x64.exe /S``
   * - notepadpp.exe
     - 3001
     - ``notepadpp.exe /S``


ECS Data Summary
----------------

.. admonition:: 📊 Elasticsearch Data Collection
   :class: note

   **Total Log Entries**: 28 events  
   **Collection Duration**: 54.0 seconds  
   **Data Sources**: vm-agent, file-monitor, process-monitor, registry-monitor

Event Distribution
~~~~~~~~~~~~~~~~~~

.. raw:: html

   <div class="event-distribution">
       <div class="event-category">
           <h4>📁 File Events (18)</h4>
           <div class="event-bar" style="width: 64%;">64%</div>
       </div>
       <div class="event-category">
           <h4>🔑 Registry Events (8)</h4>
           <div class="event-bar" style="width: 29%;">29%</div>
       </div>
       <div class="event-category">
           <h4>🔄 Process Events (2)</h4>
           <div class="event-bar" style="width: 7%;">7%</div>
       </div>
   </div>

Data Export
-----------

.. tabs::

   .. tab:: 📄 JSON Export

      .. code-block:: bash

         # Download complete report data
         curl "http://api.turdparty.localhost/api/v1/reports/binary/fd885114-5cbf-4a64-86ea-0ccf0918664d" \
           -H "Accept: application/json" > notepadpp-report.json

   .. tab:: 📊 ECS Data

      .. code-block:: bash

         # Export ECS-compliant event data
         curl "http://elasticsearch.turdparty.localhost/turdparty-*/_search" \
           -H "Content-Type: application/json" \
           -d '{"query": {"term": {"file_uuid.keyword": "fd885114-5cbf-4a64-86ea-0ccf0918664d"}}}'

Conclusion
----------

.. admonition:: ✅ Final Assessment
   :class: tip

   The notepadpp demonstrates **standard, benign behavior** consistent with legitimate software installation. No security concerns were identified during the comprehensive analysis.

   **Recommendations:**
   
   * ✅ **Safe for deployment** in enterprise environments
   * ✅ **No additional security controls** required
   * ✅ **Standard software approval** process applicable

Report Metadata
---------------

.. list-table:: Report Generation Details
   :header-rows: 1
   :widths: 30 70

   * - Property
     - Value
   * - **Report ID**
     - RPT-fd885114-5cbf-4a64-86ea-0ccf0918664d
   * - **Generated At**
     - 2025-06-12T11:52:13Z
   * - **Analysis Engine**
     - TurdParty v1.0.0
   * - **Report Version**
     - 1.0
   * - **Classification**
     - Internal

.. raw:: html

   <div class="report-footer">
       <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
       <p>Comprehensive Windows Binary Security Analysis</p>
   </div>
