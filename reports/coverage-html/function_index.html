<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">31.48%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:30 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066___init___py.html">services/api/src/__init__.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t41">services/api/src/main.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t41"><data value='lifespan'>lifespan</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t92">services/api/src/main.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t92"><data value='create_application'>create_application</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>1</td>
                <td class="right" data-ratio="18 20">90.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t134">services/api/src/main.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html#t134"><data value='global_exception_handler'>create_application.global_exception_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html">services/api/src/main.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78___init___py.html">services/api/src/models/__init__.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t11">services/api/src/models/base.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t11"><data value='utc_now'>utc_now</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t21">services/api/src/models/base.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t21"><data value='tablename__'>Base.__tablename__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html">services/api/src/models/base.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t98">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t98"><data value='utc_now'>_utc_now</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t47">services/api/src/models/file_upload.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t47"><data value='repr__'>FileUpload.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html">services/api/src/models/file_upload.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t69">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t69"><data value='repr__'>VMInstance.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t73">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t73"><data value='is_expired'>VMInstance.is_expired</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t80">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t80"><data value='runtime_minutes'>VMInstance.runtime_minutes</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t62">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t62"><data value='repr__'>WorkflowJob.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t65">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t65"><data value='add_celery_task'>WorkflowJob.add_celery_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t76">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t76"><data value='update_progress'>WorkflowJob.update_progress</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b___init___py.html">services/api/src/routes/__init__.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t61">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t61"><data value='get_celery_worker_status'>get_celery_worker_status</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t157">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t157"><data value='get_task_metrics'>get_task_metrics</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t235">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t235"><data value='get_detailed_celery_status'>get_detailed_celery_status</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t21">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t21"><data value='health_check'>health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t31">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t31"><data value='detailed_health_check'>detailed_health_check</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t79">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t79"><data value='readiness_check'>readiness_check</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t92">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t92"><data value='elasticsearch_health'>elasticsearch_health</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t119">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t119"><data value='minio_health'>minio_health</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t137">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t137"><data value='kibana_health'>kibana_health</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t168">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t168"><data value='redis_health'>redis_health</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t186">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t186"><data value='database_health'>database_health</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t215">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t215"><data value='logstash_health'>logstash_health</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t245">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html#t245"><data value='vm_monitor_health'>vm_monitor_health</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7___init___py.html">services/api/src/routes/v1/__init__.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t21">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t21"><data value='upload_file_endpoint'>upload_file_endpoint</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t83">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t83"><data value='list_files'>list_files</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t124">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t124"><data value='get_file_details'>get_file_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t162">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html#t162"><data value='get_file_download_url'>get_file_download_url</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t23">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t23"><data value='get_vm_templates'>get_vm_templates</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t154">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t154"><data value='create_vm'>create_vm</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t256">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t256"><data value='list_vms'>list_vms</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t301">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t301"><data value='get_vm_details'>get_vm_details</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t348">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t348"><data value='perform_vm_action'>perform_vm_action</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t458">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t458"><data value='delete_vm'>delete_vm</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>78</td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="90 90">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t22">services/api/src/routes/v1/workflow.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t22"><data value='start_workflow'>start_workflow</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t131">services/api/src/routes/v1/workflow.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t131"><data value='get_workflow_status'>get_workflow_status</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t190">services/api/src/routes/v1/workflow.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html#t190"><data value='list_workflows'>list_workflows</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html">services/api/src/routes/v1/workflow.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077___init___py.html">services/api/src/services/__init__.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html#t18">services/api/src/services/celery_app.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html#t18"><data value='init_celery'>init_celery</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html#t63">services/api/src/services/celery_app.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html#t63"><data value='get_celery_app'>get_celery_app</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html">services/api/src/services/celery_app.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t33">services/api/src/services/database.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t33"><data value='init_database'>init_database</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t39">services/api/src/services/database.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t39"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t48">services/api/src/services/database.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html#t48"><data value='get_sync_db'>get_sync_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html">services/api/src/services/database.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t18">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t18"><data value='init__'>ELKLogger.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t41">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t41"><data value='log_file_injection_event'>ELKLogger.log_file_injection_event</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t91">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t91"><data value='log_installation_base'>ELKLogger.log_installation_base</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t139">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t139"><data value='log_system_event'>ELKLogger.log_system_event</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t176">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t176"><data value='get_system_info'>ELKLogger._get_system_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t198">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t198"><data value='search_logs'>ELKLogger.search_logs</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t242">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t242"><data value='close'>ELKLogger.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t25">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t25"><data value='init__'>FileInjectionService.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t33">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t33"><data value='get_all'>FileInjectionService.get_all</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t57">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t57"><data value='get_by_id'>FileInjectionService.get_by_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t65">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t65"><data value='create_injection'>FileInjectionService.create_injection</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t105">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t105"><data value='get_status'>FileInjectionService.get_status</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t128">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t128"><data value='process_injection'>FileInjectionService.process_injection</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t185">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t185"><data value='simulate_processing'>FileInjectionService._simulate_processing</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t208">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t208"><data value='delete'>FileInjectionService.delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t29">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t29"><data value='init_minio'>init_minio</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t54">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t54"><data value='get_minio_client'>get_minio_client</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t61">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t61"><data value='upload_file'>upload_file</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t117">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t117"><data value='download_file'>download_file</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t144">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t144"><data value='get_file_url'>get_file_url</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t172">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html#t172"><data value='delete_file'>delete_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t19">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t19"><data value='init__'>VMManager.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t24">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t24"><data value='get_docker_client'>VMManager.get_docker_client</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t36">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t36"><data value='create_vagrant_vm'>VMManager.create_vagrant_vm</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t88">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t88"><data value='create_docker_vm'>VMManager.create_docker_vm</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t157">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t157"><data value='start_vm'>VMManager.start_vm</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t178">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t178"><data value='stop_vm'>VMManager.stop_vm</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t199">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t199"><data value='delete_vm'>VMManager.delete_vm</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t220">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t220"><data value='generate_vagrantfile'>VMManager._generate_vagrantfile</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t261">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t261"><data value='map_template_to_docker_image'>VMManager._map_template_to_docker_image</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t275">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t275"><data value='get_container_ip'>VMManager._get_container_ip</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t286">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t286"><data value='run_vagrant_command'>VMManager._run_vagrant_command</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t317">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t317"><data value='get_vagrant_vm_info'>VMManager._get_vagrant_vm_info</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t348">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t348"><data value='start_vagrant_vm'>VMManager._start_vagrant_vm</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t359">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t359"><data value='stop_vagrant_vm'>VMManager._stop_vagrant_vm</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t374">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t374"><data value='delete_vagrant_vm'>VMManager._delete_vagrant_vm</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t397">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t397"><data value='start_docker_vm'>VMManager._start_docker_vm</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t415">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t415"><data value='stop_docker_vm'>VMManager._stop_docker_vm</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t437">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t437"><data value='delete_docker_vm'>VMManager._delete_docker_vm</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t32">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t32"><data value='filter'>CorrelationIdFilter.filter</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t54">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t54"><data value='init__'>TurdPartyJSONFormatter.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t64">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t64"><data value='format'>TurdPartyJSONFormatter.format</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
                <td>1</td>
                <td class="right" data-ratio="11 13">84.62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t116">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t116"><data value='dispatch'>CorrelationIdMiddleware.dispatch</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t174">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t174"><data value='init__'>PerformanceLogger.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t183">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t183"><data value='log_operation'>PerformanceLogger.log_operation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t202">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t202"><data value='time_operation'>PerformanceLogger.time_operation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t213">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t213"><data value='decorator'>PerformanceLogger.time_operation.decorator</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t215">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t215"><data value='async_wrapper'>PerformanceLogger.time_operation.decorator.async_wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t237">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t237"><data value='sync_wrapper'>PerformanceLogger.time_operation.decorator.sync_wrapper</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t268">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t268"><data value='setup_logging'>setup_logging</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>1</td>
                <td class="right" data-ratio="18 20">90.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t319">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t319"><data value='get_correlation_id'>get_correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t329">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t329"><data value='set_correlation_id'>set_correlation_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t20">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t20"><data value='init__'>SphinxReportGenerator.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t41">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t41"><data value='generate_report_from_uuid'>SphinxReportGenerator.generate_report_from_uuid</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t89">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t89"><data value='fetch_report_data'>SphinxReportGenerator._fetch_report_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t105">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t105"><data value='fetch_ecs_data'>SphinxReportGenerator._fetch_ecs_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t127">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t127"><data value='generate_rst_content'>SphinxReportGenerator._generate_rst_content</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t150">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t150"><data value='categorize_events'>SphinxReportGenerator._categorize_events</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t158">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t158"><data value='summarize_installation'>SphinxReportGenerator._summarize_installation</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t196">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t196"><data value='create_process_timeline'>SphinxReportGenerator._create_process_timeline</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t211">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t211"><data value='summarize_file_changes'>SphinxReportGenerator._summarize_file_changes</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t226">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t226"><data value='summarize_registry_changes'>SphinxReportGenerator._summarize_registry_changes</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t246">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t246"><data value='get_report_filename'>SphinxReportGenerator._get_report_filename</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t253">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t253"><data value='update_reports_index'>SphinxReportGenerator._update_reports_index</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t278">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t278"><data value='build_html'>SphinxReportGenerator._build_html</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t310">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t310"><data value='generate_all_reports'>SphinxReportGenerator.generate_all_reports</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t361">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t361"><data value='serve_reports'>SphinxReportGenerator.serve_reports</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t381">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t381"><data value='cleanup'>SphinxReportGenerator.cleanup</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t387">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html#t387"><data value='main'>main</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html">services/report_generator.py</a></td>
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582___init___py.html">services/workers/tasks/__init__.py</a></td>
                <td class="name left"><a href="z_aa86a36947487582___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html#t15">services/workers/tasks/file_operations.py</a></td>
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html#t15"><data value='download_file_from_minio'>download_file_from_minio</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html#t122">services/workers/tasks/file_operations.py</a></td>
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html#t122"><data value='validate_file_for_injection'>validate_file_for_injection</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html">services/workers/tasks/file_operations.py</a></td>
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1676</td>
                <td>1107</td>
                <td>13</td>
                <td>370</td>
                <td>3</td>
                <td class="right" data-ratio="644 2046">31.48%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:30 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
