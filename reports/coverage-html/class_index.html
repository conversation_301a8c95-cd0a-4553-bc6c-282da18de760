<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">29.25%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:57 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066___init___py.html">services/api/src/__init__.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html">services/api/src/main.py</a></td>
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>27</td>
                <td>5</td>
                <td>6</td>
                <td>1</td>
                <td class="right" data-ratio="39 67">58.21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78___init___py.html">services/api/src/models/__init__.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html#t12">services/api/src/models/analysis_run.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html#t12"><data value='AnalysisRun'>AnalysisRun</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>1</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html#t172">services/api/src/models/analysis_run.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html#t172"><data value='RunComparison'>RunComparison</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html">services/api/src/models/analysis_run.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_analysis_run_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t16">services/api/src/models/base.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_base_py.html#t16"><data value='Base'>Base</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html">services/api/src/models/base.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93.33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t10">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t10"><data value='InjectionStatus'>InjectionStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t17">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t17"><data value='FileInjectionCreate'>FileInjectionCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t35">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t35"><data value='FileInjectionResponse'>FileInjectionResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t65">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t65"><data value='FileInjectionStatus'>FileInjectionStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t91">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t91"><data value='FileInjectionUpdate'>FileInjectionUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t103">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html#t103"><data value='ELKLogEntry'>ELKLogEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html">services/api/src/models/file_injection.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 52">98.08%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t14">services/api/src/models/file_upload.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t14"><data value='FileStatus'>FileStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t24">services/api/src/models/file_upload.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html#t24"><data value='FileUpload'>FileUpload</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html">services/api/src/models/file_upload.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t15">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t15"><data value='VMStatus'>VMStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t26">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html#t26"><data value='VMInstance'>VMInstance</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html">services/api/src/models/vm_instance.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>1</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t14">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t14"><data value='WorkflowStatus'>WorkflowStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t27">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html#t27"><data value='WorkflowJob'>WorkflowJob</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>1</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html">services/api/src/models/workflow_job.py</a></td>
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b___init___py.html">services/api/src/routes/__init__.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t18">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t18"><data value='CeleryWorkerStatus'>CeleryWorkerStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t38">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html#t38"><data value='TaskMetrics'>TaskMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html">services/api/src/routes/celery_health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>84</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="34 144">23.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html">services/api/src/routes/health.py</a></td>
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>127</td>
                <td>95</td>
                <td>0</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="52 161">32.30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7___init___py.html">services/api/src/routes/v1/__init__.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_enhanced_analysis_py.html">services/api/src/routes/v1/enhanced_analysis.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_enhanced_analysis_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>89</td>
                <td>89</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 135">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html">services/api/src/routes/v1/files.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>50</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="28 92">30.43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_run_comparison_py.html">services/api/src/routes/v1/run_comparison.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_run_comparison_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t86">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t86"><data value='VMTemplate'>VMTemplate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t101">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t101"><data value='VMAction'>VMAction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t111">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t111"><data value='VMActionRequest'>VMActionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t117">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t117"><data value='VMCreateRequest'>VMCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t131">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html#t131"><data value='VMResponse'>VMResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html">services/api/src/routes/v1/vms.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>176</td>
                <td>98</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="90 222">40.54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html">services/api/src/routes/v1/workflow.py</a></td>
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>56</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="25 97">25.77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077___init___py.html">services/api/src/services/__init__.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html">services/api/src/services/celery_app.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 18">55.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html">services/api/src/services/database.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>10</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 29">51.72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t15">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html#t15"><data value='ELKLogger'>ELKLogger</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html">services/api/src/services/elk_logger.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t22">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html#t22"><data value='FileInjectionService'>FileInjectionService</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 89">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html">services/api/src/services/file_injection_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html">services/api/src/services/minio_client.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>53</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="22 79">27.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_run_comparison_service_py.html#t15">services/api/src/services/run_comparison_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_run_comparison_service_py.html#t15"><data value='RunComparisonService'>RunComparisonService</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_run_comparison_service_py.html">services/api/src/services/run_comparison_service.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_run_comparison_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t16">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html#t16"><data value='VMManager'>VMManager</data></a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 204">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html">services/api/src/services/vm_manager.py</a></td>
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t24">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t24"><data value='CorrelationIdFilter'>CorrelationIdFilter</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t46">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t46"><data value='TurdPartyJSONFormatter'>TurdPartyJSONFormatter</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
                <td>1</td>
                <td class="right" data-ratio="13 15">86.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t108">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t108"><data value='CorrelationIdMiddleware'>CorrelationIdMiddleware</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t166">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html#t166"><data value='PerformanceLogger'>PerformanceLogger</data></a></td>
                <td>36</td>
                <td>35</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="1 46">2.17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html">services/api/src/utils/logging.py</a></td>
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>3</td>
                <td>0</td>
                <td>4</td>
                <td>1</td>
                <td class="right" data-ratio="45 49">91.84%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1747</td>
                <td>1190</td>
                <td>15</td>
                <td>400</td>
                <td>3</td>
                <td class="right" data-ratio="628 2147">29.25%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:57 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
