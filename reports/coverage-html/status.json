{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "0d225537713cf6f3512d4bd07c05d75d", "files": {"z_d77f5b497682b066___init___py": {"hash": "7b63013638218808a74ac05df33d59b1", "index": {"url": "z_d77f5b497682b066___init___py.html", "file": "services/api/src/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d77f5b497682b066_main_py": {"hash": "002b365594b9a71b3d09cfe093d98c63", "index": {"url": "z_d77f5b497682b066_main_py.html", "file": "services/api/src/main.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 61, "n_excluded": 5, "n_missing": 27, "n_branches": 6, "n_partial_branches": 1, "n_missing_branches": 1}}}, "z_e07897f544287e78___init___py": {"hash": "fe45e5434ec42e5ed91f5f7d7fec6927", "index": {"url": "z_e07897f544287e78___init___py.html", "file": "services/api/src/models/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e07897f544287e78_analysis_run_py": {"hash": "9dc76c11ff28190b1d53f10fb533bdf7", "index": {"url": "z_e07897f544287e78_analysis_run_py.html", "file": "services/api/src/models/analysis_run.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 80, "n_excluded": 4, "n_missing": 80, "n_branches": 16, "n_partial_branches": 0, "n_missing_branches": 16}}}, "z_e07897f544287e78_base_py": {"hash": "210345743631efff140a13b23704a4e6", "index": {"url": "z_e07897f544287e78_base_py.html", "file": "services/api/src/models/base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 2, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e07897f544287e78_file_injection_py": {"hash": "5402768955a5516354948022f925d9bf", "index": {"url": "z_e07897f544287e78_file_injection_py.html", "file": "services/api/src/models/file_injection.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e07897f544287e78_file_upload_py": {"hash": "aa0798ccacbbfb9a61133d06d59ed0c2", "index": {"url": "z_e07897f544287e78_file_upload_py.html", "file": "services/api/src/models/file_upload.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 26, "n_excluded": 2, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e07897f544287e78_vm_instance_py": {"hash": "dbe246c92fd17ceb2ced65d78f2dd228", "index": {"url": "z_e07897f544287e78_vm_instance_py.html", "file": "services/api/src/models/vm_instance.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 49, "n_excluded": 2, "n_missing": 7, "n_branches": 8, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_e07897f544287e78_workflow_job_py": {"hash": "2e0b10a78ff17bfc2aaec9b60e933e29", "index": {"url": "z_e07897f544287e78_workflow_job_py.html", "file": "services/api/src/models/workflow_job.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 46, "n_excluded": 2, "n_missing": 9, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_c95e0092a954a65b___init___py": {"hash": "dc245a46a14166bf9a3a501e58bbe5ed", "index": {"url": "z_c95e0092a954a65b___init___py.html", "file": "services/api/src/routes/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c95e0092a954a65b_celery_health_py": {"hash": "6cba1cc89a2975d9105b599c776ea767", "index": {"url": "z_c95e0092a954a65b_celery_health_py.html", "file": "services/api/src/routes/celery_health.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 84, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_c95e0092a954a65b_health_py": {"hash": "4ad38de7e82c238ea57b918b4476c483", "index": {"url": "z_c95e0092a954a65b_health_py.html", "file": "services/api/src/routes/health.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 95, "n_branches": 34, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_180ef2cae615fee7___init___py": {"hash": "3e3a3aba2057eff98a92d3552601afa7", "index": {"url": "z_180ef2cae615fee7___init___py.html", "file": "services/api/src/routes/v1/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_180ef2cae615fee7_enhanced_analysis_py": {"hash": "3ca1ebb3009afb3005f39e6878680103", "index": {"url": "z_180ef2cae615fee7_enhanced_analysis_py.html", "file": "services/api/src/routes/v1/enhanced_analysis.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 89, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 46}}}, "z_180ef2cae615fee7_files_py": {"hash": "07076f0c29559952beb26104ffd8ea4a", "index": {"url": "z_180ef2cae615fee7_files_py.html", "file": "services/api/src/routes/v1/files.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 50, "n_branches": 22, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_180ef2cae615fee7_run_comparison_py": {"hash": "57ea2a1dd38bd10aaaa8e6991a640ef7", "index": {"url": "z_180ef2cae615fee7_run_comparison_py.html", "file": "services/api/src/routes/v1/run_comparison.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 78, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_180ef2cae615fee7_vms_py": {"hash": "d691d293115c093a3eb9aff2959b35a1", "index": {"url": "z_180ef2cae615fee7_vms_py.html", "file": "services/api/src/routes/v1/vms.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 98, "n_branches": 46, "n_partial_branches": 0, "n_missing_branches": 34}}}, "z_180ef2cae615fee7_workflow_py": {"hash": "3ddb4f9e3d1863a92bbbafba007bad8d", "index": {"url": "z_180ef2cae615fee7_workflow_py.html", "file": "services/api/src/routes/v1/workflow.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 56, "n_branches": 22, "n_partial_branches": 0, "n_missing_branches": 16}}}, "z_5bfaf2b5aff22077___init___py": {"hash": "42972df273e768d77367f2a81247b293", "index": {"url": "z_5bfaf2b5aff22077___init___py.html", "file": "services/api/src/services/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5bfaf2b5aff22077_celery_app_py": {"hash": "6258cb8c60ac35e53818e32a026ed303", "index": {"url": "z_5bfaf2b5aff22077_celery_app_py.html", "file": "services/api/src/services/celery_app.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 6, "n_branches": 2, "n_partial_branches": 0, "n_missing_branches": 2}}}, "z_5bfaf2b5aff22077_database_py": {"hash": "924f45d714e8d3b7c3b9d619384752f7", "index": {"url": "z_5bfaf2b5aff22077_database_py.html", "file": "services/api/src/services/database.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 10, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_5bfaf2b5aff22077_elk_logger_py": {"hash": "05e5da18106f4446e662d1c882921060", "index": {"url": "z_5bfaf2b5aff22077_elk_logger_py.html", "file": "services/api/src/services/elk_logger.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 50, "n_branches": 6, "n_partial_branches": 0, "n_missing_branches": 6}}}, "z_5bfaf2b5aff22077_file_injection_service_py": {"hash": "7df98899a787aa5434f29c86d8753cca", "index": {"url": "z_5bfaf2b5aff22077_file_injection_service_py.html", "file": "services/api/src/services/file_injection_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 90, "n_excluded": 0, "n_missing": 71, "n_branches": 18, "n_partial_branches": 0, "n_missing_branches": 18}}}, "z_5bfaf2b5aff22077_minio_client_py": {"hash": "8ff73f79109207fc34355c8a08ed6762", "index": {"url": "z_5bfaf2b5aff22077_minio_client_py.html", "file": "services/api/src/services/minio_client.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 53, "n_branches": 4, "n_partial_branches": 0, "n_missing_branches": 4}}}, "z_5bfaf2b5aff22077_run_comparison_service_py": {"hash": "9df57ea01149b8267d837b5484e963ed", "index": {"url": "z_5bfaf2b5aff22077_run_comparison_service_py.html", "file": "services/api/src/services/run_comparison_service.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_5bfaf2b5aff22077_vm_manager_py": {"hash": "2a8fdb5c65d1702a1144cf0915306c54", "index": {"url": "z_5bfaf2b5aff22077_vm_manager_py.html", "file": "services/api/src/services/vm_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 160, "n_branches": 44, "n_partial_branches": 0, "n_missing_branches": 44}}}, "z_39b70bc3b132bbad_logging_py": {"hash": "c8a99719969af9d8e7bdbd538c9826c9", "index": {"url": "z_39b70bc3b132bbad_logging_py.html", "file": "services/api/src/utils/logging.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 50, "n_branches": 20, "n_partial_branches": 2, "n_missing_branches": 12}}}}}