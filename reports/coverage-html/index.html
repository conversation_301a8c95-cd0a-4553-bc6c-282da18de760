<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">31.48%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:30 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066___init___py.html">services/api/src/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d77f5b497682b066_main_py.html">services/api/src/main.py</a></td>
                <td>61</td>
                <td>26</td>
                <td>5</td>
                <td>6</td>
                <td>1</td>
                <td class="right" data-ratio="40 67">59.70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78___init___py.html">services/api/src/models/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_base_py.html">services/api/src/models/base.py</a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">87.50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_injection_py.html">services/api/src/models/file_injection.py</a></td>
                <td>52</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 52">98.08%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_file_upload_py.html">services/api/src/models/file_upload.py</a></td>
                <td>26</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_vm_instance_py.html">services/api/src/models/vm_instance.py</a></td>
                <td>49</td>
                <td>7</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="46 57">80.70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e07897f544287e78_workflow_job_py.html">services/api/src/models/workflow_job.py</a></td>
                <td>46</td>
                <td>9</td>
                <td>2</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="37 52">71.15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b___init___py.html">services/api/src/routes/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_celery_health_py.html">services/api/src/routes/celery_health.py</a></td>
                <td>112</td>
                <td>84</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="34 144">23.61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c95e0092a954a65b_health_py.html">services/api/src/routes/health.py</a></td>
                <td>127</td>
                <td>95</td>
                <td>0</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="52 161">32.30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7___init___py.html">services/api/src/routes/v1/__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_files_py.html">services/api/src/routes/v1/files.py</a></td>
                <td>70</td>
                <td>50</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="28 92">30.43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_vms_py.html">services/api/src/routes/v1/vms.py</a></td>
                <td>176</td>
                <td>98</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="90 222">40.54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_180ef2cae615fee7_workflow_py.html">services/api/src/routes/v1/workflow.py</a></td>
                <td>75</td>
                <td>56</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="25 97">25.77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077___init___py.html">services/api/src/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_celery_app_py.html">services/api/src/services/celery_app.py</a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 18">55.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_database_py.html">services/api/src/services/database.py</a></td>
                <td>25</td>
                <td>10</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 29">51.72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_elk_logger_py.html">services/api/src/services/elk_logger.py</a></td>
                <td>66</td>
                <td>50</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="16 72">22.22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_file_injection_service_py.html">services/api/src/services/file_injection_service.py</a></td>
                <td>90</td>
                <td>71</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="19 108">17.59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_minio_client_py.html">services/api/src/services/minio_client.py</a></td>
                <td>75</td>
                <td>53</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="22 79">27.85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5bfaf2b5aff22077_vm_manager_py.html">services/api/src/services/vm_manager.py</a></td>
                <td>189</td>
                <td>160</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="29 233">12.45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_39b70bc3b132bbad_logging_py.html">services/api/src/utils/logging.py</a></td>
                <td>103</td>
                <td>50</td>
                <td>0</td>
                <td>20</td>
                <td>2</td>
                <td class="right" data-ratio="61 123">49.59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3833460954761d_report_generator_py.html">services/report_generator.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>2</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 259">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582___init___py.html">services/workers/tasks/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa86a36947487582_file_operations_py.html">services/workers/tasks/file_operations.py</a></td>
                <td>95</td>
                <td>84</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="15 125">12.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1676</td>
                <td>1107</td>
                <td>13</td>
                <td>370</td>
                <td>3</td>
                <td class="right" data-ratio="644 2046">31.48%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-12 19:30 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_aa86a36947487582_file_operations_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_d77f5b497682b066___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
