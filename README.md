# 💩🎉TurdParty🎉💩 - Malware Analysis Platform

A comprehensive malware analysis platform with VM-based execution, real-time monitoring, and intelligent reporting.

## 🚀 Quick Start

```bash
# Start the platform
docker-compose up -d

# Access the platform
open http://localhost:8081
```

## 🏗️ Architecture

**Core Services**: API (FastAPI) • Workers (Celery) • Storage (MinIO/PostgreSQL) • Monitoring (ELK)

**Service URLs**:
- **Reports**: http://localhost:8081
- **API**: http://api.turdparty.localhost/docs  
- **Kibana**: http://kibana.turdparty.localhost/app/discover
- **MinIO**: http://minio.turdparty.localhost

## 📊 Features

✅ **File Upload & Analysis** - Secure upload with UUID tracking  
✅ **VM-based Execution** - Isolated Windows VM environment  
✅ **Real-time Monitoring** - ELK stack with ECS compliance  
✅ **Enhanced Analysis** - Installation wizard verification & artifact collection  
✅ **Performance** - 50+ events/sec, 100% success rate

## 🧪 Testing

```bash
# Run test suite
python -m pytest tests/ -v

# Integration tests  
scripts/run-comprehensive-tests.sh

# Top 20 binaries
python scripts/test-top-20-binaries.py
```

**Latest Results**: 20/20 binaries (100% success) • 1,506 ECS events • 29.7s execution

## 📚 Documentation

- **API Docs**: http://api.turdparty.localhost/docs
- **Sphinx Docs**: `docs/` directory
- **Build Docs**: `scripts/build-sphinx-docs.sh`

## 🔧 Development

```
turdparty-collab/
├── services/          # Service code (API, Workers)
├── docs/              # Documentation  
├── scripts/           # Essential scripts
├── tests/             # Test suite
├── config/            # Configurations
└── archive/           # Historical items
```

## 🌐 Key API Endpoints

- `POST /api/v1/files/upload` - Upload files
- `GET /api/v1/reports/binary/{uuid}` - Analysis reports
- `/api/v1/enhanced-analysis/*` - Enhanced analysis features

## 📈 Performance

**Benchmarks**: File upload <1s • VM provisioning <30s • 50+ events/sec processing  
**Scale**: 1000+ concurrent requests • 10TB+ ECS data • 100+ reports/min

## 🛠️ Operations

```bash
# Health check
curl http://api.turdparty.localhost/health

# Service logs
docker-compose logs -f

# Restart
docker-compose restart
```

---

**💩🥳TurdParty🥳💩** - Production-ready malware analysis platform! 🚀
