<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript Error Logging</title>
</head>
<body>
    <h1>💩🎉 TurdParty JavaScript Error Logging Test</h1>
    <p>This page will test the JavaScript error logging system.</p>
    
    <button id="trigger-error">Trigger JavaScript Error</button>
    <button id="trigger-promise-error">Trigger Promise Rejection</button>
    <button id="trigger-custom-error">Trigger Custom Error</button>
    
    <div id="status"></div>

    <!-- Load the error logger -->
    <script src="http://status.turdparty.localhost/assets/js/error-logger.js"></script>
    
    <script>
        // Wait for error logger to initialize
        setTimeout(() => {
            const statusDiv = document.getElementById('status');
            
            if (window.TurdPartyErrorLogger) {
                statusDiv.innerHTML = '<p style="color: green;">✅ Error Logger initialized successfully!</p>';
                
                // Test custom error logging
                window.TurdPartyErrorLogger.logInfo('Error logger test page loaded', 'test-page');
            } else {
                statusDiv.innerHTML = '<p style="color: red;">❌ Error Logger failed to initialize</p>';
            }
            
            // Set up test buttons
            document.getElementById('trigger-error').addEventListener('click', () => {
                // This will trigger a global error
                throw new Error('Test JavaScript error from button click');
            });
            
            document.getElementById('trigger-promise-error').addEventListener('click', () => {
                // This will trigger an unhandled promise rejection
                Promise.reject(new Error('Test promise rejection error'));
            });
            
            document.getElementById('trigger-custom-error').addEventListener('click', () => {
                // This will trigger a custom logged error
                if (window.TurdPartyErrorLogger) {
                    window.TurdPartyErrorLogger.logCustomError(
                        'User triggered custom error for testing',
                        'test-button',
                        {
                            buttonId: 'trigger-custom-error',
                            testType: 'manual',
                            timestamp: new Date().toISOString()
                        }
                    );
                    statusDiv.innerHTML += '<p style="color: blue;">📝 Custom error logged!</p>';
                }
            });
            
        }, 1000);
    </script>
</body>
</html>
