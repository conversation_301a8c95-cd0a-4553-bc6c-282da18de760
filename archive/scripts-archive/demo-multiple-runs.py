#!/usr/bin/env python3
"""
Demo script for multiple runs management and diff reporting.
Simulates running the same binary multiple times with variations.
"""

import json
import time
import uuid
import random
from datetime import datetime, timedelta
from pathlib import Path

class MultipleRunsDemo:
    """Demonstrate multiple runs management with diff reporting."""
    
    def __init__(self):
        self.base_uuid = str(uuid.uuid4())
        self.binary_name = "notepadpp"
        self.runs_data = []
        
    def simulate_run_variation(self, run_number: int, base_metrics: dict) -> dict:
        """Simulate variations in binary behavior across runs."""
        
        # Base metrics for Notepad++
        metrics = base_metrics.copy()
        
        # Simulate realistic variations
        if run_number == 1:
            # First run - baseline
            pass
        elif run_number == 2:
            # Minor variation - typical
            metrics['files_created'] += random.randint(-2, 3)
            metrics['execution_duration'] += random.uniform(-2.0, 5.0)
        elif run_number == 3:
            # Significant change - new version or environment change
            metrics['files_created'] += 8  # New plugin files
            metrics['registry_keys'] += 5  # Additional settings
            metrics['execution_duration'] += 15.0  # Slower due to new features
            metrics['processes_spawned'] += 1  # Additional helper process
        elif run_number == 4:
            # Performance regression
            metrics['execution_duration'] += 25.0  # Much slower
            metrics['files_created'] += 2
        elif run_number == 5:
            # Back to normal (fix applied)
            metrics['execution_duration'] = base_metrics['execution_duration'] + 1.0
            metrics['files_created'] = base_metrics['files_created']
        
        return metrics
    
    def generate_run_data(self, run_number: int) -> dict:
        """Generate realistic run data with variations."""
        
        # Base metrics for Notepad++
        base_metrics = {
            'files_created': 18,
            'registry_keys': 8,
            'processes_spawned': 2,
            'execution_duration': 12.3,
            'installer_runtime': 12.3,
            'ecs_events': 28
        }
        
        # Apply variations
        metrics = self.simulate_run_variation(run_number, base_metrics)
        
        # Generate realistic file paths
        base_files = [
            "C:\\Program Files\\Notepad++\\notepad++.exe",
            "C:\\Program Files\\Notepad++\\SciLexer.dll",
            "C:\\Program Files\\Notepad++\\config.xml",
            "C:\\Program Files\\Notepad++\\langs.xml",
            "C:\\Program Files\\Notepad++\\stylers.xml",
            "C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\config.xml",
            "C:\\Users\\<USER>\\Desktop\\Notepad++.lnk"
        ]
        
        # Add variation-specific files
        files_created = base_files.copy()
        if run_number == 3:  # New version with plugins
            files_created.extend([
                "C:\\Program Files\\Notepad++\\plugins\\ComparePlugin.dll",
                "C:\\Program Files\\Notepad++\\plugins\\NppFTP.dll",
                "C:\\Program Files\\Notepad++\\plugins\\XMLTools.dll"
            ])
        
        # Adjust file count to match metrics
        while len(files_created) < metrics['files_created']:
            files_created.append(f"C:\\Program Files\\Notepad++\\temp_file_{len(files_created)}.tmp")
        
        files_created = files_created[:metrics['files_created']]
        
        # Generate registry keys
        base_registry = [
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++",
            "HKEY_CURRENT_USER\\SOFTWARE\\Notepad++",
            "HKEY_CLASSES_ROOT\\.txt\\OpenWithProgids\\Notepad++_file"
        ]
        
        registry_keys = base_registry.copy()
        while len(registry_keys) < metrics['registry_keys']:
            registry_keys.append(f"HKEY_CURRENT_USER\\SOFTWARE\\Notepad++\\Setting_{len(registry_keys)}")
        
        registry_keys = registry_keys[:metrics['registry_keys']]
        
        # Generate process data
        processes = [
            {"name": "npp.8.6.Installer.x64.exe", "pid": 3000, "command": "npp.8.6.Installer.x64.exe /S"},
            {"name": "notepad++.exe", "pid": 3001, "command": "notepad++.exe"}
        ]
        
        if metrics['processes_spawned'] > 2:
            processes.append({"name": "NppShell_06.dll", "pid": 3002, "command": "regsvr32 NppShell_06.dll"})
        
        # Create run data
        run_data = {
            "run_number": run_number,
            "file_uuid": self.base_uuid,
            "binary_filename": "npp.8.6.Installer.x64.exe",
            "started_at": (datetime.utcnow() - timedelta(days=run_number)).isoformat(),
            "completed_at": (datetime.utcnow() - timedelta(days=run_number) + timedelta(seconds=metrics['execution_duration'])).isoformat(),
            "status": "completed",
            "execution_duration_seconds": metrics['execution_duration'],
            "installer_runtime_seconds": metrics['installer_runtime'],
            "ecs_events_count": metrics['ecs_events'],
            "files_created_count": metrics['files_created'],
            "registry_keys_count": metrics['registry_keys'],
            "processes_spawned_count": metrics['processes_spawned'],
            "analysis_results": {
                "files_created": files_created,
                "registry_keys": registry_keys,
                "processes": processes
            },
            "performance_metrics": {
                "total_runtime_seconds": metrics['execution_duration'],
                "installation_time_seconds": metrics['installer_runtime'],
                "download_time_seconds": 0,
                "pre_install_time_seconds": 1.2,
                "post_install_time_seconds": 2.1,
                "cleanup_time_seconds": 0.5
            }
        }
        
        return run_data
    
    def calculate_differences(self, current_run: dict, previous_run: dict) -> dict:
        """Calculate differences between two runs."""
        
        changes = []
        
        # Compare key metrics
        metrics_to_compare = [
            ('files_created_count', 'files_created'),
            ('registry_keys_count', 'registry_keys'),
            ('processes_spawned_count', 'processes_spawned'),
            ('execution_duration_seconds', 'execution_duration')
        ]
        
        for metric_key, display_name in metrics_to_compare:
            current_val = current_run.get(metric_key, 0)
            previous_val = previous_run.get(metric_key, 0)
            
            if current_val != previous_val:
                change_data = {
                    "metric": display_name,
                    "previous": previous_val,
                    "current": current_val,
                    "change": current_val - previous_val
                }
                
                if metric_key == 'execution_duration_seconds' and previous_val > 0:
                    change_data["change_percent"] = ((current_val - previous_val) / previous_val) * 100
                
                changes.append(change_data)
        
        # Calculate anomaly score
        anomaly_score = min(len(changes) * 15, 100)
        
        # Add extra points for significant performance changes
        for change in changes:
            if change["metric"] == "execution_duration" and "change_percent" in change:
                if abs(change["change_percent"]) > 50:
                    anomaly_score += 30
                elif abs(change["change_percent"]) > 20:
                    anomaly_score += 15
        
        anomaly_score = min(anomaly_score, 100)
        
        return {
            "differs_from_previous": len(changes) > 0,
            "diff_summary": {
                "type": "comparison",
                "changes_count": len(changes),
                "changes": changes,
                "previous_run": previous_run["run_number"],
                "comparison_timestamp": datetime.utcnow().isoformat()
            },
            "anomaly_score": anomaly_score
        }
    
    def generate_multiple_runs(self, num_runs: int = 5) -> list:
        """Generate multiple runs with realistic variations."""
        
        print(f"🔄 Generating {num_runs} runs for {self.binary_name}")
        print(f"📋 Base UUID: {self.base_uuid}")
        print("=" * 60)
        
        runs = []
        
        for run_number in range(1, num_runs + 1):
            print(f"\n📊 Generating run #{run_number}...")
            
            # Generate run data
            run_data = self.generate_run_data(run_number)
            
            # Calculate differences from previous run
            if run_number > 1:
                previous_run = runs[-1]
                diff_data = self.calculate_differences(run_data, previous_run)
                run_data.update(diff_data)
                
                print(f"   🔍 Changes from run #{run_number-1}: {diff_data['diff_summary']['changes_count']}")
                print(f"   ⚠️ Anomaly score: {diff_data['anomaly_score']}/100")
                
                if diff_data['differs_from_previous']:
                    for change in diff_data['diff_summary']['changes'][:3]:
                        print(f"      • {change['metric']}: {change['previous']} → {change['current']}")
            else:
                run_data.update({
                    "differs_from_previous": False,
                    "diff_summary": {"type": "first_run", "changes": []},
                    "anomaly_score": 0
                })
                print(f"   ✅ First run (baseline)")
            
            runs.append(run_data)
        
        self.runs_data = runs
        return runs
    
    def generate_comparison_report(self, run_a_idx: int, run_b_idx: int) -> dict:
        """Generate detailed comparison between two specific runs."""
        
        if run_a_idx >= len(self.runs_data) or run_b_idx >= len(self.runs_data):
            raise ValueError("Invalid run indices")
        
        run_a = self.runs_data[run_a_idx]
        run_b = self.runs_data[run_b_idx]
        
        print(f"\n🔍 Detailed Comparison: Run #{run_a['run_number']} vs Run #{run_b['run_number']}")
        print("=" * 60)
        
        # File differences
        files_a = set(run_a['analysis_results']['files_created'])
        files_b = set(run_b['analysis_results']['files_created'])
        
        files_diff = {
            "added_files": list(files_b - files_a),
            "removed_files": list(files_a - files_b),
            "common_files": list(files_a & files_b)
        }
        
        # Registry differences
        registry_a = set(run_a['analysis_results']['registry_keys'])
        registry_b = set(run_b['analysis_results']['registry_keys'])
        
        registry_diff = {
            "added_keys": list(registry_b - registry_a),
            "removed_keys": list(registry_a - registry_b),
            "common_keys": list(registry_a & registry_b)
        }
        
        # Performance differences
        perf_diff = {
            "execution_time_change": run_b['execution_duration_seconds'] - run_a['execution_duration_seconds'],
            "execution_time_change_percent": ((run_b['execution_duration_seconds'] - run_a['execution_duration_seconds']) / run_a['execution_duration_seconds']) * 100
        }
        
        comparison = {
            "run_a": run_a['run_number'],
            "run_b": run_b['run_number'],
            "files_diff": files_diff,
            "registry_diff": registry_diff,
            "performance_diff": perf_diff,
            "total_changes": len(files_diff['added_files']) + len(files_diff['removed_files']) + 
                           len(registry_diff['added_keys']) + len(registry_diff['removed_keys'])
        }
        
        # Print summary
        print(f"📁 Files: +{len(files_diff['added_files'])} -{len(files_diff['removed_files'])}")
        print(f"📋 Registry: +{len(registry_diff['added_keys'])} -{len(registry_diff['removed_keys'])}")
        print(f"⚡ Performance: {perf_diff['execution_time_change']:+.1f}s ({perf_diff['execution_time_change_percent']:+.1f}%)")
        print(f"📊 Total Changes: {comparison['total_changes']}")
        
        return comparison
    
    def save_results(self):
        """Save all run data and comparisons to files."""
        
        timestamp = int(time.time())
        
        # Save runs data
        runs_file = f"/tmp/multiple-runs-demo-{timestamp}.json"
        with open(runs_file, 'w') as f:
            json.dump({
                "demo_metadata": {
                    "binary_name": self.binary_name,
                    "base_uuid": self.base_uuid,
                    "total_runs": len(self.runs_data),
                    "generated_at": datetime.utcnow().isoformat()
                },
                "runs": self.runs_data
            }, f, indent=2)
        
        print(f"\n📄 Results saved: {runs_file}")
        
        # Generate summary
        print(f"\n📊 MULTIPLE RUNS DEMO SUMMARY")
        print("=" * 60)
        print(f"Binary: {self.binary_name}")
        print(f"UUID: {self.base_uuid}")
        print(f"Total Runs: {len(self.runs_data)}")
        
        runs_with_changes = [r for r in self.runs_data if r.get('differs_from_previous', False)]
        print(f"Runs with Changes: {len(runs_with_changes)}")
        
        if runs_with_changes:
            avg_anomaly = sum(r['anomaly_score'] for r in runs_with_changes) / len(runs_with_changes)
            print(f"Average Anomaly Score: {avg_anomaly:.1f}/100")
            
            high_anomaly_runs = [r for r in runs_with_changes if r['anomaly_score'] > 50]
            print(f"High Anomaly Runs: {len(high_anomaly_runs)}")
        
        return runs_file

def main():
    """Main demo execution."""
    print("🚀 Multiple Runs Management Demo")
    print("Simulating realistic variations in binary analysis")
    print("=" * 60)
    
    demo = MultipleRunsDemo()
    
    # Generate 5 runs with variations
    runs = demo.generate_multiple_runs(5)
    
    # Generate detailed comparisons
    print(f"\n🔍 DETAILED COMPARISONS")
    print("=" * 60)
    
    # Compare run 1 vs 3 (significant changes)
    demo.generate_comparison_report(0, 2)
    
    # Compare run 3 vs 5 (regression and fix)
    demo.generate_comparison_report(2, 4)
    
    # Save results
    demo.save_results()
    
    print(f"\n🎉 Demo complete! Multiple runs management demonstrated.")

if __name__ == "__main__":
    main()
