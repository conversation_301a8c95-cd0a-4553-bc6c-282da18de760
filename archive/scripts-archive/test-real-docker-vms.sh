#!/bin/bash

# Real Docker VM Tests - Direct Container Testing
# Tests actual Docker container creation and management without API dependencies

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
TEST_CONTAINERS=()
TIMESTAMP=$(date +%s)

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║              🐳 Real Docker VM Tests                        ║"
    print_colored $CYAN "║           Direct Container Testing - No Mocks               ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to cleanup containers
cleanup_containers() {
    print_colored $PURPLE "🧹 Cleaning up test containers..."
    
    for container_id in "${TEST_CONTAINERS[@]}"; do
        if [ -n "$container_id" ]; then
            docker stop "$container_id" >/dev/null 2>&1 || true
            docker rm -f "$container_id" >/dev/null 2>&1 || true
            print_colored $GREEN "   ✅ Cleaned up: $container_id"
        fi
    done
    
    # Clean up any remaining test containers
    docker ps -a --filter "label=turdparty.test=true" --format "{{.ID}}" | xargs -r docker rm -f >/dev/null 2>&1 || true
    
    echo ""
}

# Function to test Docker availability
test_docker_availability() {
    print_colored $PURPLE "🔍 Testing Docker Availability:"
    echo ""
    
    if ! command -v docker >/dev/null 2>&1; then
        print_colored $RED "❌ Docker command not found"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_colored $RED "❌ Docker daemon not running"
        exit 1
    fi
    
    local docker_version=$(docker --version)
    print_colored $GREEN "✅ Docker available: $docker_version"
    echo ""
}

# Function to test Ubuntu container creation
test_ubuntu_container() {
    print_colored $PURPLE "🐧 Testing Ubuntu Container Creation:"
    echo ""
    
    local container_name="turdparty_test_ubuntu_${TIMESTAMP}"
    
    print_colored $BLUE "📦 Creating Ubuntu 20.04 container..."
    
    # Create Ubuntu container with TurdParty configuration
    local container_id=$(docker run -d \
        --name "$container_name" \
        --label "turdparty.vm=true" \
        --label "turdparty.vm.name=$container_name" \
        --label "turdparty.vm.template=ubuntu:20.04" \
        --label "turdparty.test=true" \
        --memory="512m" \
        --cpus="1" \
        --volume "/tmp:/tmp:rw" \
        ubuntu:20.04 \
        /bin/bash -c 'while true; do sleep 30; done')
    
    if [ $? -eq 0 ] && [ -n "$container_id" ]; then
        TEST_CONTAINERS+=("$container_id")
        print_colored $GREEN "✅ Ubuntu container created: ${container_id:0:12}"
        
        # Test container is running
        local status=$(docker inspect --format='{{.State.Status}}' "$container_id")
        if [ "$status" = "running" ]; then
            print_colored $GREEN "✅ Container is running"
            
            # Test Ubuntu version
            local ubuntu_version=$(docker exec "$container_id" cat /etc/os-release | grep "PRETTY_NAME" | cut -d'"' -f2)
            print_colored $GREEN "✅ OS: $ubuntu_version"
            
            # Test file system access
            docker exec "$container_id" bash -c 'echo "TurdParty test file" > /tmp/test_file.txt'
            local file_content=$(docker exec "$container_id" cat /tmp/test_file.txt)
            if [ "$file_content" = "TurdParty test file" ]; then
                print_colored $GREEN "✅ File system access working"
            else
                print_colored $RED "❌ File system access failed"
            fi
            
            # Test network connectivity
            if docker exec "$container_id" ping -c 1 ******* >/dev/null 2>&1; then
                print_colored $GREEN "✅ Network connectivity working"
            else
                print_colored $YELLOW "⚠️ Network connectivity test failed"
            fi
            
            # Test resource limits
            local memory_limit=$(docker inspect --format='{{.HostConfig.Memory}}' "$container_id")
            if [ "$memory_limit" = "536870912" ]; then  # 512MB in bytes
                print_colored $GREEN "✅ Memory limit applied: 512MB"
            else
                print_colored $YELLOW "⚠️ Memory limit: $memory_limit bytes"
            fi
            
        else
            print_colored $RED "❌ Container not running: $status"
        fi
    else
        print_colored $RED "❌ Ubuntu container creation failed"
    fi
    
    echo ""
}

# Function to test Alpine container creation
test_alpine_container() {
    print_colored $PURPLE "🏔️ Testing Alpine Container Creation:"
    echo ""
    
    local container_name="turdparty_test_alpine_${TIMESTAMP}"
    
    print_colored $BLUE "📦 Creating Alpine Linux container..."
    
    # Create Alpine container
    local container_id=$(docker run -d \
        --name "$container_name" \
        --label "turdparty.vm=true" \
        --label "turdparty.vm.name=$container_name" \
        --label "turdparty.vm.template=alpine:latest" \
        --label "turdparty.test=true" \
        --memory="256m" \
        --cpus="1" \
        alpine:latest \
        /bin/sh -c 'while true; do sleep 30; done')
    
    if [ $? -eq 0 ] && [ -n "$container_id" ]; then
        TEST_CONTAINERS+=("$container_id")
        print_colored $GREEN "✅ Alpine container created: ${container_id:0:12}"
        
        # Test Alpine version
        local alpine_version=$(docker exec "$container_id" cat /etc/alpine-release 2>/dev/null || echo "Unknown")
        print_colored $GREEN "✅ Alpine version: $alpine_version"
        
        # Test package manager
        if docker exec "$container_id" apk --version >/dev/null 2>&1; then
            print_colored $GREEN "✅ Alpine package manager (apk) available"
        else
            print_colored $RED "❌ Alpine package manager not available"
        fi
        
        # Test minimal footprint
        local container_size=$(docker exec "$container_id" du -sh / 2>/dev/null | cut -f1 || echo "Unknown")
        print_colored $GREEN "✅ Container size: $container_size"
        
    else
        print_colored $RED "❌ Alpine container creation failed"
    fi
    
    echo ""
}

# Function to test CentOS container creation
test_centos_container() {
    print_colored $PURPLE "🔴 Testing CentOS Container Creation:"
    echo ""
    
    local container_name="turdparty_test_centos_${TIMESTAMP}"
    
    print_colored $BLUE "📦 Creating CentOS 7 container..."
    
    # Create CentOS container
    local container_id=$(docker run -d \
        --name "$container_name" \
        --label "turdparty.vm=true" \
        --label "turdparty.vm.name=$container_name" \
        --label "turdparty.vm.template=centos:7" \
        --label "turdparty.test=true" \
        --memory="1024m" \
        --cpus="1" \
        centos:7 \
        /bin/bash -c 'while true; do sleep 30; done')
    
    if [ $? -eq 0 ] && [ -n "$container_id" ]; then
        TEST_CONTAINERS+=("$container_id")
        print_colored $GREEN "✅ CentOS container created: ${container_id:0:12}"
        
        # Test CentOS version
        local centos_version=$(docker exec "$container_id" cat /etc/centos-release 2>/dev/null || echo "Unknown")
        print_colored $GREEN "✅ CentOS version: $centos_version"
        
        # Test package manager
        if docker exec "$container_id" yum --version >/dev/null 2>&1; then
            print_colored $GREEN "✅ CentOS package manager (yum) available"
        else
            print_colored $RED "❌ CentOS package manager not available"
        fi
        
    else
        print_colored $RED "❌ CentOS container creation failed"
    fi
    
    echo ""
}

# Function to test container networking
test_container_networking() {
    print_colored $PURPLE "🌐 Testing Container Networking:"
    echo ""
    
    if [ ${#TEST_CONTAINERS[@]} -eq 0 ]; then
        print_colored $YELLOW "⚠️ No containers available for networking test"
        return
    fi
    
    local test_container="${TEST_CONTAINERS[0]}"
    
    # Test container IP
    local container_ip=$(docker inspect --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' "$test_container")
    if [ -n "$container_ip" ]; then
        print_colored $GREEN "✅ Container IP: $container_ip"
    else
        print_colored $RED "❌ No container IP found"
    fi
    
    # Test DNS resolution
    if docker exec "$test_container" nslookup google.com >/dev/null 2>&1; then
        print_colored $GREEN "✅ DNS resolution working"
    else
        print_colored $YELLOW "⚠️ DNS resolution test failed"
    fi
    
    echo ""
}

# Function to test file injection simulation
test_file_injection() {
    print_colored $PURPLE "💉 Testing File Injection Simulation:"
    echo ""
    
    if [ ${#TEST_CONTAINERS[@]} -eq 0 ]; then
        print_colored $YELLOW "⚠️ No containers available for file injection test"
        return
    fi
    
    local test_container="${TEST_CONTAINERS[0]}"
    
    # Create test file
    local test_file="/tmp/turdparty_injection_test.txt"
    echo "TurdParty file injection test - $(date)" > "$test_file"
    echo "Container: $test_container" >> "$test_file"
    echo "Test data: $(openssl rand -hex 16)" >> "$test_file"
    
    # Copy file to container
    if docker cp "$test_file" "$test_container:/tmp/injected_file.txt"; then
        print_colored $GREEN "✅ File copied to container"
        
        # Verify file in container
        local injected_content=$(docker exec "$test_container" cat /tmp/injected_file.txt)
        if echo "$injected_content" | grep -q "TurdParty file injection test"; then
            print_colored $GREEN "✅ File injection successful"
            print_colored $YELLOW "   📄 Injected content preview:"
            echo "$injected_content" | head -2 | sed 's/^/      /'
        else
            print_colored $RED "❌ File injection verification failed"
        fi
    else
        print_colored $RED "❌ File copy to container failed"
    fi
    
    # Cleanup test file
    rm -f "$test_file"
    
    echo ""
}

# Function to test container resource monitoring
test_resource_monitoring() {
    print_colored $PURPLE "📊 Testing Container Resource Monitoring:"
    echo ""
    
    if [ ${#TEST_CONTAINERS[@]} -eq 0 ]; then
        print_colored $YELLOW "⚠️ No containers available for monitoring test"
        return
    fi
    
    for container_id in "${TEST_CONTAINERS[@]}"; do
        if [ -n "$container_id" ]; then
            print_colored $BLUE "📈 Monitoring container: ${container_id:0:12}"
            
            # Get container stats
            local stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" "$container_id")
            echo "$stats" | tail -n +2 | sed 's/^/   /'
            
            # Get detailed memory info
            local memory_usage=$(docker exec "$container_id" cat /proc/meminfo | grep MemTotal | awk '{print $2}' 2>/dev/null || echo "Unknown")
            if [ "$memory_usage" != "Unknown" ]; then
                local memory_mb=$((memory_usage / 1024))
                print_colored $GREEN "   📊 Container memory: ${memory_mb}MB"
            fi
        fi
    done
    
    echo ""
}

# Function to test container lifecycle operations
test_container_lifecycle() {
    print_colored $PURPLE "🔄 Testing Container Lifecycle Operations:"
    echo ""
    
    local container_name="turdparty_lifecycle_test_${TIMESTAMP}"
    
    # Create container
    print_colored $BLUE "📦 Creating lifecycle test container..."
    local container_id=$(docker run -d \
        --name "$container_name" \
        --label "turdparty.test=true" \
        alpine:latest \
        /bin/sh -c 'while true; do sleep 10; done')
    
    if [ $? -eq 0 ] && [ -n "$container_id" ]; then
        TEST_CONTAINERS+=("$container_id")
        print_colored $GREEN "✅ Container created: ${container_id:0:12}"
        
        # Test pause/unpause
        print_colored $BLUE "⏸️ Testing container pause..."
        if docker pause "$container_id" >/dev/null 2>&1; then
            print_colored $GREEN "✅ Container paused"
            
            if docker unpause "$container_id" >/dev/null 2>&1; then
                print_colored $GREEN "✅ Container unpaused"
            else
                print_colored $RED "❌ Container unpause failed"
            fi
        else
            print_colored $RED "❌ Container pause failed"
        fi
        
        # Test stop/start
        print_colored $BLUE "🛑 Testing container stop..."
        if docker stop "$container_id" >/dev/null 2>&1; then
            print_colored $GREEN "✅ Container stopped"
            
            print_colored $BLUE "▶️ Testing container start..."
            if docker start "$container_id" >/dev/null 2>&1; then
                print_colored $GREEN "✅ Container restarted"
            else
                print_colored $RED "❌ Container restart failed"
            fi
        else
            print_colored $RED "❌ Container stop failed"
        fi
    else
        print_colored $RED "❌ Lifecycle test container creation failed"
    fi
    
    echo ""
}

# Function to generate test summary
generate_summary() {
    print_colored $PURPLE "📊 Test Summary:"
    echo ""
    
    local total_containers=${#TEST_CONTAINERS[@]}
    local running_containers=0
    
    for container_id in "${TEST_CONTAINERS[@]}"; do
        if [ -n "$container_id" ]; then
            local status=$(docker inspect --format='{{.State.Status}}' "$container_id" 2>/dev/null || echo "unknown")
            if [ "$status" = "running" ]; then
                running_containers=$((running_containers + 1))
            fi
        fi
    done
    
    print_colored $GREEN "✅ Total containers created: $total_containers"
    print_colored $GREEN "✅ Containers running: $running_containers"
    
    if [ $total_containers -gt 0 ]; then
        local success_rate=$((running_containers * 100 / total_containers))
        print_colored $CYAN "📈 Success rate: ${success_rate}%"
    fi
    
    echo ""
    print_colored $CYAN "🎯 Real Docker VM testing demonstrates:"
    print_colored $CYAN "   • Actual container creation and management"
    print_colored $CYAN "   • Resource limit enforcement"
    print_colored $CYAN "   • File injection capabilities"
    print_colored $CYAN "   • Network connectivity"
    print_colored $CYAN "   • Lifecycle operations (start/stop/pause)"
    print_colored $CYAN "   • Multi-OS support (Ubuntu/Alpine/CentOS)"
    echo ""
}

# Main function
main() {
    print_banner
    
    # Test Docker availability
    test_docker_availability
    
    # Run container tests
    test_ubuntu_container
    test_alpine_container
    test_centos_container
    
    # Test advanced features
    test_container_networking
    test_file_injection
    test_resource_monitoring
    test_container_lifecycle
    
    # Generate summary
    generate_summary
    
    print_colored $GREEN "🎉 Real Docker VM testing completed successfully!"
    print_colored $GREEN "All tests used actual Docker containers - no mocks!"
    echo ""
}

# Trap to ensure cleanup on exit
trap cleanup_containers EXIT

# Run main function
main "$@"
