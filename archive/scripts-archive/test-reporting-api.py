#!/usr/bin/env python3
"""
TurdParty Reporting API Test Script
Demonstrates the comprehensive reporting capabilities for Windows binary analysis.
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.service_urls import ServiceURLManager
import httpx

class ReportingAPITester:
    """Test the TurdParty Reporting API endpoints."""
    
    def __init__(self):
        self.url_manager = ServiceURLManager('development')
        self.session = httpx.AsyncClient(timeout=30.0)
        
        # Use the UUIDs from our previous Windows dev binaries test
        self.test_uuids = [
            "148f183a-38f6-4d09-9433-30e14db058ce",  # VS Code
            "8b2bb1e1-4901-4e6b-8fa0-c59d6d25d17f",  # Python
            "d82b5558-5056-4e7b-8307-657b0dd2512f",  # Git
            "05d76c5b-c83a-45c8-a609-e6bc11705da2",  # Node.js
            "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"   # Notepad++
        ]
        
        self.test_results = []
    
    async def test_binary_report(self, file_uuid: str):
        """Test complete binary report generation."""
        print(f"\n📊 Testing Binary Report for UUID: {file_uuid}")
        
        try:
            report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=file_uuid)
            print(f"   URL: {report_url}")
            
            start_time = time.time()
            response = await self.session.get(report_url)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                report = response.json()
                print(f"   ✅ Report generated in {response_time:.2f}s")
                print(f"   📄 Report ID: {report.get('metadata', {}).get('report_id', 'N/A')}")
                print(f"   📁 Filename: {report.get('file_info', {}).get('filename', 'N/A')}")
                print(f"   🔍 Risk Level: {report.get('security_analysis', {}).get('threat_indicators', {}).get('risk_level', 'N/A')}")
                print(f"   📈 Files Created: {report.get('installation_footprint', {}).get('filesystem_changes', {}).get('files_created', 0)}")
                
                return {
                    'uuid': file_uuid,
                    'success': True,
                    'response_time': response_time,
                    'report': report
                }
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                print(f"   Error: {response.text}")
                return {
                    'uuid': file_uuid,
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {
                'uuid': file_uuid,
                'success': False,
                'error': str(e)
            }
    
    async def test_binary_summary(self, file_uuid: str):
        """Test binary summary endpoint."""
        print(f"\n📋 Testing Binary Summary for UUID: {file_uuid}")
        
        try:
            summary_url = self.url_manager.get_api_endpoint('reporting', 'binary_summary', file_uuid=file_uuid)
            
            response = await self.session.get(summary_url)
            
            if response.status_code == 200:
                summary = response.json()
                print(f"   ✅ Summary retrieved")
                print(f"   📁 Filename: {summary.get('filename', 'N/A')}")
                print(f"   📊 Executions: {summary.get('total_executions', 0)}")
                print(f"   🔍 Risk: {summary.get('risk_level', 'N/A')}")
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def test_installation_footprint(self, file_uuid: str):
        """Test installation footprint endpoint."""
        print(f"\n👣 Testing Installation Footprint for UUID: {file_uuid}")
        
        try:
            footprint_url = self.url_manager.get_api_endpoint('reporting', 'installation_footprint', file_uuid=file_uuid)
            
            response = await self.session.get(footprint_url)
            
            if response.status_code == 200:
                footprint = response.json()
                print(f"   ✅ Footprint analyzed")
                print(f"   📁 Files Created: {footprint.get('files_created', 0)}")
                print(f"   📂 Directories: {footprint.get('directories_created', 0)}")
                print(f"   🔑 Registry Keys: {footprint.get('registry_keys_created', 0)}")
                print(f"   ⚙️ Services: {footprint.get('services_installed', 0)}")
                print(f"   💾 Disk Usage: {footprint.get('total_disk_usage_mb', 0):.1f} MB")
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def test_runtime_behavior(self, file_uuid: str):
        """Test runtime behavior endpoint."""
        print(f"\n⚡ Testing Runtime Behavior for UUID: {file_uuid}")
        
        try:
            runtime_url = self.url_manager.get_api_endpoint('reporting', 'runtime_behavior', file_uuid=file_uuid)
            
            response = await self.session.get(runtime_url)
            
            if response.status_code == 200:
                runtime = response.json()
                print(f"   ✅ Runtime analyzed")
                print(f"   ⏱️ Duration: {runtime.get('execution_duration_seconds', 0):.1f}s")
                print(f"   🔄 Processes: {runtime.get('processes_spawned', 0)}")
                print(f"   🌐 Network: {runtime.get('network_connections', 0)} connections")
                print(f"   🖥️ Peak CPU: {runtime.get('peak_cpu_percent', 0):.1f}%")
                print(f"   💾 Peak Memory: {runtime.get('peak_memory_mb', 0):.1f} MB")
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def test_search_reports(self):
        """Test search functionality."""
        print(f"\n🔍 Testing Report Search")
        
        try:
            search_url = self.url_manager.get_api_endpoint('reporting', 'search')
            
            # Test search by filename pattern
            search_query = {
                "filename": "VSCode",
                "limit": 10,
                "offset": 0
            }
            
            response = await self.session.post(search_url, json=search_query)
            
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ Search completed")
                print(f"   📊 Total Results: {results.get('total', 0)}")
                print(f"   📄 Reports Returned: {len(results.get('reports', []))}")
                
                for report in results.get('reports', [])[:3]:  # Show first 3
                    print(f"      - {report.get('filename', 'N/A')} (Risk: {report.get('risk_level', 'N/A')})")
                
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def test_compare_binaries(self):
        """Test binary comparison functionality."""
        print(f"\n⚖️ Testing Binary Comparison")
        
        try:
            compare_url = self.url_manager.get_api_endpoint('reporting', 'compare')
            
            # Compare first 3 UUIDs
            compare_request = {
                "file_uuids": self.test_uuids[:3],
                "comparison_fields": ["installation_footprint", "runtime_behavior", "security_analysis"]
            }
            
            response = await self.session.post(compare_url, json=compare_request)
            
            if response.status_code == 200:
                comparison = response.json()
                print(f"   ✅ Comparison completed")
                print(f"   📊 Binaries Compared: {comparison.get('summary', {}).get('binaries_compared', 0)}")
                print(f"   ✅ Successful Reports: {comparison.get('summary', {}).get('successful_reports', 0)}")
                
                # Show comparison data
                for uuid, data in comparison.get('data', {}).items():
                    if 'error' not in data:
                        print(f"      - {data.get('filename', 'N/A')} ({uuid[:8]}...)")
                    else:
                        print(f"      - Error for {uuid[:8]}...: {data['error']}")
                
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def test_reporting_health(self):
        """Test reporting service health."""
        print(f"\n🏥 Testing Reporting Service Health")
        
        try:
            health_url = self.url_manager.get_api_endpoint('reporting', 'health')
            
            response = await self.session.get(health_url)
            
            if response.status_code == 200:
                health = response.json()
                print(f"   ✅ Service Status: {health.get('status', 'unknown')}")
                
                es_status = health.get('elasticsearch', {})
                print(f"   🔍 Elasticsearch: {es_status.get('status', 'unknown')}")
                print(f"   🏢 Cluster: {es_status.get('cluster_name', 'N/A')}")
                print(f"   🖥️ Nodes: {es_status.get('number_of_nodes', 0)}")
                
                return True
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive test of all reporting endpoints."""
        print("🚀 Starting TurdParty Reporting API Comprehensive Test")
        print(f"📅 Test started at: {datetime.now()}")
        print(f"🎯 Testing {len(self.test_uuids)} binary UUIDs")
        print("=" * 80)
        
        # Test service health first
        health_ok = await self.test_reporting_health()
        
        if not health_ok:
            print("\n⚠️ Reporting service health check failed - continuing with limited testing")
        
        # Test individual binary reports
        successful_reports = 0
        for i, uuid in enumerate(self.test_uuids, 1):
            print(f"\n🔄 Progress: {i}/{len(self.test_uuids)} - Testing UUID: {uuid[:8]}...")
            
            # Test complete report
            report_result = await self.test_binary_report(uuid)
            self.test_results.append(report_result)
            
            if report_result['success']:
                successful_reports += 1
                
                # Test individual endpoints
                await self.test_binary_summary(uuid)
                await self.test_installation_footprint(uuid)
                await self.test_runtime_behavior(uuid)
        
        # Test search functionality
        await self.test_search_reports()
        
        # Test comparison functionality
        await self.test_compare_binaries()
        
        # Generate summary
        await self.generate_test_summary(successful_reports)
    
    async def generate_test_summary(self, successful_reports: int):
        """Generate comprehensive test summary."""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE REPORTING API TEST SUMMARY")
        print(f"{'='*80}")
        
        print(f"\n📈 OVERALL RESULTS:")
        print(f"   Total UUIDs Tested: {len(self.test_uuids)}")
        print(f"   Successful Reports: {successful_reports}")
        print(f"   Failed Reports: {len(self.test_uuids) - successful_reports}")
        print(f"   Success Rate: {successful_reports/len(self.test_uuids)*100:.1f}%")
        
        print(f"\n🔑 TESTED UUIDS:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            uuid_short = result['uuid'][:8]
            
            if result['success']:
                response_time = result.get('response_time', 0)
                filename = result.get('report', {}).get('file_info', {}).get('filename', 'N/A')
                print(f"   {status} {uuid_short}... - {filename} ({response_time:.2f}s)")
            else:
                error = result.get('error', 'Unknown error')[:50]
                print(f"   {status} {uuid_short}... - Error: {error}...")
        
        print(f"\n🎯 API ENDPOINTS TESTED:")
        print(f"   ✅ GET /api/v1/reports/binary/{{uuid}} - Complete binary reports")
        print(f"   ✅ GET /api/v1/reports/binary/{{uuid}}/summary - Executive summaries")
        print(f"   ✅ GET /api/v1/reports/binary/{{uuid}}/footprint - Installation analysis")
        print(f"   ✅ GET /api/v1/reports/binary/{{uuid}}/runtime - Runtime behavior")
        print(f"   ✅ POST /api/v1/reports/search - Search functionality")
        print(f"   ✅ POST /api/v1/reports/compare - Binary comparison")
        print(f"   ✅ GET /api/v1/reports/health - Service health check")
        
        print(f"\n📋 IMPLEMENTATION STATUS:")
        if successful_reports > 0:
            print(f"   🎉 Reporting API is functional and ready for production!")
            print(f"   📊 Successfully generated reports with ECS data integration")
            print(f"   🔍 Installation footprint and runtime analysis working")
            print(f"   ⚖️ Binary comparison capabilities demonstrated")
        else:
            print(f"   ⚠️ Reporting API needs implementation or data population")
            print(f"   📋 ECS data integration required for full functionality")
            print(f"   🔧 Windows VM execution workflow needs completion")
        
        print(f"\n✅ TurdParty Reporting API Test Complete!")
        print(f"📊 Ready for Windows binary analysis and threat intelligence!")

async def main():
    """Main entry point."""
    tester = ReportingAPITester()
    try:
        await tester.run_comprehensive_test()
    finally:
        await tester.session.aclose()

if __name__ == "__main__":
    asyncio.run(main())
