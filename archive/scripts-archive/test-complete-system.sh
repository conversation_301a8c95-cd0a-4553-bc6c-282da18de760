#!/bin/bash

# TurdParty Complete System Test
# Tests the entire workflow: API → MinIO → VM → ELK → Status Monitoring

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║           🚀 TurdParty Complete System Test                  ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to test service health
test_service_health() {
    local service_name="$1"
    local url="$2"
    local expected_status="$3"
    
    print_colored $BLUE "🔍 Testing: $service_name"
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [ "$status_code" = "200" ]; then
        print_colored $GREEN "   ✅ $service_name: Operational (HTTP $status_code)"
        return 0
    else
        print_colored $RED "   ❌ $service_name: Failed (HTTP $status_code)"
        return 1
    fi
}

# Function to test API endpoints
test_api_functionality() {
    print_colored $PURPLE "📡 Testing API Functionality:"
    echo ""
    
    # Test health endpoint
    test_service_health "API Health Check" "http://localhost:8000/health/" "200"
    
    # Test detailed health
    print_colored $BLUE "🔍 Testing: API Detailed Health"
    local health_response=$(curl -s http://localhost:8000/health/detailed)
    local api_status=$(echo "$health_response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    print_colored $YELLOW "   API Status: $api_status"
    
    # Test file listing
    test_service_health "File Listing Endpoint" "http://localhost:8000/api/v1/files/" "200"
    
    # Test workflow listing
    test_service_health "Workflow Listing Endpoint" "http://localhost:8000/api/v1/workflow/" "200"
    
    echo ""
}

# Function to test file upload and workflow
test_file_workflow() {
    print_colored $PURPLE "📁 Testing File Upload & Workflow:"
    echo ""
    
    # Create test file
    local test_file="/tmp/turdparty_test_$(date +%s).txt"
    echo "TurdParty System Test File - $(date)" > "$test_file"
    echo "This file tests the complete workflow:" >> "$test_file"
    echo "1. File Upload → MinIO Storage" >> "$test_file"
    echo "2. UUID Generation & Database Storage" >> "$test_file"
    echo "3. Workflow Orchestration" >> "$test_file"
    echo "4. VM Creation & File Injection" >> "$test_file"
    echo "5. ELK Data Collection" >> "$test_file"
    echo "6. 30-minute Runtime → Termination" >> "$test_file"
    
    print_colored $BLUE "📤 Uploading test file: $(basename $test_file)"
    
    # Upload file
    local upload_response=$(curl -s -X POST \
        -F "file=@$test_file" \
        -F "description=Complete system test file" \
        http://localhost:8000/api/v1/files/upload)
    
    if echo "$upload_response" | grep -q "file_id"; then
        local file_id=$(echo "$upload_response" | grep -o '"file_id":"[^"]*"' | cut -d'"' -f4)
        print_colored $GREEN "   ✅ File uploaded successfully"
        print_colored $YELLOW "   📋 File ID: $file_id"
        
        # Start workflow
        print_colored $BLUE "🔄 Starting workflow for uploaded file..."
        
        local workflow_response=$(curl -s -X POST \
            -F "file_id=$file_id" \
            -F "description=Complete system test workflow" \
            -F "vm_template=ubuntu/focal64" \
            -F "injection_path=/tmp/injected_test_file" \
            http://localhost:8000/api/v1/workflow/start)
        
        if echo "$workflow_response" | grep -q "workflow_job_id"; then
            local workflow_id=$(echo "$workflow_response" | grep -o '"workflow_job_id":"[^"]*"' | cut -d'"' -f4)
            print_colored $GREEN "   ✅ Workflow started successfully"
            print_colored $YELLOW "   🔧 Workflow ID: $workflow_id"
            
            # Check workflow status
            print_colored $BLUE "📊 Checking workflow status..."
            local status_response=$(curl -s "http://localhost:8000/api/v1/workflow/$workflow_id")
            local workflow_status=$(echo "$status_response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
            local current_step=$(echo "$status_response" | grep -o '"current_step":"[^"]*"' | cut -d'"' -f4)
            local progress=$(echo "$status_response" | grep -o '"progress":"[^"]*"' | cut -d'"' -f4)
            
            print_colored $YELLOW "   📈 Status: $workflow_status"
            print_colored $YELLOW "   🎯 Current Step: $current_step"
            print_colored $YELLOW "   📊 Progress: $progress%"
            
            echo ""
            print_colored $CYAN "🎉 Complete workflow test successful!"
            print_colored $CYAN "   The system is processing your file through the full pipeline:"
            print_colored $CYAN "   • File stored in MinIO with UUID"
            print_colored $CYAN "   • Workflow orchestration active"
            print_colored $CYAN "   • VM creation tasks queued"
            print_colored $CYAN "   • File injection scheduled"
            print_colored $CYAN "   • 30-minute monitoring planned"
            
        else
            print_colored $RED "   ❌ Workflow start failed"
            echo "$workflow_response"
        fi
        
    else
        print_colored $RED "   ❌ File upload failed"
        echo "$upload_response"
    fi
    
    # Clean up
    rm -f "$test_file"
    echo ""
}

# Function to test storage services
test_storage_services() {
    print_colored $PURPLE "💾 Testing Storage Services:"
    echo ""
    
    test_service_health "MinIO Storage" "http://localhost:9000/minio/health/live" "200"

    # Test PostgreSQL via Docker health check
    print_colored $BLUE "🔍 Testing: PostgreSQL Database"
    if docker ps --filter "name=turdpartycollab_database" --filter "health=healthy" | grep -q "healthy"; then
        print_colored $GREEN "   ✅ PostgreSQL Database: Operational (Docker Health Check)"
    else
        print_colored $YELLOW "   🟡 PostgreSQL Database: Starting or Unhealthy"
    fi

    # Test Redis via Docker health check
    print_colored $BLUE "🔍 Testing: Redis Cache"
    if docker ps --filter "name=turdpartycollab_cache" --filter "health=healthy" | grep -q "healthy"; then
        print_colored $GREEN "   ✅ Redis Cache: Operational (Docker Health Check)"
    else
        print_colored $YELLOW "   🟡 Redis Cache: Starting or Unhealthy"
    fi

    test_service_health "Elasticsearch" "http://localhost:9200/_cluster/health" "200"
    
    echo ""
}

# Function to test monitoring services
test_monitoring_services() {
    print_colored $PURPLE "📊 Testing Monitoring Services:"
    echo ""
    
    test_service_health "Status Dashboard" "http://localhost:8090/health" "200"
    
    # Test if Kibana is accessible (might be starting)
    local kibana_status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:5601" 2>/dev/null || echo "000")
    if [ "$kibana_status" = "200" ]; then
        print_colored $GREEN "   ✅ Kibana: Operational (HTTP $kibana_status)"
    else
        print_colored $YELLOW "   🟡 Kibana: Starting or Unavailable (HTTP $kibana_status)"
    fi
    
    echo ""
}

# Function to show system overview
show_system_overview() {
    print_colored $PURPLE "🏗️ TurdParty System Overview:"
    echo ""
    
    print_colored $GREEN "✅ Core Services (Operational):"
    print_colored $GREEN "   • FastAPI Service (Port 8000) - File upload & workflow orchestration"
    print_colored $GREEN "   • Status Dashboard (Port 8090) - Real-time system monitoring"
    print_colored $GREEN "   • MinIO Storage (Port 9000-9001) - Object storage with UUID"
    print_colored $GREEN "   • PostgreSQL Database (Port 5432) - Metadata & workflow state"
    print_colored $GREEN "   • Redis Cache (Port 6379) - Session & queue management"
    print_colored $GREEN "   • Elasticsearch (Port 9200) - Search & analytics engine"
    echo ""
    
    print_colored $YELLOW "🟡 Processing Services (Starting/Variable):"
    print_colored $YELLOW "   • Celery Workers - File processing, VM management, injection"
    print_colored $YELLOW "   • Kibana (Port 5601) - Data visualization dashboard"
    print_colored $YELLOW "   • Logstash - Data processing pipeline"
    print_colored $YELLOW "   • VM Monitor - Virtual machine health tracking"
    echo ""
    
    print_colored $CYAN "🔄 Workflow Pipeline:"
    print_colored $CYAN "   1. 📤 File Upload → MinIO Storage (UUID generated)"
    print_colored $CYAN "   2. 🗄️ Database Record → Workflow Job Creation"
    print_colored $CYAN "   3. 🖥️ VM Creation → Ubuntu/Windows instances"
    print_colored $CYAN "   4. 💉 File Injection → Target VM filesystem"
    print_colored $CYAN "   5. 👁️ Runtime Monitoring → Fibratus data collection"
    print_colored $CYAN "   6. 📊 ELK Pipeline → Data analysis & visualization"
    print_colored $CYAN "   7. ⏰ 30-minute Runtime → Automatic VM termination"
    echo ""
}

# Function to show access URLs
show_access_urls() {
    print_colored $PURPLE "🌐 Access URLs:"
    echo ""
    
    print_colored $CYAN "📊 Status Dashboard: http://localhost:8090"
    print_colored $CYAN "   • Real-time service monitoring with animated status lights"
    print_colored $CYAN "   • Interactive Mermaid architecture diagram"
    print_colored $CYAN "   • Service metrics and uptime statistics"
    echo ""
    
    print_colored $CYAN "🚀 API Documentation: http://localhost:8000/docs"
    print_colored $CYAN "   • Interactive Swagger UI for all endpoints"
    print_colored $CYAN "   • File upload and workflow management"
    print_colored $CYAN "   • Real-time workflow status tracking"
    echo ""
    
    print_colored $CYAN "🗄️ MinIO Console: http://localhost:9001"
    print_colored $CYAN "   • Object storage management interface"
    print_colored $CYAN "   • File browser with UUID-based organization"
    print_colored $CYAN "   • Credentials: minioadmin / minioadmin"
    echo ""
    
    print_colored $CYAN "📈 Elasticsearch: http://localhost:9200"
    print_colored $CYAN "   • Search and analytics engine"
    print_colored $CYAN "   • Cluster health and index management"
    print_colored $CYAN "   • Runtime data from VM monitoring"
    echo ""
    
    print_colored $CYAN "📊 Kibana: http://localhost:5601 (if running)"
    print_colored $CYAN "   • Data visualization and dashboard"
    print_colored $CYAN "   • ELK stack analysis interface"
    print_colored $CYAN "   • VM runtime data exploration"
    echo ""
}

# Main test function
main() {
    print_banner
    
    # Test core services
    test_api_functionality
    test_storage_services
    test_monitoring_services
    
    # Test complete workflow
    test_file_workflow
    
    # Show system overview
    show_system_overview
    show_access_urls
    
    print_colored $GREEN "🎉 TurdParty Complete System Test Finished!"
    print_colored $GREEN "The system is operational and ready for file analysis workflows."
    echo ""
}

# Run main function
main "$@"
