#!/usr/bin/env python3
"""
End-to-End TurdParty Workflow Test
Tests the complete workflow: Upload → VM Execution → ECS Data → Reports → Sphinx
"""

import asyncio
import json
import time
import uuid
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

import httpx
from elasticsearch import AsyncElasticsearch

class TurdPartyWorkflowTester:
    """Test the complete TurdParty workflow end-to-end."""
    
    def __init__(self):
        self.session = httpx.AsyncClient(timeout=60.0)
        self.es_client = AsyncElasticsearch(
            hosts=["http://elasticsearch.turdparty.localhost:9200"],
            timeout=30
        )
        
        # Test binaries
        self.test_binaries = {
            "notepadpp": {
                "filename": "npp.8.5.8.Installer.x64.exe",
                "uuid": "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb",
                "description": "Notepad++ 8.5.8 installer"
            },
            "git": {
                "filename": "Git-2.42.0-64-bit.exe", 
                "uuid": str(uuid.uuid4()),
                "description": "Git for Windows installer"
            }
        }
        
        self.test_results = {
            "upload": {},
            "vm_execution": {},
            "ecs_data": {},
            "api_reports": {},
            "sphinx_reports": {}
        }
    
    async def test_complete_workflow(self):
        """Run the complete end-to-end workflow test."""
        print("🚀 TurdParty End-to-End Workflow Test")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # Step 1: Test Infrastructure
            await self._test_infrastructure()
            
            # Step 2: Test File Upload (Mock)
            await self._test_file_upload()
            
            # Step 3: Test VM Execution (Mock)
            await self._test_vm_execution()
            
            # Step 4: Test ECS Data Generation
            await self._test_ecs_data_generation()
            
            # Step 5: Test API Reports
            await self._test_api_reports()
            
            # Step 6: Test Sphinx Reports
            await self._test_sphinx_reports()
            
            # Step 7: Generate Summary
            await self._generate_test_summary(start_time)
            
        except Exception as e:
            print(f"❌ Workflow test failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.session.aclose()
            await self.es_client.close()
    
    async def _test_infrastructure(self):
        """Test that all required services are available."""
        print("\n🔧 Testing Infrastructure...")
        
        services = {
            "API": "http://api.turdparty.localhost/health",
            "Elasticsearch": "http://elasticsearch.turdparty.localhost/_cluster/health",
            "Kibana": "http://kibana.turdparty.localhost/api/status"
        }
        
        for service, url in services.items():
            try:
                response = await self.session.get(url)
                if response.status_code in [200, 201]:
                    print(f"   ✅ {service}: Available")
                else:
                    print(f"   ⚠️ {service}: Status {response.status_code}")
            except Exception as e:
                print(f"   ❌ {service}: Failed - {e}")
    
    async def _test_file_upload(self):
        """Test file upload functionality (mock)."""
        print("\n📁 Testing File Upload...")
        
        for binary_name, binary_info in self.test_binaries.items():
            try:
                # Mock file upload by creating metadata
                upload_data = {
                    "filename": binary_info["filename"],
                    "file_uuid": binary_info["uuid"],
                    "file_size_bytes": 50 * 1024 * 1024,  # 50MB
                    "upload_timestamp": datetime.utcnow().isoformat() + "Z",
                    "description": binary_info["description"],
                    "blake3_hash": hashlib.blake2b(binary_info["filename"].encode()).hexdigest()[:64],
                    "sha256_hash": hashlib.sha256(binary_info["filename"].encode()).hexdigest(),
                    "md5_hash": hashlib.md5(binary_info["filename"].encode()).hexdigest()
                }
                
                self.test_results["upload"][binary_name] = upload_data
                print(f"   ✅ {binary_name}: Upload metadata created")
                
            except Exception as e:
                print(f"   ❌ {binary_name}: Upload failed - {e}")
    
    async def _test_vm_execution(self):
        """Test VM execution simulation."""
        print("\n🖥️ Testing VM Execution...")
        
        for binary_name, binary_info in self.test_binaries.items():
            try:
                # Mock VM execution results
                execution_data = {
                    "vm_id": str(uuid.uuid4()),
                    "execution_start": datetime.utcnow().isoformat() + "Z",
                    "execution_duration": 45.7,
                    "exit_code": 0,
                    "processes_spawned": 3,
                    "files_created": 15 if binary_name == "notepadpp" else 25,
                    "registry_keys_modified": 10 if binary_name == "notepadpp" else 18,
                    "network_connections": 0,
                    "status": "completed"
                }
                
                self.test_results["vm_execution"][binary_name] = execution_data
                print(f"   ✅ {binary_name}: VM execution simulated")
                
            except Exception as e:
                print(f"   ❌ {binary_name}: VM execution failed - {e}")
    
    async def _test_ecs_data_generation(self):
        """Test ECS data generation and indexing."""
        print("\n📊 Testing ECS Data Generation...")
        
        for binary_name, binary_info in self.test_binaries.items():
            try:
                events_generated = await self._generate_mock_ecs_events(binary_name, binary_info)
                
                self.test_results["ecs_data"][binary_name] = {
                    "events_generated": events_generated,
                    "index_pattern": f"turdparty-*-{datetime.now().strftime('%Y.%m.%d')}",
                    "status": "indexed"
                }
                
                print(f"   ✅ {binary_name}: {events_generated} ECS events generated")
                
            except Exception as e:
                print(f"   ❌ {binary_name}: ECS generation failed - {e}")
    
    async def _generate_mock_ecs_events(self, binary_name, binary_info):
        """Generate mock ECS events for a binary."""
        base_time = datetime.utcnow()
        vm_id = str(uuid.uuid4())
        events = []
        
        # Generate file creation events
        file_count = 15 if binary_name == "notepadpp" else 25
        for i in range(file_count):
            event = {
                "@timestamp": (base_time + timedelta(seconds=i * 2)).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["file"],
                    "type": ["creation"],
                    "action": "file_created",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "file": {
                    "path": f"C:\\Program Files\\{binary_name}\\file_{i}.exe",
                    "size": 1024 * (i + 1),
                    "type": "file"
                },
                "file_uuid": binary_info["uuid"],
                "vm_id": vm_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "file-creation", binary_name]
            }
            events.append(event)
        
        # Generate registry events
        registry_count = 10 if binary_name == "notepadpp" else 18
        for i in range(registry_count):
            event = {
                "@timestamp": (base_time + timedelta(seconds=(file_count + i) * 2)).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["configuration"],
                    "type": ["change"],
                    "action": "registry_key_created",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "registry": {
                    "key": f"HKEY_LOCAL_MACHINE\\SOFTWARE\\{binary_name}\\key_{i}",
                    "value": f"value_{i}"
                },
                "file_uuid": binary_info["uuid"],
                "vm_id": vm_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "registry-change", binary_name]
            }
            events.append(event)
        
        # Generate process events
        for i in range(3):
            event = {
                "@timestamp": (base_time + timedelta(seconds=(file_count + registry_count + i) * 2)).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["process"],
                    "type": ["start"],
                    "action": "process_start",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "process": {
                    "name": f"{binary_name}_process_{i}.exe",
                    "pid": 1000 + i,
                    "command_line": f"{binary_name}_process_{i}.exe /install"
                },
                "file_uuid": binary_info["uuid"],
                "vm_id": vm_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "process-execution", binary_name]
            }
            events.append(event)
        
        # Send events to Elasticsearch
        sent_count = 0
        for event in events:
            try:
                index = f"turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}"
                es_url = f"http://elasticsearch.turdparty.localhost/{index}/_doc"
                
                response = await self.session.post(
                    es_url,
                    json=event,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code in [200, 201]:
                    sent_count += 1
                    
            except Exception as e:
                print(f"   ⚠️ Failed to send event: {e}")
        
        return sent_count
    
    async def _test_api_reports(self):
        """Test API report generation."""
        print("\n📋 Testing API Reports...")
        
        # Wait for ECS data to be indexed
        await asyncio.sleep(5)
        
        for binary_name, binary_info in self.test_binaries.items():
            try:
                # Test if we can query the ECS data
                query = {
                    "query": {
                        "term": {"file_uuid.keyword": binary_info["uuid"]}
                    },
                    "size": 5
                }
                
                es_url = f"http://elasticsearch.turdparty.localhost/turdparty-*/_search"
                response = await self.session.post(es_url, json=query)
                
                if response.status_code == 200:
                    result = response.json()
                    hits = result.get("hits", {}).get("hits", [])
                    
                    self.test_results["api_reports"][binary_name] = {
                        "ecs_events_found": len(hits),
                        "total_events": result.get("hits", {}).get("total", {}).get("value", 0),
                        "status": "data_available"
                    }
                    
                    print(f"   ✅ {binary_name}: {len(hits)} ECS events available for reporting")
                else:
                    print(f"   ⚠️ {binary_name}: ECS query failed - {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {binary_name}: API report test failed - {e}")
    
    async def _test_sphinx_reports(self):
        """Test Sphinx report generation."""
        print("\n📚 Testing Sphinx Reports...")
        
        for binary_name, binary_info in self.test_binaries.items():
            try:
                # Check if Sphinx reports server is available
                sphinx_url = "http://localhost:8081"
                response = await self.session.get(sphinx_url)
                
                if response.status_code == 200:
                    self.test_results["sphinx_reports"][binary_name] = {
                        "platform_available": True,
                        "report_url": f"{sphinx_url}/reports/{binary_name}-analysis.html",
                        "status": "platform_ready"
                    }
                    
                    print(f"   ✅ {binary_name}: Sphinx platform available")
                else:
                    print(f"   ⚠️ {binary_name}: Sphinx platform not available")
                    
            except Exception as e:
                print(f"   ❌ {binary_name}: Sphinx test failed - {e}")
    
    async def _generate_test_summary(self, start_time):
        """Generate comprehensive test summary."""
        total_time = time.time() - start_time
        
        print(f"\n{'='*60}")
        print("📊 END-TO-END WORKFLOW TEST SUMMARY")
        print(f"{'='*60}")
        
        print(f"\n⏱️ EXECUTION TIME: {total_time:.1f} seconds")
        
        # Test Results Summary
        print(f"\n📋 TEST RESULTS:")
        
        for stage, results in self.test_results.items():
            print(f"\n   🔸 {stage.upper().replace('_', ' ')}:")
            
            if results:
                for binary_name, data in results.items():
                    status = data.get("status", "unknown")
                    if "available" in status or "completed" in status or "indexed" in status or "ready" in status:
                        print(f"      ✅ {binary_name}: {status}")
                    else:
                        print(f"      ⚠️ {binary_name}: {status}")
            else:
                print(f"      ❌ No results")
        
        # Detailed Metrics
        print(f"\n📈 DETAILED METRICS:")
        
        for binary_name in self.test_binaries.keys():
            print(f"\n   📁 {binary_name.upper()}:")
            
            # Upload metrics
            upload_data = self.test_results["upload"].get(binary_name, {})
            if upload_data:
                print(f"      📤 Upload: {upload_data.get('file_size_bytes', 0) // 1024 // 1024}MB")
            
            # VM execution metrics
            vm_data = self.test_results["vm_execution"].get(binary_name, {})
            if vm_data:
                print(f"      🖥️ VM Execution: {vm_data.get('execution_duration', 0)}s")
                print(f"      📊 Files Created: {vm_data.get('files_created', 0)}")
                print(f"      🔑 Registry Keys: {vm_data.get('registry_keys_modified', 0)}")
            
            # ECS data metrics
            ecs_data = self.test_results["ecs_data"].get(binary_name, {})
            if ecs_data:
                print(f"      📊 ECS Events: {ecs_data.get('events_generated', 0)}")
            
            # API report metrics
            api_data = self.test_results["api_reports"].get(binary_name, {})
            if api_data:
                print(f"      📋 API Events Found: {api_data.get('total_events', 0)}")
            
            # Sphinx report metrics
            sphinx_data = self.test_results["sphinx_reports"].get(binary_name, {})
            if sphinx_data:
                platform_status = "Available" if sphinx_data.get("platform_available") else "Unavailable"
                print(f"      📚 Sphinx Platform: {platform_status}")
        
        # Access URLs
        print(f"\n🌐 ACCESS URLS:")
        print(f"   📊 Kibana: http://kibana.turdparty.localhost/app/discover")
        print(f"   🔍 Elasticsearch: http://elasticsearch.turdparty.localhost/_cat/indices")
        print(f"   📋 API Health: http://api.turdparty.localhost/health")
        print(f"   📚 Sphinx Reports: http://localhost:8081")
        
        # Next Steps
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. ✅ Infrastructure is ready")
        print(f"   2. ✅ ECS data pipeline is working")
        print(f"   3. ✅ Mock data generation is functional")
        print(f"   4. 🔄 Implement real file upload endpoints")
        print(f"   5. 🔄 Implement real VM execution with Celery")
        print(f"   6. 🔄 Connect reporting API to ECS data")
        print(f"   7. 🔄 Generate real Sphinx reports from API data")
        
        print(f"\n🎉 END-TO-END WORKFLOW TEST COMPLETE!")
        
        # Save results to file
        results_file = f"/tmp/turdparty-e2e-test-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"📄 Test results saved: {results_file}")

async def main():
    """Main entry point."""
    tester = TurdPartyWorkflowTester()
    await tester.test_complete_workflow()

if __name__ == "__main__":
    asyncio.run(main())
