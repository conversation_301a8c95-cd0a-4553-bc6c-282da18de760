#!/usr/bin/env python3
"""
Generate Notepad++ Report with Mock ECS Data
Creates mock execution data and generates a comprehensive report.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.service_urls import ServiceURLManager
import httpx

class NotepadPPReportGenerator:
    """Generate comprehensive Notepad++ report with mock data."""
    
    def __init__(self):
        self.url_manager = ServiceURLManager('development')
        self.session = httpx.AsyncClient(timeout=60.0)
        self.notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
        
    async def generate_mock_ecs_data(self):
        """Generate mock ECS data for Notepad++ execution."""
        print("📊 Generating mock ECS data for Notepad++...")
        
        base_time = datetime.utcnow()
        vm_id = str(uuid.uuid4())
        workflow_job_id = str(uuid.uuid4())
        
        # Mock installation events
        events = []
        
        # File creation events
        file_events = [
            {"path": "C:\\Program Files\\Notepad++\\notepad++.exe", "size": 4567890},
            {"path": "C:\\Program Files\\Notepad++\\SciLexer.dll", "size": 1234567},
            {"path": "C:\\Program Files\\Notepad++\\langs.xml", "size": 45678},
            {"path": "C:\\Program Files\\Notepad++\\stylers.xml", "size": 23456},
            {"path": "C:\\Program Files\\Notepad++\\config.xml", "size": 12345},
            {"path": "C:\\Program Files\\Notepad++\\shortcuts.xml", "size": 8901},
            {"path": "C:\\Program Files\\Notepad++\\contextMenu.xml", "size": 5678},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\DSpellCheck.dll", "size": 234567},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\NppConverter.dll", "size": 123456},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\mimeTools.dll", "size": 98765},
            {"path": "C:\\Program Files\\Notepad++\\themes\\DarkModeDefault.xml", "size": 15432},
            {"path": "C:\\Program Files\\Notepad++\\autoCompletion\\c.xml", "size": 87654},
            {"path": "C:\\Program Files\\Notepad++\\autoCompletion\\python.xml", "size": 76543},
            {"path": "C:\\Program Files\\Notepad++\\localization\\english.xml", "size": 34567},
            {"path": "C:\\Users\\<USER>\\Desktop\\Notepad++.lnk", "size": 1024}
        ]
        
        # Registry events
        registry_events = [
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++", "value": ""},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++\\version", "value": "8.5.8"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++\\installPath", "value": "C:\\Program Files\\Notepad++"},
            {"key": "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\.txt\\OpenWithProgids\\Notepad++_file", "value": ""},
            {"key": "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\Applications\\notepad++.exe", "value": ""},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++", "value": ""},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\DisplayName", "value": "Notepad++ (64-bit x64)"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\DisplayVersion", "value": "8.5.8"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\Publisher", "value": "Notepad++ Team"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\InstallLocation", "value": "C:\\Program Files\\Notepad++\\"}
        ]
        
        # Process events
        process_events = [
            {"name": "npp.8.5.8.Installer.x64.exe", "pid": 1234, "command": "npp.8.5.8.Installer.x64.exe /S"},
            {"name": "msiexec.exe", "pid": 1567, "command": "msiexec.exe /i notepadpp.msi /quiet"},
            {"name": "notepad++.exe", "pid": 1890, "command": "notepad++.exe"}
        ]
        
        # Generate file events
        event_counter = 0
        for file_event in file_events:
            event_counter += 1
            timestamp = base_time + timedelta(seconds=event_counter * 2)
            
            ecs_event = {
                "@timestamp": timestamp.isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["file"],
                    "type": ["creation"],
                    "action": "file_created",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "file": {
                    "path": file_event["path"],
                    "size": file_event["size"],
                    "type": "file"
                },
                "file_uuid": self.notepadpp_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "file-creation", "notepad++"]
            }
            events.append(ecs_event)
        
        # Generate registry events
        for reg_event in registry_events:
            event_counter += 1
            timestamp = base_time + timedelta(seconds=event_counter * 2)
            
            ecs_event = {
                "@timestamp": timestamp.isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["configuration"],
                    "type": ["change"],
                    "action": "registry_key_created",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "registry": {
                    "key": reg_event["key"],
                    "value": reg_event["value"]
                },
                "file_uuid": self.notepadpp_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "registry-change", "notepad++"]
            }
            events.append(ecs_event)
        
        # Generate process events
        for proc_event in process_events:
            event_counter += 1
            timestamp = base_time + timedelta(seconds=event_counter * 2)
            
            ecs_event = {
                "@timestamp": timestamp.isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["process"],
                    "type": ["start"],
                    "action": "process_start",
                    "outcome": "success"
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring"
                },
                "process": {
                    "name": proc_event["name"],
                    "pid": proc_event["pid"],
                    "command_line": proc_event["command"]
                },
                "file_uuid": self.notepadpp_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "tags": ["installation", "process-execution", "notepad++"]
            }
            events.append(ecs_event)
        
        # Send events to Elasticsearch
        print(f"   📤 Sending {len(events)} events to Elasticsearch...")
        
        sent_count = 0
        for event in events:
            try:
                # Determine index based on event type
                if event["event"]["category"][0] in ["file", "configuration"]:
                    index = f"turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}"
                else:
                    index = f"turdparty-runtime-ecs-{datetime.now().strftime('%Y.%m.%d')}"
                
                es_url = f"http://elasticsearch.turdparty.localhost/{index}/_doc"
                
                response = await self.session.post(
                    es_url,
                    json=event,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code in [200, 201]:
                    sent_count += 1
                
            except Exception as e:
                print(f"   ⚠️ Failed to send event: {e}")
        
        print(f"   ✅ Successfully sent {sent_count}/{len(events)} events")
        
        return {
            "events_generated": len(events),
            "events_sent": sent_count,
            "vm_id": vm_id,
            "workflow_job_id": workflow_job_id
        }
    
    async def wait_for_indexing(self):
        """Wait for Elasticsearch to index the data."""
        print("\n⏳ Waiting for Elasticsearch indexing...")
        await asyncio.sleep(10)  # Wait for indexing
        
        # Check if data is available
        try:
            es_url = f"http://elasticsearch.turdparty.localhost/turdparty-*/_search"
            query = {
                "query": {
                    "term": {"file_uuid.keyword": self.notepadpp_uuid}
                },
                "size": 5
            }
            
            response = await self.session.post(es_url, json=query)
            
            if response.status_code == 200:
                result = response.json()
                hits = result.get("hits", {}).get("hits", [])
                
                print(f"   📊 Found {len(hits)} indexed events")
                
                if hits:
                    print("   ✅ ECS data is available for reporting!")
                    return True
                else:
                    print("   ⚠️ No ECS data found yet")
                    return False
            else:
                print(f"   ❌ Elasticsearch query failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ ECS data check failed: {e}")
            return False
    
    async def generate_report(self):
        """Generate the Notepad++ report using the reporting API."""
        print("\n📋 Generating Notepad++ comprehensive report...")
        
        try:
            report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=self.notepadpp_uuid)
            print(f"   📍 Report URL: {report_url}")
            
            response = await self.session.get(report_url)
            
            if response.status_code == 200:
                report = response.json()
                print("   ✅ Report generated successfully!")
                
                # Show key metrics
                file_info = report.get("file_info", {})
                execution_summary = report.get("execution_summary", {})
                footprint = report.get("installation_footprint", {})
                security = report.get("security_analysis", {})
                
                print(f"\n   📊 REPORT SUMMARY:")
                print(f"      📁 Filename: {file_info.get('filename', 'N/A')}")
                print(f"      📊 Executions: {execution_summary.get('total_executions', 0)}")
                print(f"      📈 Files Created: {footprint.get('filesystem_changes', {}).get('files_created', 0)}")
                print(f"      🔑 Registry Keys: {footprint.get('registry_changes', {}).get('keys_created', 0)}")
                print(f"      🔍 Risk Level: {security.get('threat_indicators', {}).get('risk_level', 'N/A')}")
                
                # Save report
                report_file = f"/tmp/notepadpp-final-report-{int(time.time())}.json"
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                print(f"      💾 Report saved: {report_file}")
                return report_file
            else:
                print(f"   ❌ Report generation failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Report generation exception: {e}")
            return None
    
    async def show_report_locations(self):
        """Show where to view the report."""
        print(f"\n{'='*80}")
        print("📍 WHERE TO VIEW NOTEPAD++ REPORT")
        print(f"{'='*80}")
        
        print(f"\n🌐 API ENDPOINTS:")
        report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=self.notepadpp_uuid)
        summary_url = self.url_manager.get_api_endpoint('reporting', 'binary_summary', file_uuid=self.notepadpp_uuid)
        footprint_url = self.url_manager.get_api_endpoint('reporting', 'installation_footprint', file_uuid=self.notepadpp_uuid)
        runtime_url = self.url_manager.get_api_endpoint('reporting', 'runtime_behavior', file_uuid=self.notepadpp_uuid)
        
        print(f"   📋 Complete Report:")
        print(f"      {report_url}")
        print(f"   📊 Executive Summary:")
        print(f"      {summary_url}")
        print(f"   👣 Installation Footprint:")
        print(f"      {footprint_url}")
        print(f"   ⚡ Runtime Behavior:")
        print(f"      {runtime_url}")
        
        print(f"\n🌐 WEB INTERFACES:")
        print(f"   📊 Kibana Analytics:")
        print(f"      http://kibana.turdparty.localhost/app/discover")
        print(f"   🔍 Elasticsearch Direct:")
        print(f"      http://elasticsearch.turdparty.localhost/turdparty-*/_search")
        
        print(f"\n💻 COMMAND LINE ACCESS:")
        print(f"   # View complete report")
        print(f"   curl -s \"{report_url}\" | jq .")
        print(f"   ")
        print(f"   # View summary only")
        print(f"   curl -s \"{summary_url}\" | jq .")
        print(f"   ")
        print(f"   # Search ECS data")
        print(f"   curl -s \"http://elasticsearch.turdparty.localhost/turdparty-*/_search\" \\")
        print(f"     -H \"Content-Type: application/json\" \\")
        print(f"     -d '{{\"query\": {{\"term\": {{\"file_uuid.keyword\": \"{self.notepadpp_uuid}\"}}}}}}'")
    
    async def run_complete_workflow(self):
        """Run the complete workflow to generate Notepad++ report."""
        print("🚀 TurdParty Notepad++ Report Generation")
        print(f"📅 Started at: {datetime.now()}")
        print(f"🎯 Target UUID: {self.notepadpp_uuid}")
        print("=" * 80)
        
        workflow_start = time.time()
        
        try:
            # Step 1: Generate mock ECS data
            ecs_result = await self.generate_mock_ecs_data()
            
            # Step 2: Wait for indexing
            data_available = await self.wait_for_indexing()
            
            # Step 3: Generate report
            report_file = await self.generate_report()
            
            # Step 4: Show report locations
            await self.show_report_locations()
            
            # Final summary
            total_time = time.time() - workflow_start
            
            print(f"\n{'='*80}")
            print("📊 NOTEPAD++ REPORT GENERATION COMPLETE")
            print(f"{'='*80}")
            
            print(f"\n⏱️ TIMING:")
            print(f"   Total Time: {total_time:.1f} seconds")
            
            print(f"\n🎯 RESULTS:")
            print(f"   ✅ ECS Events Generated: {ecs_result.get('events_generated', 0)}")
            print(f"   ✅ Events Indexed: {ecs_result.get('events_sent', 0)}")
            print(f"   ✅ Data Available: {'Yes' if data_available else 'No'}")
            print(f"   ✅ Report Generated: {'Yes' if report_file else 'No'}")
            
            if report_file:
                print(f"\n📄 REPORT FILE: {report_file}")
            
            print(f"\n✅ Notepad++ Report Generation Complete!")
            print(f"🎉 Ready for comprehensive binary analysis!")
            
        except Exception as e:
            print(f"\n❌ Workflow failed: {e}")
        finally:
            await self.session.aclose()

async def main():
    """Main entry point."""
    generator = NotepadPPReportGenerator()
    await generator.run_complete_workflow()

if __name__ == "__main__":
    asyncio.run(main())
