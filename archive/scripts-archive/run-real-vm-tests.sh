#!/bin/bash

# Real VM Integration Tests Runner
# Executes comprehensive tests on actual Docker containers and Vagrant VMs

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
TEST_DIR="tests/integration"
LOG_DIR="/tmp/turdparty_vm_tests"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="${LOG_DIR}/vm_tests_${TIMESTAMP}.log"

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}" | tee -a "${LOG_FILE}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║           🧪 Real VM Integration Tests Runner               ║"
    print_colored $CYAN "║              No Mocks - Real VMs Only                       ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_colored $PURPLE "🔍 Checking Prerequisites:"
    echo ""
    
    local all_good=true
    
    # Check Python and pytest
    if command -v python3 >/dev/null 2>&1; then
        local python_version=$(python3 --version)
        print_colored $GREEN "✅ Python: $python_version"
    else
        print_colored $RED "❌ Python3 not found"
        all_good=false
    fi
    
    if python3 -c "import pytest" 2>/dev/null; then
        local pytest_version=$(python3 -c "import pytest; print(pytest.__version__)")
        print_colored $GREEN "✅ Pytest: $pytest_version"
    else
        print_colored $RED "❌ Pytest not available"
        all_good=false
    fi
    
    # Check Docker
    if command -v docker >/dev/null 2>&1; then
        if docker info >/dev/null 2>&1; then
            local docker_version=$(docker --version)
            print_colored $GREEN "✅ Docker: $docker_version"
        else
            print_colored $RED "❌ Docker daemon not running"
            all_good=false
        fi
    else
        print_colored $YELLOW "⚠️ Docker not found (Docker tests will be skipped)"
    fi
    
    # Check Vagrant
    if command -v vagrant >/dev/null 2>&1; then
        local vagrant_version=$(vagrant --version)
        print_colored $GREEN "✅ Vagrant: $vagrant_version"
    else
        print_colored $YELLOW "⚠️ Vagrant not found (Vagrant tests will be skipped)"
    fi
    
    # Check VirtualBox
    if command -v VBoxManage >/dev/null 2>&1; then
        local vbox_version=$(VBoxManage --version)
        print_colored $GREEN "✅ VirtualBox: $vbox_version"
    else
        print_colored $YELLOW "⚠️ VirtualBox not found (Vagrant tests will be skipped)"
    fi
    
    # Check API availability
    if curl -s http://localhost:8000/health/ >/dev/null 2>&1; then
        print_colored $GREEN "✅ TurdParty API: Available"
    else
        print_colored $RED "❌ TurdParty API not available at http://localhost:8000"
        print_colored $RED "   Please start the TurdParty services first"
        all_good=false
    fi
    
    echo ""
    
    if [ "$all_good" = false ]; then
        print_colored $RED "❌ Prerequisites check failed. Please fix the issues above."
        exit 1
    fi
    
    print_colored $GREEN "✅ All prerequisites satisfied!"
    echo ""
}

# Function to install test dependencies
install_dependencies() {
    print_colored $PURPLE "📦 Installing Test Dependencies:"
    echo ""
    
    # Install Python dependencies
    print_colored $BLUE "🐍 Installing Python packages..."
    
    pip3 install --user pytest httpx docker pyyaml 2>&1 | tee -a "${LOG_FILE}"
    
    if [ $? -eq 0 ]; then
        print_colored $GREEN "✅ Python dependencies installed"
    else
        print_colored $RED "❌ Failed to install Python dependencies"
        exit 1
    fi
    
    echo ""
}

# Function to run Docker VM tests
run_docker_tests() {
    print_colored $PURPLE "🐳 Running Docker VM Tests:"
    echo ""
    
    if ! command -v docker >/dev/null 2>&1 || ! docker info >/dev/null 2>&1; then
        print_colored $YELLOW "⚠️ Docker not available, skipping Docker tests"
        return 0
    fi
    
    print_colored $BLUE "🔧 Running Docker integration tests..."
    
    # Run Docker VM tests
    python3 -m pytest "${TEST_DIR}/test_real_vm_operations.py" \
        -v -s --tb=short \
        --junitxml="${LOG_DIR}/docker_tests_${TIMESTAMP}.xml" \
        2>&1 | tee -a "${LOG_FILE}"
    
    local docker_exit_code=$?
    
    if [ $docker_exit_code -eq 0 ]; then
        print_colored $GREEN "✅ Docker VM tests passed"
    else
        print_colored $RED "❌ Docker VM tests failed (exit code: $docker_exit_code)"
    fi
    
    echo ""
    return $docker_exit_code
}

# Function to run Vagrant VM tests
run_vagrant_tests() {
    print_colored $PURPLE "📦 Running Vagrant VM Tests:"
    echo ""
    
    if ! command -v vagrant >/dev/null 2>&1 || ! command -v VBoxManage >/dev/null 2>&1; then
        print_colored $YELLOW "⚠️ Vagrant or VirtualBox not available, skipping Vagrant tests"
        return 0
    fi
    
    print_colored $BLUE "🔧 Running Vagrant integration tests..."
    
    # Run Vagrant VM tests (excluding Windows tests by default)
    python3 -m pytest "${TEST_DIR}/test_real_vagrant_vms.py" \
        -v -s --tb=short \
        -m "not windows" \
        --junitxml="${LOG_DIR}/vagrant_tests_${TIMESTAMP}.xml" \
        2>&1 | tee -a "${LOG_FILE}"
    
    local vagrant_exit_code=$?
    
    if [ $vagrant_exit_code -eq 0 ]; then
        print_colored $GREEN "✅ Vagrant VM tests passed"
    else
        print_colored $RED "❌ Vagrant VM tests failed (exit code: $vagrant_exit_code)"
    fi
    
    echo ""
    return $vagrant_exit_code
}

# Function to run Windows VM tests (optional)
run_windows_tests() {
    print_colored $PURPLE "🪟 Running Windows VM Tests (Optional):"
    echo ""
    
    if ! command -v vagrant >/dev/null 2>&1; then
        print_colored $YELLOW "⚠️ Vagrant not available, skipping Windows tests"
        return 0
    fi
    
    # Check if Windows boxes are available
    if ! vagrant box list | grep -q "windows\|Windows"; then
        print_colored $YELLOW "⚠️ No Windows Vagrant boxes found, skipping Windows tests"
        print_colored $YELLOW "   To run Windows tests, add a Windows box:"
        print_colored $YELLOW "   vagrant box add gusztavvargadr/windows-10"
        return 0
    fi
    
    print_colored $BLUE "🔧 Running Windows VM tests..."
    
    # Run Windows VM tests
    python3 -m pytest "${TEST_DIR}/test_real_vagrant_vms.py" \
        -v -s --tb=short \
        -m "windows" \
        --junitxml="${LOG_DIR}/windows_tests_${TIMESTAMP}.xml" \
        2>&1 | tee -a "${LOG_FILE}"
    
    local windows_exit_code=$?
    
    if [ $windows_exit_code -eq 0 ]; then
        print_colored $GREEN "✅ Windows VM tests passed"
    else
        print_colored $RED "❌ Windows VM tests failed (exit code: $windows_exit_code)"
    fi
    
    echo ""
    return $windows_exit_code
}

# Function to run performance tests
run_performance_tests() {
    print_colored $PURPLE "⚡ Running VM Performance Tests:"
    echo ""
    
    print_colored $BLUE "🏃 Testing VM creation speed..."
    
    # Time Docker VM creation
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        local start_time=$(date +%s)
        
        # Create a simple Docker VM
        local vm_response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d '{"name":"perf-test-'$(date +%s)'","template":"alpine:latest","vm_type":"docker","memory_mb":256,"cpus":1,"domain":"TurdParty"}' \
            http://localhost:8000/api/v1/vms/)
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        if echo "$vm_response" | grep -q "vm_id"; then
            print_colored $GREEN "✅ Docker VM creation: ${duration}s"
            
            # Cleanup
            local vm_id=$(echo "$vm_response" | grep -o '"vm_id":"[^"]*"' | cut -d'"' -f4)
            curl -s -X DELETE "http://localhost:8000/api/v1/vms/${vm_id}?force=true" >/dev/null
        else
            print_colored $RED "❌ Docker VM performance test failed"
        fi
    fi
    
    echo ""
}

# Function to generate test report
generate_report() {
    print_colored $PURPLE "📊 Generating Test Report:"
    echo ""
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    local skipped_tests=0
    
    # Count test results from log
    if [ -f "${LOG_FILE}" ]; then
        passed_tests=$(grep -c "PASSED" "${LOG_FILE}" 2>/dev/null || echo "0")
        failed_tests=$(grep -c "FAILED" "${LOG_FILE}" 2>/dev/null || echo "0")
        skipped_tests=$(grep -c "SKIPPED" "${LOG_FILE}" 2>/dev/null || echo "0")
        total_tests=$((passed_tests + failed_tests + skipped_tests))
    fi
    
    print_colored $GREEN "✅ Passed: $passed_tests"
    print_colored $RED "❌ Failed: $failed_tests"
    print_colored $YELLOW "⏭️ Skipped: $skipped_tests"
    print_colored $CYAN "📋 Total: $total_tests"
    
    if [ $total_tests -gt 0 ]; then
        local success_rate=$((passed_tests * 100 / total_tests))
        print_colored $CYAN "📈 Success Rate: ${success_rate}%"
    fi
    
    echo ""
    print_colored $CYAN "📄 Full test log: $LOG_FILE"
    print_colored $CYAN "📁 Test artifacts: $LOG_DIR"
    echo ""
}

# Function to cleanup
cleanup() {
    print_colored $PURPLE "🧹 Cleaning up test resources..."
    
    # Kill any hanging processes
    pkill -f "vagrant" 2>/dev/null || true
    pkill -f "VBoxHeadless" 2>/dev/null || true
    
    # Clean up Docker containers
    docker ps -a --filter "label=turdparty.vm" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
    
    print_colored $GREEN "✅ Cleanup completed"
    echo ""
}

# Main function
main() {
    # Create log directory
    mkdir -p "${LOG_DIR}"
    
    # Initialize log
    echo "Real VM Integration Tests - $(date)" > "${LOG_FILE}"
    
    print_banner
    
    # Check prerequisites
    check_prerequisites
    
    # Install dependencies
    install_dependencies
    
    # Run test suites
    local docker_result=0
    local vagrant_result=0
    local windows_result=0
    
    # Docker tests
    run_docker_tests
    docker_result=$?
    
    # Vagrant tests
    run_vagrant_tests
    vagrant_result=$?
    
    # Performance tests
    run_performance_tests
    
    # Optional Windows tests
    if [ "${RUN_WINDOWS_TESTS:-false}" = "true" ]; then
        run_windows_tests
        windows_result=$?
    fi
    
    # Generate report
    generate_report
    
    # Cleanup
    cleanup
    
    # Final result
    local overall_result=0
    if [ $docker_result -ne 0 ] || [ $vagrant_result -ne 0 ] || [ $windows_result -ne 0 ]; then
        overall_result=1
        print_colored $RED "❌ Some tests failed. Check the log for details."
    else
        print_colored $GREEN "🎉 All VM integration tests completed successfully!"
    fi
    
    print_colored $GREEN "Real VM testing complete - actual containers and VMs tested!"
    echo ""
    
    exit $overall_result
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --windows)
            export RUN_WINDOWS_TESTS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [--windows] [--help]"
            echo ""
            echo "Options:"
            echo "  --windows    Include Windows VM tests (requires Windows Vagrant box)"
            echo "  --help       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
