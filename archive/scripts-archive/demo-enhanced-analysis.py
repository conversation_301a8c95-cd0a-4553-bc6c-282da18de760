#!/usr/bin/env python3
"""
Demo script for enhanced analysis with installer/runtime distinction and artifact collection.
"""

import json
import time
import uuid
from datetime import datetime
from pathlib import Path

class EnhancedAnalysisDemo:
    """Demonstrate enhanced analysis capabilities."""
    
    def __init__(self):
        self.demo_uuid = str(uuid.uuid4())
        self.binary_name = "notepadpp-enhanced.exe"
        
    def simulate_enhanced_analysis(self) -> dict:
        """Simulate complete enhanced analysis workflow."""
        
        print("🚀 Enhanced Binary Analysis Demo")
        print("=" * 60)
        print(f"Binary: {self.binary_name}")
        print(f"UUID: {self.demo_uuid}")
        print("=" * 60)
        
        # Phase 1: Installation Wizard Verification
        print("\n📋 Phase 1: Installation Wizard Verification")
        installation_verification = self._simulate_installation_verification()
        
        # Phase 2: Installer vs Runtime Footprint Distinction
        print("\n🔄 Phase 2: Installer vs Runtime Footprint Analysis")
        footprint_analysis = self._simulate_footprint_analysis()
        
        # Phase 3: Artifact Collection and MinIO Upload
        print("\n📦 Phase 3: Artifact Collection and Upload")
        artifact_collection = self._simulate_artifact_collection()
        
        # Generate comprehensive report
        enhanced_report = self._generate_enhanced_report(
            installation_verification, footprint_analysis, artifact_collection
        )
        
        return enhanced_report
    
    def _simulate_installation_verification(self) -> dict:
        """Simulate installation wizard completion verification."""
        
        print("   🔍 Verifying installation wizard completion...")
        
        # Simulate installation monitoring
        installation_phases = {
            "pre_install": {
                "duration_seconds": 5.2,
                "baseline_captured": True,
                "files_before": 125430,
                "registry_keys_before": 8945
            },
            "installer_execution": {
                "duration_seconds": 12.3,
                "exit_code": 0,
                "installer_command": "notepadpp-enhanced.exe /S",
                "stdout": "Installation completed successfully",
                "stderr": "",
                "wizard_completed": True
            },
            "post_install": {
                "duration_seconds": 3.1,
                "verification_checks": [
                    {"check": "installation_artifacts", "passed": True, "artifacts_found": 18},
                    {"check": "registry_entries", "passed": True, "entries_found": 8},
                    {"check": "installed_processes", "passed": True, "processes_found": 1},
                    {"check": "file_integrity", "passed": True, "integrity_verified": True}
                ],
                "files_after": 125448,
                "registry_keys_after": 8953
            }
        }
        
        verification_result = {
            "installation_successful": True,
            "wizard_completion_verified": True,
            "installer_exit_code": 0,
            "installation_duration_seconds": 20.6,
            "verification_checks_passed": 4,
            "verification_checks_total": 4,
            "installation_phases": installation_phases,
            "installation_footprint": {
                "files_added": 18,
                "registry_keys_added": 8,
                "processes_spawned": 1,
                "services_added": 0
            }
        }
        
        print(f"   ✅ Installation verified: {verification_result['wizard_completion_verified']}")
        print(f"   📊 Exit code: {verification_result['installer_exit_code']}")
        print(f"   ⏱️ Duration: {verification_result['installation_duration_seconds']}s")
        print(f"   📁 Files added: {verification_result['installation_footprint']['files_added']}")
        
        return verification_result
    
    def _simulate_footprint_analysis(self) -> dict:
        """Simulate installer vs runtime footprint distinction."""
        
        print("   🔍 Analyzing installer vs runtime footprints...")
        
        # Installer footprint (what installation creates)
        installer_footprint = {
            "phase_type": "INSTALLATION",
            "duration_seconds": 20.6,
            "elk_index": f"turdparty-installer-{self.demo_uuid}",
            "changes": {
                "files_created": 18,
                "registry_keys_added": 8,
                "services_installed": 0,
                "shortcuts_created": 3,
                "uninstall_entries": 1
            },
            "artifacts": [
                "C:\\Program Files\\Notepad++\\notepad++.exe",
                "C:\\Program Files\\Notepad++\\SciLexer.dll",
                "C:\\Program Files\\Notepad++\\config.xml",
                "C:\\Users\\<USER>\\Desktop\\Notepad++.lnk"
            ],
            "elk_events": 156
        }
        
        # Runtime footprint (what running the app creates/modifies)
        runtime_footprint = {
            "phase_type": "RUNTIME",
            "duration_seconds": 45.3,
            "elk_index": f"turdparty-runtime-{self.demo_uuid}",
            "changes": {
                "files_modified": 3,
                "files_created": 2,
                "registry_keys_modified": 5,
                "temp_files_created": 1,
                "user_data_created": 2
            },
            "artifacts": [
                "C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\config.xml",
                "C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\session.xml",
                "C:\\temp\\npp_temp_file.tmp"
            ],
            "elk_events": 89
        }
        
        footprint_comparison = {
            "installer_impact": sum(installer_footprint["changes"].values()),
            "runtime_impact": sum(runtime_footprint["changes"].values()),
            "primary_impact_phase": "installer",
            "installer_elk_events": installer_footprint["elk_events"],
            "runtime_elk_events": runtime_footprint["elk_events"],
            "total_elk_events": installer_footprint["elk_events"] + runtime_footprint["elk_events"]
        }
        
        print(f"   📊 Installer impact: {footprint_comparison['installer_impact']} changes")
        print(f"   🔄 Runtime impact: {footprint_comparison['runtime_impact']} changes")
        print(f"   📈 Primary impact: {footprint_comparison['primary_impact_phase']}")
        print(f"   📋 Total ELK events: {footprint_comparison['total_elk_events']}")
        
        return {
            "installer_footprint": installer_footprint,
            "runtime_footprint": runtime_footprint,
            "comparison": footprint_comparison
        }
    
    def _simulate_artifact_collection(self) -> dict:
        """Simulate artifact collection and MinIO upload."""
        
        print("   🔍 Collecting and uploading artifacts...")
        
        # Simulate artifact collection
        installer_artifacts = [
            {
                "filename": "notepad++.exe",
                "type": "installer_file",
                "size_bytes": 8945672,
                "hash": "a1b2c3d4e5f6...",
                "path": "C:\\Program Files\\Notepad++\\notepad++.exe"
            },
            {
                "filename": "registry_export_installer.reg",
                "type": "registry_export",
                "size_bytes": 2048,
                "hash": "f6e5d4c3b2a1...",
                "path": "C:\\temp\\registry_export_installer.reg"
            }
        ]
        
        runtime_artifacts = [
            {
                "filename": "config.xml",
                "type": "runtime_file",
                "size_bytes": 1024,
                "hash": "b2c3d4e5f6a1...",
                "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\config.xml"
            },
            {
                "filename": "session.xml",
                "type": "runtime_file",
                "size_bytes": 512,
                "hash": "c3d4e5f6a1b2...",
                "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\session.xml"
            }
        ]
        
        system_artifacts = [
            {
                "filename": "event_log_export.evtx",
                "type": "system_log",
                "size_bytes": 4096,
                "hash": "d4e5f6a1b2c3...",
                "path": "C:\\temp\\event_log_export.evtx"
            }
        ]
        
        # Simulate MinIO upload
        upload_results = []
        all_artifacts = installer_artifacts + runtime_artifacts + system_artifacts
        
        for i, artifact in enumerate(all_artifacts):
            phase = "installer" if artifact in installer_artifacts else "runtime" if artifact in runtime_artifacts else "system"
            
            upload_result = {
                "artifact_index": i,
                "filename": artifact["filename"],
                "bucket": "turdparty-artifacts",
                "object_key": f"{self.demo_uuid}/artifacts/{phase}/{artifact['filename']}",
                "upload_success": True,
                "file_size": artifact["size_bytes"],
                "upload_timestamp": datetime.utcnow().isoformat()
            }
            upload_results.append(upload_result)
        
        collection_result = {
            "success": True,
            "total_artifacts": len(all_artifacts),
            "installer_artifacts": len(installer_artifacts),
            "runtime_artifacts": len(runtime_artifacts),
            "system_artifacts": len(system_artifacts),
            "upload_results": upload_results,
            "minio_bucket": "turdparty-artifacts",
            "artifact_base_path": f"{self.demo_uuid}/artifacts/"
        }
        
        print(f"   📦 Total artifacts: {collection_result['total_artifacts']}")
        print(f"   📁 Installer artifacts: {collection_result['installer_artifacts']}")
        print(f"   🔄 Runtime artifacts: {collection_result['runtime_artifacts']}")
        print(f"   🖥️ System artifacts: {collection_result['system_artifacts']}")
        print(f"   ☁️ MinIO bucket: {collection_result['minio_bucket']}")
        
        return collection_result
    
    def _generate_enhanced_report(self, installation_verification: dict, 
                                footprint_analysis: dict, artifact_collection: dict) -> dict:
        """Generate comprehensive enhanced analysis report."""
        
        print("\n📊 ENHANCED ANALYSIS SUMMARY")
        print("=" * 60)
        
        enhanced_report = {
            "demo_metadata": {
                "binary_name": self.binary_name,
                "demo_uuid": self.demo_uuid,
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "enhanced_features": [
                    "Installation wizard completion verification",
                    "Installer vs runtime footprint distinction",
                    "Comprehensive artifact collection",
                    "MinIO artifact storage with metadata",
                    "Separate ELK indices for installer/runtime"
                ]
            },
            "installation_verification": installation_verification,
            "footprint_analysis": footprint_analysis,
            "artifact_collection": artifact_collection,
            "api_endpoints": {
                "installation_verification": f"/api/v1/enhanced-analysis/installation-verification/{self.demo_uuid}",
                "phase_breakdown": f"/api/v1/enhanced-analysis/phase-breakdown/{self.demo_uuid}",
                "artifacts_info": f"/api/v1/enhanced-analysis/artifacts/{self.demo_uuid}",
                "download_urls": f"/api/v1/enhanced-analysis/artifacts/{self.demo_uuid}/download-urls",
                "footprint_comparison": f"/api/v1/enhanced-analysis/footprint-comparison/{self.demo_uuid}",
                "elk_indices": f"/api/v1/enhanced-analysis/elk-indices/{self.demo_uuid}",
                "summary": f"/api/v1/enhanced-analysis/summary/{self.demo_uuid}"
            },
            "elk_access": {
                "installer_index": f"turdparty-installer-{self.demo_uuid}",
                "runtime_index": f"turdparty-runtime-{self.demo_uuid}",
                "elasticsearch_url": "http://elasticsearch.turdparty.localhost",
                "kibana_url": "http://kibana.turdparty.localhost"
            },
            "minio_access": {
                "bucket": "turdparty-artifacts",
                "base_path": f"{self.demo_uuid}/artifacts/",
                "minio_console": "http://minio.turdparty.localhost"
            }
        }
        
        # Print summary
        print(f"Binary: {self.binary_name}")
        print(f"UUID: {self.demo_uuid}")
        print(f"Installation Verified: ✅ {installation_verification['wizard_completion_verified']}")
        print(f"Installer Footprint: {footprint_analysis['comparison']['installer_impact']} changes")
        print(f"Runtime Footprint: {footprint_analysis['comparison']['runtime_impact']} changes")
        print(f"Artifacts Collected: {artifact_collection['total_artifacts']}")
        print(f"ELK Events: {footprint_analysis['comparison']['total_elk_events']}")
        
        print(f"\n🌐 Access URLs:")
        print(f"   📊 API Summary: http://api.turdparty.localhost{enhanced_report['api_endpoints']['summary']}")
        print(f"   📋 Kibana Installer: {enhanced_report['elk_access']['kibana_url']}/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),index:'{enhanced_report['elk_access']['installer_index']}',interval:auto,query:(language:kuery,query:''),sort:!())")
        print(f"   🔄 Kibana Runtime: {enhanced_report['elk_access']['kibana_url']}/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),index:'{enhanced_report['elk_access']['runtime_index']}',interval:auto,query:(language:kuery,query:''),sort:!())")
        print(f"   📦 MinIO Artifacts: {enhanced_report['minio_access']['minio_console']}/browser/{enhanced_report['minio_access']['bucket']}/{enhanced_report['minio_access']['base_path']}")
        
        return enhanced_report
    
    def save_demo_results(self, enhanced_report: dict):
        """Save demo results to file."""
        
        timestamp = int(time.time())
        results_file = f"/tmp/enhanced-analysis-demo-{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(enhanced_report, f, indent=2, default=str)
        
        print(f"\n📄 Demo results saved: {results_file}")
        return results_file

def main():
    """Main demo execution."""
    print("🚀 Enhanced Binary Analysis Demo")
    print("Demonstrating installer wizard verification, footprint distinction, and artifact collection")
    print("=" * 80)
    
    demo = EnhancedAnalysisDemo()
    
    # Run enhanced analysis simulation
    enhanced_report = demo.simulate_enhanced_analysis()
    
    # Save results
    demo.save_demo_results(enhanced_report)
    
    print(f"\n🎉 Enhanced analysis demo complete!")
    print(f"\n💡 Key Improvements Demonstrated:")
    print(f"   ✅ Installation wizard completion verification with exit code monitoring")
    print(f"   🔄 Clear separation of installer vs runtime footprints")
    print(f"   📦 Comprehensive artifact collection with MinIO storage")
    print(f"   📊 Separate ELK indices for installer and runtime data")
    print(f"   🌐 Rich API endpoints for accessing enhanced analysis data")

if __name__ == "__main__":
    main()
