#!/bin/bash

# VM WebSocket Testing Script
# This script starts the API server and runs WebSocket tests

set -e

echo "🚀 Starting VM WebSocket Tests"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_PORT=8000
API_HOST="localhost"
TEST_TIMEOUT=60

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if API is running
check_api_health() {
    local max_attempts=30
    local attempt=1
    
    print_status "Checking API health..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://${API_HOST}:${API_PORT}/health" > /dev/null 2>&1; then
            print_success "API is healthy and responding"
            return 0
        fi
        
        print_status "Waiting for API to start... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "API failed to start within timeout"
    return 1
}

# Function to start API server
start_api_server() {
    print_status "Starting API server..."
    
    # Check if API is already running
    if curl -s "http://${API_HOST}:${API_PORT}/health" > /dev/null 2>&1; then
        print_warning "API server is already running"
        return 0
    fi
    
    # Start API server in background
    cd "$(dirname "$0")/.."
    
    # Install dependencies if needed
    if [ ! -d "venv" ]; then
        print_status "Creating virtual environment..."
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
    else
        source venv/bin/activate
    fi
    
    # Start the server
    print_status "Launching FastAPI server..."
    uvicorn api.main:app --host 0.0.0.0 --port $API_PORT --reload &
    API_PID=$!
    
    # Wait for server to start
    if check_api_health; then
        print_success "API server started successfully (PID: $API_PID)"
        return 0
    else
        print_error "Failed to start API server"
        kill $API_PID 2>/dev/null || true
        return 1
    fi
}

# Function to run WebSocket tests
run_websocket_tests() {
    print_status "Running WebSocket tests..."
    
    cd "$(dirname "$0")/.."
    source venv/bin/activate
    
    # Install test dependencies
    pip install websockets httpx > /dev/null 2>&1
    
    # Run the test script
    python3 scripts/test-vm-websockets.py --base-url "ws://${API_HOST}:${API_PORT}"
    local test_result=$?
    
    if [ $test_result -eq 0 ]; then
        print_success "All WebSocket tests passed!"
        return 0
    else
        print_error "Some WebSocket tests failed"
        return 1
    fi
}

# Function to run basic API tests
run_api_tests() {
    print_status "Running basic API tests..."
    
    # Test health endpoint
    if curl -s "http://${API_HOST}:${API_PORT}/health" | grep -q "healthy"; then
        print_success "Health endpoint test passed"
    else
        print_error "Health endpoint test failed"
        return 1
    fi
    
    # Test VM templates endpoint
    if curl -s "http://${API_HOST}:${API_PORT}/api/v1/vms/templates" | grep -q "ubuntu"; then
        print_success "VM templates endpoint test passed"
    else
        print_error "VM templates endpoint test failed"
        return 1
    fi
    
    # Test VM list endpoint
    if curl -s "http://${API_HOST}:${API_PORT}/api/v1/vms/" | grep -q "vms"; then
        print_success "VM list endpoint test passed"
    else
        print_error "VM list endpoint test failed"
        return 1
    fi
    
    print_success "All basic API tests passed!"
    return 0
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    
    if [ ! -z "$API_PID" ]; then
        print_status "Stopping API server (PID: $API_PID)..."
        kill $API_PID 2>/dev/null || true
        wait $API_PID 2>/dev/null || true
        print_success "API server stopped"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --api-only          Only test basic API endpoints"
    echo "  --websocket-only    Only test WebSocket functionality"
    echo "  --no-start-api      Don't start API server (assume it's already running)"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all tests (start API + basic + WebSocket tests)"
    echo "  $0 --api-only       # Run only basic API tests"
    echo "  $0 --websocket-only # Run only WebSocket tests"
    echo "  $0 --no-start-api   # Run tests against existing API server"
}

# Main execution
main() {
    local run_api_tests_flag=true
    local run_websocket_tests_flag=true
    local start_api_flag=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --api-only)
                run_websocket_tests_flag=false
                shift
                ;;
            --websocket-only)
                run_api_tests_flag=false
                shift
                ;;
            --no-start-api)
                start_api_flag=false
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Start API server if needed
    if [ "$start_api_flag" = true ]; then
        if ! start_api_server; then
            print_error "Failed to start API server"
            exit 1
        fi
    else
        print_status "Skipping API server startup"
        if ! check_api_health; then
            print_error "API server is not running"
            exit 1
        fi
    fi
    
    local overall_success=true
    
    # Run basic API tests
    if [ "$run_api_tests_flag" = true ]; then
        echo ""
        print_status "Running basic API tests..."
        if ! run_api_tests; then
            overall_success=false
        fi
    fi
    
    # Run WebSocket tests
    if [ "$run_websocket_tests_flag" = true ]; then
        echo ""
        print_status "Running WebSocket tests..."
        if ! run_websocket_tests; then
            overall_success=false
        fi
    fi
    
    # Print final results
    echo ""
    echo "================================"
    if [ "$overall_success" = true ]; then
        print_success "🎉 All tests completed successfully!"
        exit 0
    else
        print_error "❌ Some tests failed"
        exit 1
    fi
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
