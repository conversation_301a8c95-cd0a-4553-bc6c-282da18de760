#!/usr/bin/env python3
"""
TurdParty Notepad++ Celery Workflow Runner
Starts Celery services and executes the complete Notepad++ workflow.
"""

import asyncio
import json
import time
import subprocess
import signal
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.service_urls import ServiceURLManager
import httpx

class NotepadPPCeleryWorkflowRunner:
    """Run Notepad++ workflow using Celery."""
    
    def __init__(self):
        self.url_manager = ServiceURLManager('development')
        self.session = httpx.AsyncClient(timeout=300.0)
        self.notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
        
        self.celery_processes = []
    
    def start_celery_services(self):
        """Start Celery worker and beat services."""
        print("🚀 Starting Celery services...")
        
        try:
            # Start Redis first (should be running via docker-compose)
            print("   📊 Checking Redis connection...")
            
            # Start Celery worker
            print("   👷 Starting Celery worker...")
            worker_cmd = [
                "celery", "-A", "services.workers.celery_app", 
                "worker", "--loglevel=info", "--concurrency=2"
            ]
            
            worker_process = subprocess.Popen(
                worker_cmd,
                cwd="/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab",
                env={
                    **dict(os.environ),
                    "PYTHONPATH": "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab",
                    "REDIS_HOST": "redis.turdparty.localhost",
                    "REDIS_PORT": "6379",
                    "ELASTICSEARCH_HOST": "elasticsearch.turdparty.localhost",
                    "ELASTICSEARCH_PORT": "9200"
                }
            )
            
            self.celery_processes.append(worker_process)
            
            # Start Celery beat (scheduler)
            print("   ⏰ Starting Celery beat...")
            beat_cmd = [
                "celery", "-A", "services.workers.celery_app", 
                "beat", "--loglevel=info"
            ]
            
            beat_process = subprocess.Popen(
                beat_cmd,
                cwd="/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab",
                env={
                    **dict(os.environ),
                    "PYTHONPATH": "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab",
                    "REDIS_HOST": "redis.turdparty.localhost",
                    "REDIS_PORT": "6379"
                }
            )
            
            self.celery_processes.append(beat_process)
            
            # Wait for services to start
            print("   ⏳ Waiting for Celery services to start...")
            time.sleep(10)
            
            print("   ✅ Celery services started successfully!")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to start Celery services: {e}")
            return False
    
    def stop_celery_services(self):
        """Stop Celery services."""
        print("\n🛑 Stopping Celery services...")
        
        for process in self.celery_processes:
            try:
                process.terminate()
                process.wait(timeout=10)
                print("   ✅ Celery process stopped")
            except subprocess.TimeoutExpired:
                process.kill()
                print("   ⚠️ Celery process killed (timeout)")
            except Exception as e:
                print(f"   ❌ Error stopping process: {e}")
    
    async def trigger_workflow(self):
        """Trigger the Notepad++ workflow via Celery."""
        print("\n🔄 Triggering Notepad++ Celery workflow...")
        
        try:
            # Use Celery to trigger the workflow
            # For now, we'll use a direct Python call since we need the Celery app running
            
            # Import and call the workflow task directly
            import os
            os.environ["PYTHONPATH"] = "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab"
            os.environ["REDIS_HOST"] = "redis.turdparty.localhost"
            os.environ["ELASTICSEARCH_HOST"] = "elasticsearch.turdparty.localhost"
            
            from services.workers.tasks.simple_workflow import process_notepadpp_workflow
            
            print(f"   🎯 Starting workflow for UUID: {self.notepadpp_uuid}")
            
            # Execute the workflow task
            result = process_notepadpp_workflow(self.notepadpp_uuid)
            
            if result.get("success"):
                print(f"   ✅ Workflow completed successfully!")
                print(f"   🆔 Workflow Job ID: {result.get('workflow_job_id')}")
                print(f"   🖥️ VM ID: {result.get('vm_id')}")
                print(f"   ⏱️ Duration: {result.get('workflow_duration', 0):.2f} seconds")
                print(f"   📊 ECS Events: {result.get('ecs_events_generated', 0)}")
                
                return result
            else:
                print(f"   ❌ Workflow failed: {result.get('error')}")
                return None
                
        except Exception as e:
            print(f"   ❌ Failed to trigger workflow: {e}")
            return None
    
    async def wait_for_ecs_data(self):
        """Wait for ECS data to be indexed."""
        print("\n📊 Waiting for ECS data indexing...")
        
        try:
            # Wait for data to be indexed
            print("   ⏳ Waiting 30 seconds for Elasticsearch indexing...")
            await asyncio.sleep(30)
            
            # Check if data is available
            es_url = f"http://elasticsearch.turdparty.localhost/turdparty-*/_search"
            query = {
                "query": {
                    "term": {"file_uuid.keyword": self.notepadpp_uuid}
                },
                "size": 10
            }
            
            response = await self.session.post(es_url, json=query)
            
            if response.status_code == 200:
                result = response.json()
                hits = result.get("hits", {}).get("hits", [])
                
                print(f"   📈 Found {len(hits)} ECS log entries")
                
                if hits:
                    print("   ✅ ECS data is available!")
                    
                    # Show sample event types
                    event_types = {}
                    for hit in hits:
                        source = hit["_source"]
                        event_action = source.get("event", {}).get("action", "unknown")
                        event_types[event_action] = event_types.get(event_action, 0) + 1
                    
                    print("   📊 Event types found:")
                    for event_type, count in event_types.items():
                        print(f"      - {event_type}: {count}")
                    
                    return True
                else:
                    print("   ⚠️ No ECS data found yet")
                    return False
            else:
                print(f"   ❌ Elasticsearch query failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ ECS data check failed: {e}")
            return False
    
    async def generate_report(self):
        """Generate the Notepad++ report."""
        print("\n📋 Generating Notepad++ report...")
        
        try:
            report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=self.notepadpp_uuid)
            print(f"   📍 Report URL: {report_url}")
            
            response = await self.session.get(report_url)
            
            if response.status_code == 200:
                report = response.json()
                print("   ✅ Report generated successfully!")
                
                # Show key metrics
                file_info = report.get("file_info", {})
                execution_summary = report.get("execution_summary", {})
                footprint = report.get("installation_footprint", {})
                security = report.get("security_analysis", {})
                
                print(f"   📁 Filename: {file_info.get('filename', 'N/A')}")
                print(f"   📊 Executions: {execution_summary.get('total_executions', 0)}")
                print(f"   📈 Files Created: {footprint.get('filesystem_changes', {}).get('files_created', 0)}")
                print(f"   🔍 Risk Level: {security.get('threat_indicators', {}).get('risk_level', 'N/A')}")
                
                # Save report
                report_file = f"/tmp/notepadpp-celery-report-{int(time.time())}.json"
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                print(f"   💾 Report saved: {report_file}")
                return report_file
            else:
                print(f"   ❌ Report generation failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Report generation exception: {e}")
            return None
    
    async def run_complete_workflow(self):
        """Run the complete Celery-based workflow."""
        print("🚀 TurdParty Notepad++ Celery Workflow")
        print(f"📅 Started at: {datetime.now()}")
        print(f"🎯 Target UUID: {self.notepadpp_uuid}")
        print("=" * 80)
        
        workflow_start = time.time()
        
        try:
            # Step 1: Start Celery services
            if not self.start_celery_services():
                print("❌ Failed to start Celery services")
                return
            
            # Step 2: Trigger workflow
            workflow_result = await self.trigger_workflow()
            if not workflow_result:
                print("❌ Workflow execution failed")
                return
            
            # Step 3: Wait for ECS data
            ecs_available = await self.wait_for_ecs_data()
            
            # Step 4: Generate report
            report_file = await self.generate_report()
            
            # Step 5: Show results
            await self.show_final_results(workflow_start, workflow_result, report_file)
            
        except KeyboardInterrupt:
            print("\n⚠️ Workflow interrupted by user")
        except Exception as e:
            print(f"\n❌ Workflow failed: {e}")
        finally:
            self.stop_celery_services()
            await self.session.aclose()
    
    async def show_final_results(self, workflow_start, workflow_result, report_file):
        """Show final workflow results."""
        total_time = time.time() - workflow_start
        
        print(f"\n{'='*80}")
        print("📊 NOTEPAD++ CELERY WORKFLOW RESULTS")
        print(f"{'='*80}")
        
        print(f"\n⏱️ TIMING:")
        print(f"   Total Time: {total_time:.1f} seconds")
        print(f"   Workflow Duration: {workflow_result.get('workflow_duration', 0):.2f} seconds")
        
        print(f"\n🎯 WORKFLOW RESULTS:")
        if workflow_result:
            print(f"   ✅ Status: Success")
            print(f"   🆔 Job ID: {workflow_result.get('workflow_job_id', 'N/A')}")
            print(f"   🖥️ VM ID: {workflow_result.get('vm_id', 'N/A')}")
            print(f"   📊 ECS Events: {workflow_result.get('ecs_events_generated', 0)}")
        else:
            print(f"   ❌ Status: Failed")
        
        print(f"\n📋 REPORT ACCESS:")
        if report_file:
            print(f"   📄 JSON Report: {report_file}")
        
        print(f"   🌐 API Endpoints:")
        print(f"      Complete: http://api.turdparty.localhost/api/v1/reports/binary/{self.notepadpp_uuid}")
        print(f"      Summary: http://api.turdparty.localhost/api/v1/reports/binary/{self.notepadpp_uuid}/summary")
        print(f"      Footprint: http://api.turdparty.localhost/api/v1/reports/binary/{self.notepadpp_uuid}/footprint")
        
        print(f"\n🔍 DATA LOCATIONS:")
        print(f"   📊 Kibana: http://kibana.turdparty.localhost/app/discover")
        print(f"   🔍 Elasticsearch: http://elasticsearch.turdparty.localhost/turdparty-*/_search")
        print(f"   🌸 Flower (Celery): http://flower.turdparty.localhost")
        
        print(f"\n✅ Notepad++ Celery Workflow Complete!")

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print("\n⚠️ Received interrupt signal, shutting down...")
    sys.exit(0)

async def main():
    """Main entry point."""
    import os
    
    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    runner = NotepadPPCeleryWorkflowRunner()
    await runner.run_complete_workflow()

if __name__ == "__main__":
    import os
    asyncio.run(main())
