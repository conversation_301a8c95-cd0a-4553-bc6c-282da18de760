#!/bin/bash

# TurdParty VM Management API Test
# Tests the complete VM management functionality: Docker & Vagrant VMs

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║              🖥️ TurdParty VM Management Test                 ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to test API endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    
    print_colored $BLUE "🔍 Testing: $name"
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [ "$status_code" = "200" ]; then
        print_colored $GREEN "   ✅ $name: Operational (HTTP $status_code)"
        return 0
    else
        print_colored $RED "   ❌ $name: Failed (HTTP $status_code)"
        return 1
    fi
}

# Function to test VM creation
test_vm_creation() {
    print_colored $PURPLE "🚀 Testing VM Creation:"
    echo ""
    
    # Test Docker VM creation
    print_colored $BLUE "🐳 Creating Docker VM..."
    
    local docker_response=$(curl -s -X POST \
        -F "name=test-docker-$(date +%s)" \
        -F "template=ubuntu:20.04" \
        -F "vm_type=docker" \
        -F "memory_mb=256" \
        -F "cpus=1" \
        -F "description=Test Docker VM for system validation" \
        http://localhost:8000/api/v1/vms/create)
    
    if echo "$docker_response" | grep -q "vm_id"; then
        local vm_id=$(echo "$docker_response" | grep -o '"vm_id":"[^"]*"' | cut -d'"' -f4)
        local task_id=$(echo "$docker_response" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
        
        print_colored $GREEN "   ✅ Docker VM creation queued successfully"
        print_colored $YELLOW "   📋 VM ID: $vm_id"
        print_colored $YELLOW "   🔧 Task ID: $task_id"
        
        # Wait and check VM status
        print_colored $BLUE "⏳ Waiting for VM creation (30 seconds)..."
        sleep 30
        
        local status_response=$(curl -s "http://localhost:8000/api/v1/vms/$vm_id")
        local vm_status=$(echo "$status_response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        local vm_name=$(echo "$status_response" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
        
        print_colored $YELLOW "   📊 VM Status: $vm_status"
        print_colored $YELLOW "   🏷️ VM Name: $vm_name"
        
        # Test VM operations
        if [ "$vm_status" = "running" ]; then
            print_colored $GREEN "   🎉 VM is running! Testing operations..."
            
            # Test VM start (should already be running)
            print_colored $BLUE "   🔄 Testing VM start operation..."
            local start_response=$(curl -s -X POST "http://localhost:8000/api/v1/vms/$vm_id/start")
            if echo "$start_response" | grep -q "already running\|start queued"; then
                print_colored $GREEN "   ✅ VM start operation successful"
            fi
            
            # Test VM stop
            print_colored $BLUE "   🛑 Testing VM stop operation..."
            local stop_response=$(curl -s -X POST -F "force=false" "http://localhost:8000/api/v1/vms/$vm_id/stop")
            if echo "$stop_response" | grep -q "stop queued"; then
                print_colored $GREEN "   ✅ VM stop operation queued"
                
                # Wait for stop
                sleep 10
                
                # Test VM deletion
                print_colored $BLUE "   🗑️ Testing VM deletion..."
                local delete_response=$(curl -s -X DELETE "http://localhost:8000/api/v1/vms/$vm_id?force=true")
                if echo "$delete_response" | grep -q "deletion queued"; then
                    print_colored $GREEN "   ✅ VM deletion queued successfully"
                fi
            fi
        else
            print_colored $YELLOW "   ⚠️ VM not yet running (status: $vm_status)"
            print_colored $YELLOW "   This is normal for the first run as Docker images need to be pulled"
        fi
        
        echo ""
        return 0
    else
        print_colored $RED "   ❌ Docker VM creation failed"
        echo "$docker_response"
        echo ""
        return 1
    fi
}

# Function to test VM listing
test_vm_listing() {
    print_colored $PURPLE "📋 Testing VM Listing:"
    echo ""
    
    local list_response=$(curl -s "http://localhost:8000/api/v1/vms/")
    local vm_count=$(echo "$list_response" | grep -o '"total":[0-9]*' | cut -d':' -f2)
    
    print_colored $YELLOW "   📊 Total VMs: $vm_count"
    
    if [ "$vm_count" -gt 0 ]; then
        print_colored $GREEN "   ✅ VM listing working - found $vm_count VMs"
        
        # Show VM details
        echo "$list_response" | python3 -m json.tool 2>/dev/null | head -20 || echo "$list_response"
    else
        print_colored $YELLOW "   ℹ️ No VMs found (this is normal for a fresh system)"
    fi
    
    echo ""
}

# Function to test API endpoints
test_vm_api_endpoints() {
    print_colored $PURPLE "🔌 Testing VM API Endpoints:"
    echo ""
    
    test_endpoint "VM List Endpoint" "http://localhost:8000/api/v1/vms/"
    test_endpoint "API Documentation" "http://localhost:8000/docs"
    test_endpoint "API Health Check" "http://localhost:8000/health/"
    
    echo ""
}

# Function to show VM management features
show_vm_features() {
    print_colored $PURPLE "🎯 VM Management Features:"
    echo ""
    
    print_colored $GREEN "✅ Docker VM Support:"
    print_colored $GREEN "   • Ubuntu 20.04, 22.04, 18.04 templates"
    print_colored $GREEN "   • Configurable memory (MB) and CPU cores"
    print_colored $GREEN "   • Automatic container lifecycle management"
    print_colored $GREEN "   • Network isolation with turdpartycollab_net"
    print_colored $GREEN "   • Volume mounting for file injection"
    print_colored $GREEN "   • Container labeling for identification"
    echo ""
    
    print_colored $GREEN "✅ VM Operations:"
    print_colored $GREEN "   • Create VM (POST /api/v1/vms/create)"
    print_colored $GREEN "   • List VMs (GET /api/v1/vms/)"
    print_colored $GREEN "   • Get VM details (GET /api/v1/vms/{vm_id})"
    print_colored $GREEN "   • Start VM (POST /api/v1/vms/{vm_id}/start)"
    print_colored $GREEN "   • Stop VM (POST /api/v1/vms/{vm_id}/stop)"
    print_colored $GREEN "   • Delete VM (DELETE /api/v1/vms/{vm_id})"
    echo ""
    
    print_colored $GREEN "✅ Workflow Integration:"
    print_colored $GREEN "   • Celery task queue for async operations"
    print_colored $GREEN "   • Database tracking of VM lifecycle"
    print_colored $GREEN "   • Automatic 30-minute runtime termination"
    print_colored $GREEN "   • File injection capabilities"
    print_colored $GREEN "   • ELK stack integration for monitoring"
    echo ""
    
    print_colored $YELLOW "🟡 Planned Features:"
    print_colored $YELLOW "   • Vagrant VM support (VirtualBox backend)"
    print_colored $YELLOW "   • Windows VM templates"
    print_colored $YELLOW "   • Advanced networking configuration"
    print_colored $YELLOW "   • VM snapshots and cloning"
    print_colored $YELLOW "   • Resource usage monitoring"
    echo ""
}

# Function to show access information
show_access_info() {
    print_colored $PURPLE "🌐 VM Management Access:"
    echo ""
    
    print_colored $CYAN "🚀 API Documentation: http://localhost:8000/docs"
    print_colored $CYAN "   • Interactive Swagger UI for VM endpoints"
    print_colored $CYAN "   • Test VM creation, management, and deletion"
    print_colored $CYAN "   • Real-time API response testing"
    echo ""
    
    print_colored $CYAN "📊 Status Dashboard: http://localhost:8090"
    print_colored $CYAN "   • Real-time VM service monitoring"
    print_colored $CYAN "   • Worker health and task queue status"
    print_colored $CYAN "   • System-wide service dependencies"
    echo ""
    
    print_colored $CYAN "🔧 Example VM Creation:"
    print_colored $CYAN "curl -X POST -F 'name=my-test-vm' \\"
    print_colored $CYAN "     -F 'template=ubuntu:20.04' \\"
    print_colored $CYAN "     -F 'vm_type=docker' \\"
    print_colored $CYAN "     -F 'memory_mb=512' \\"
    print_colored $CYAN "     -F 'cpus=1' \\"
    print_colored $CYAN "     http://localhost:8000/api/v1/vms/create"
    echo ""
}

# Main test function
main() {
    print_banner
    
    # Test API endpoints
    test_vm_api_endpoints
    
    # Test VM listing
    test_vm_listing
    
    # Test VM creation and operations
    test_vm_creation
    
    # Show features
    show_vm_features
    show_access_info
    
    print_colored $GREEN "🎉 VM Management System Test Completed!"
    print_colored $GREEN "The TurdParty VM management API is operational and ready for use."
    echo ""
}

# Run main function
main "$@"
