#!/bin/bash

# Enhanced VM Management API Test Script
# Based on reference repository patterns and comprehensive testing

set -e

# ANSI color codes
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# API configuration
API_BASE_URL="http://localhost:8000"
API_PREFIX="/api/v1"
VM_ENDPOINT="${API_PREFIX}/vms"

# Test configuration
TEST_VM_PREFIX="test-api-$(date +%s)"
TEMP_DIR="/tmp/turdparty-vm-tests"
LOG_FILE="${TEMP_DIR}/vm-api-test.log"

# Create temp directory
mkdir -p "${TEMP_DIR}"

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}" | tee -a "${LOG_FILE}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $CYAN "╔══════════════════════════════════════════════════════════════╗"
    print_colored $CYAN "║          🚀 Enhanced VM Management API Test Suite           ║"
    print_colored $CYAN "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to make API requests with proper error handling
api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local expected_status="$4"
    
    local full_url="${API_BASE_URL}${endpoint}"
    local response_file="${TEMP_DIR}/response_$(date +%s).json"
    
    print_colored $BLUE "🔍 ${method} ${endpoint}"
    
    if [ -n "$data" ]; then
        curl -s -X "${method}" \
            "${full_url}" \
            -H "Content-Type: application/json" \
            -d "${data}" \
            -w "%{http_code}" \
            -o "${response_file}" > "${TEMP_DIR}/status_code"
    else
        curl -s -X "${method}" \
            "${full_url}" \
            -w "%{http_code}" \
            -o "${response_file}" > "${TEMP_DIR}/status_code"
    fi
    
    local status_code=$(cat "${TEMP_DIR}/status_code")
    local response_body=$(cat "${response_file}")
    
    if [ -n "$expected_status" ] && [ "$status_code" != "$expected_status" ]; then
        print_colored $RED "   ❌ Expected status $expected_status, got $status_code"
        print_colored $RED "   Response: $response_body"
        return 1
    fi
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        print_colored $GREEN "   ✅ Success (HTTP $status_code)"
    else
        print_colored $RED "   ❌ Failed (HTTP $status_code)"
        print_colored $RED "   Response: $response_body"
        return 1
    fi
    
    echo "$response_body"
    return 0
}

# Function to test VM templates endpoint
test_vm_templates() {
    print_colored $PURPLE "📋 Testing VM Templates Endpoint:"
    echo ""
    
    local response=$(api_request "GET" "${VM_ENDPOINT}/templates" "" "200")
    
    if [ $? -eq 0 ]; then
        local template_count=$(echo "$response" | jq '. | length' 2>/dev/null || echo "0")
        print_colored $GREEN "   📊 Found $template_count VM templates"
        
        # Check for specific templates
        local ubuntu_templates=$(echo "$response" | jq '[.[] | select(.name | contains("UBUNTU"))] | length' 2>/dev/null || echo "0")
        local docker_templates=$(echo "$response" | jq '[.[] | select(.name | contains("DOCKER"))] | length' 2>/dev/null || echo "0")
        
        print_colored $YELLOW "   🐧 Ubuntu templates: $ubuntu_templates"
        print_colored $YELLOW "   🐳 Docker templates: $docker_templates"
        
        # Verify template structure
        local first_template=$(echo "$response" | jq '.[0]' 2>/dev/null)
        if echo "$first_template" | jq -e '.value and .name and .description and .compatible_vm_types' >/dev/null 2>&1; then
            print_colored $GREEN "   ✅ Template structure validation passed"
        else
            print_colored $RED "   ❌ Template structure validation failed"
        fi
    fi
    
    echo ""
}

# Function to test VM creation with enhanced schema
test_vm_creation() {
    print_colored $PURPLE "🚀 Testing Enhanced VM Creation:"
    echo ""
    
    local vm_name="${TEST_VM_PREFIX}-docker"
    local vm_data='{
        "name": "'$vm_name'",
        "template": "DOCKER_UBUNTU_2004",
        "vm_type": "docker",
        "memory_mb": 512,
        "cpus": 1,
        "disk_gb": 10,
        "domain": "TurdParty",
        "description": "Test VM created by enhanced API test",
        "auto_start": true
    }'
    
    local response=$(api_request "POST" "${VM_ENDPOINT}/" "$vm_data" "201")
    
    if [ $? -eq 0 ]; then
        local vm_id=$(echo "$response" | jq -r '.vm_id' 2>/dev/null)
        local vm_status=$(echo "$response" | jq -r '.status' 2>/dev/null)
        
        print_colored $GREEN "   📋 VM ID: $vm_id"
        print_colored $GREEN "   📊 Status: $vm_status"
        
        # Store VM ID for cleanup
        echo "$vm_id" >> "${TEMP_DIR}/created_vms.txt"
        
        # Verify response structure
        if echo "$response" | jq -e '.vm_id and .name and .template and .vm_type and .memory_mb and .cpus and .domain and .runtime_minutes and .is_expired' >/dev/null 2>&1; then
            print_colored $GREEN "   ✅ VM creation response structure validated"
        else
            print_colored $RED "   ❌ VM creation response structure invalid"
        fi
        
        return 0
    else
        return 1
    fi
}

# Function to test domain enforcement
test_domain_enforcement() {
    print_colored $PURPLE "🔒 Testing Domain Enforcement:"
    echo ""
    
    local vm_name="${TEST_VM_PREFIX}-invalid-domain"
    local vm_data='{
        "name": "'$vm_name'",
        "template": "UBUNTU_2004",
        "vm_type": "docker",
        "domain": "InvalidDomain"
    }'
    
    local response=$(api_request "POST" "${VM_ENDPOINT}/" "$vm_data" "400")
    
    if [ $? -eq 0 ]; then
        if echo "$response" | grep -q "TurdParty domain"; then
            print_colored $GREEN "   ✅ Domain enforcement working correctly"
        else
            print_colored $RED "   ❌ Domain enforcement error message incorrect"
        fi
    fi
    
    echo ""
}

# Function to test VM actions
test_vm_actions() {
    print_colored $PURPLE "⚡ Testing VM Actions:"
    echo ""
    
    # First create a VM
    local vm_name="${TEST_VM_PREFIX}-actions"
    local vm_data='{
        "name": "'$vm_name'",
        "template": "DOCKER_ALPINE",
        "vm_type": "docker",
        "memory_mb": 256,
        "cpus": 1,
        "domain": "TurdParty",
        "auto_start": false
    }'
    
    local create_response=$(api_request "POST" "${VM_ENDPOINT}/" "$vm_data" "201")
    
    if [ $? -eq 0 ]; then
        local vm_id=$(echo "$create_response" | jq -r '.vm_id' 2>/dev/null)
        echo "$vm_id" >> "${TEMP_DIR}/created_vms.txt"
        
        # Test START action
        print_colored $BLUE "   🔄 Testing START action..."
        local start_data='{"action": "start", "force": false}'
        local start_response=$(api_request "POST" "${VM_ENDPOINT}/${vm_id}/action" "$start_data" "200")
        
        if [ $? -eq 0 ]; then
            local action=$(echo "$start_response" | jq -r '.action' 2>/dev/null)
            local task_id=$(echo "$start_response" | jq -r '.task_id' 2>/dev/null)
            
            if [ "$action" = "start" ] && [ "$task_id" != "null" ]; then
                print_colored $GREEN "   ✅ START action successful (Task: $task_id)"
            fi
        fi
        
        # Test STOP action
        print_colored $BLUE "   🛑 Testing STOP action..."
        local stop_data='{"action": "stop", "force": true}'
        local stop_response=$(api_request "POST" "${VM_ENDPOINT}/${vm_id}/action" "$stop_data" "200")
        
        if [ $? -eq 0 ]; then
            local action=$(echo "$stop_response" | jq -r '.action' 2>/dev/null)
            local force=$(echo "$stop_response" | jq -r '.force' 2>/dev/null)
            
            if [ "$action" = "stop" ] && [ "$force" = "true" ]; then
                print_colored $GREEN "   ✅ STOP action successful (Force: $force)"
            fi
        fi
        
        # Test invalid action
        print_colored $BLUE "   ❌ Testing invalid action..."
        local invalid_data='{"action": "invalid_action", "force": false}'
        api_request "POST" "${VM_ENDPOINT}/${vm_id}/action" "$invalid_data" "422" >/dev/null
        
        if [ $? -eq 0 ]; then
            print_colored $GREEN "   ✅ Invalid action properly rejected"
        fi
    fi
    
    echo ""
}

# Function to test VM listing and pagination
test_vm_listing() {
    print_colored $PURPLE "📋 Testing VM Listing and Pagination:"
    echo ""
    
    local response=$(api_request "GET" "${VM_ENDPOINT}/?skip=0&limit=10" "" "200")
    
    if [ $? -eq 0 ]; then
        local total=$(echo "$response" | jq -r '.total' 2>/dev/null)
        local skip=$(echo "$response" | jq -r '.skip' 2>/dev/null)
        local limit=$(echo "$response" | jq -r '.limit' 2>/dev/null)
        local vm_count=$(echo "$response" | jq '.vms | length' 2>/dev/null)
        
        print_colored $GREEN "   📊 Total VMs: $total"
        print_colored $GREEN "   📄 Page: skip=$skip, limit=$limit"
        print_colored $GREEN "   📋 VMs in response: $vm_count"
        
        # Verify pagination structure
        if echo "$response" | jq -e '.vms and .total and .skip and .limit' >/dev/null 2>&1; then
            print_colored $GREEN "   ✅ Pagination structure validated"
        else
            print_colored $RED "   ❌ Pagination structure invalid"
        fi
    fi
    
    echo ""
}

# Function to cleanup created VMs
cleanup_vms() {
    print_colored $PURPLE "🧹 Cleaning up test VMs:"
    echo ""
    
    if [ -f "${TEMP_DIR}/created_vms.txt" ]; then
        while IFS= read -r vm_id; do
            if [ -n "$vm_id" ] && [ "$vm_id" != "null" ]; then
                print_colored $BLUE "   🗑️ Deleting VM: $vm_id"
                curl -s -X DELETE "${API_BASE_URL}${VM_ENDPOINT}/${vm_id}?force=true" >/dev/null 2>&1
                print_colored $GREEN "   ✅ VM deleted: $vm_id"
            fi
        done < "${TEMP_DIR}/created_vms.txt"
    fi
    
    echo ""
}

# Function to generate test report
generate_report() {
    print_colored $PURPLE "📊 Test Report:"
    echo ""
    
    local total_tests=6
    local passed_tests=$(grep -c "✅" "${LOG_FILE}" 2>/dev/null || echo "0")
    local failed_tests=$(grep -c "❌" "${LOG_FILE}" 2>/dev/null || echo "0")
    
    print_colored $GREEN "✅ Passed: $passed_tests"
    print_colored $RED "❌ Failed: $failed_tests"
    print_colored $CYAN "📋 Total Tests: $total_tests"
    
    local success_rate=$((passed_tests * 100 / (passed_tests + failed_tests)))
    print_colored $CYAN "📈 Success Rate: ${success_rate}%"
    
    echo ""
    print_colored $CYAN "📄 Full log available at: $LOG_FILE"
    echo ""
}

# Main test execution
main() {
    print_banner
    
    # Initialize log
    echo "Enhanced VM Management API Test - $(date)" > "${LOG_FILE}"
    
    # Run test suite
    test_vm_templates
    test_domain_enforcement
    test_vm_creation
    test_vm_actions
    test_vm_listing
    
    # Cleanup
    cleanup_vms
    
    # Generate report
    generate_report
    
    print_colored $GREEN "🎉 Enhanced VM Management API Test Suite Completed!"
    echo ""
}

# Trap to ensure cleanup on exit
trap cleanup_vms EXIT

# Run main function
main "$@"
