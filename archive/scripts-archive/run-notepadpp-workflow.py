#!/usr/bin/env python3
"""
TurdParty Notepad++ Complete Workflow Execution
Uses Celery queue system to process Notepad++ through VM execution pipeline.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.service_urls import ServiceURLManager
import httpx

class NotepadPPWorkflowRunner:
    """Execute Notepad++ through the complete TurdParty Celery workflow."""
    
    def __init__(self):
        self.url_manager = ServiceURLManager('development')
        self.session = httpx.AsyncClient(timeout=300.0, follow_redirects=True)
        
        # Notepad++ details from our previous test
        self.notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
        self.notepadpp_filename = "npp.8.5.8.Installer.x64.exe"
        self.notepadpp_url = "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe"
        
        self.workflow_results = {}
    
    async def step1_upload_file_to_minio(self):
        """Step 1: Upload Notepad++ to MinIO via API."""
        print("📤 Step 1: Uploading Notepad++ to MinIO...")
        
        try:
            # Download Notepad++ first
            print(f"   📥 Downloading {self.notepadpp_filename}...")
            download_response = await self.session.get(self.notepadpp_url)
            download_response.raise_for_status()
            
            file_content = download_response.content
            file_size = len(file_content)
            print(f"   ✅ Downloaded {file_size:,} bytes")
            
            # Upload to MinIO via API
            upload_url = self.url_manager.get_api_endpoint('files', 'upload')
            print(f"   📤 Uploading to: {upload_url}")
            
            files = {
                'file': (self.notepadpp_filename, file_content, 'application/octet-stream')
            }
            data = {
                'description': 'Notepad++ text editor for Windows VM testing',
                'metadata': json.dumps({
                    'application_name': 'notepadpp',
                    'application_description': 'Notepad++ Text Editor',
                    'file_type': 'exe',
                    'platform': 'windows',
                    'install_command': 'npp.8.5.8.Installer.x64.exe /S',
                    'file_uuid': self.notepadpp_uuid
                })
            }
            
            upload_response = await self.session.post(upload_url, files=files, data=data)
            
            if upload_response.status_code == 201:
                upload_result = upload_response.json()
                print(f"   ✅ Upload successful!")
                print(f"   📁 File ID: {upload_result.get('file_id', 'N/A')}")
                print(f"   🪣 Bucket: {upload_result.get('bucket', 'N/A')}")
                print(f"   🔑 Object Key: {upload_result.get('object_name', 'N/A')}")
                
                self.workflow_results['upload'] = upload_result
                return upload_result
            else:
                print(f"   ❌ Upload failed: HTTP {upload_response.status_code}")
                print(f"   Error: {upload_response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Upload exception: {e}")
            return None
    
    async def step2_start_workflow_job(self, file_upload_result):
        """Step 2: Start Celery workflow job for file processing."""
        print("\n🔄 Step 2: Starting Celery workflow job...")
        
        try:
            # Create workflow job via API
            workflow_url = self.url_manager.get_api_endpoint('vms', 'create')  # Will need workflow endpoint
            
            workflow_config = {
                'name': f'notepadpp-workflow-{int(time.time())}',
                'template': 'gusztavvargadr/windows-10',
                'vm_type': 'vagrant',
                'memory_mb': 4096,
                'cpus': 2,
                'disk_gb': 40,
                'file_id': file_upload_result.get('file_id'),
                'execution_config': {
                    'command': 'npp.8.5.8.Installer.x64.exe /S',
                    'working_directory': 'C:\\temp',
                    'timeout_seconds': 300,
                    'capture_output': True,
                    'capture_filesystem_changes': True
                },
                'workflow_type': 'file_processing'
            }
            
            print(f"   🎯 Creating workflow job...")
            response = await self.session.post(workflow_url, json=workflow_config)
            
            if response.status_code in [200, 201]:
                workflow_result = response.json()
                print(f"   ✅ Workflow job created!")
                print(f"   🆔 Job ID: {workflow_result.get('vm_id', 'N/A')}")
                print(f"   📊 Status: {workflow_result.get('status', 'N/A')}")
                
                self.workflow_results['workflow'] = workflow_result
                return workflow_result
            else:
                print(f"   ❌ Workflow creation failed: HTTP {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Workflow exception: {e}")
            return None
    
    async def step3_monitor_vm_allocation(self, workflow_result):
        """Step 3: Monitor VM allocation from pool."""
        print("\n🖥️ Step 3: Monitoring VM allocation from pool...")
        
        vm_id = workflow_result.get('vm_id')
        if not vm_id:
            print("   ❌ No VM ID available")
            return None
        
        try:
            # Monitor VM status
            status_url = self.url_manager.get_api_endpoint('vms', 'status', vm_id=vm_id)
            
            max_wait = 300  # 5 minutes
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                print(f"   ⏳ Checking VM status... ({int(time.time() - start_time)}s)")
                
                response = await self.session.get(status_url)
                
                if response.status_code == 200:
                    vm_status = response.json()
                    status = vm_status.get('status', 'unknown')
                    
                    print(f"   📊 VM Status: {status}")
                    
                    if status == 'running':
                        print(f"   ✅ VM is running and ready!")
                        self.workflow_results['vm_ready'] = vm_status
                        return vm_status
                    elif status in ['failed', 'error']:
                        print(f"   ❌ VM creation failed: {status}")
                        return None
                    
                    # Continue monitoring
                    await asyncio.sleep(10)
                else:
                    print(f"   ⚠️ Status check failed: HTTP {response.status_code}")
                    await asyncio.sleep(5)
            
            print(f"   ⏰ Timeout waiting for VM to be ready")
            return None
            
        except Exception as e:
            print(f"   ❌ Monitoring exception: {e}")
            return None
    
    async def step4_monitor_file_injection(self, vm_status):
        """Step 4: Monitor file injection into VM."""
        print("\n💉 Step 4: Monitoring file injection...")
        
        vm_id = vm_status.get('vm_id') or vm_status.get('id')
        
        try:
            # The injection should happen automatically via Celery workflow
            # We'll monitor for injection completion
            
            max_wait = 180  # 3 minutes
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                print(f"   ⏳ Checking injection status... ({int(time.time() - start_time)}s)")
                
                # Check VM status for injection completion
                status_url = self.url_manager.get_api_endpoint('vms', 'status', vm_id=vm_id)
                response = await self.session.get(status_url)
                
                if response.status_code == 200:
                    vm_info = response.json()
                    
                    # Look for injection indicators
                    injection_completed = vm_info.get('injection_completed', False)
                    injection_path = vm_info.get('injection_path')
                    
                    if injection_completed and injection_path:
                        print(f"   ✅ File injection completed!")
                        print(f"   📁 Injection path: {injection_path}")
                        self.workflow_results['injection'] = vm_info
                        return vm_info
                    
                    await asyncio.sleep(5)
                else:
                    print(f"   ⚠️ Status check failed: HTTP {response.status_code}")
                    await asyncio.sleep(5)
            
            print(f"   ⏰ Timeout waiting for file injection")
            return None
            
        except Exception as e:
            print(f"   ❌ Injection monitoring exception: {e}")
            return None
    
    async def step5_monitor_execution(self, injection_result):
        """Step 5: Monitor Notepad++ installation execution."""
        print("\n⚙️ Step 5: Monitoring Notepad++ installation...")
        
        vm_id = injection_result.get('vm_id') or injection_result.get('id')
        
        try:
            # Monitor execution for up to 10 minutes
            max_wait = 600  # 10 minutes
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                elapsed = int(time.time() - start_time)
                print(f"   ⏳ Monitoring execution... ({elapsed}s)")
                
                # Check for execution completion
                status_url = self.url_manager.get_api_endpoint('vms', 'status', vm_id=vm_id)
                response = await self.session.get(status_url)
                
                if response.status_code == 200:
                    vm_info = response.json()
                    
                    # Look for execution completion indicators
                    execution_completed = vm_info.get('execution_completed', False)
                    exit_code = vm_info.get('exit_code')
                    
                    if execution_completed:
                        print(f"   ✅ Execution completed!")
                        print(f"   🔢 Exit code: {exit_code}")
                        
                        if exit_code == 0:
                            print(f"   🎉 Notepad++ installation successful!")
                        else:
                            print(f"   ⚠️ Installation completed with exit code: {exit_code}")
                        
                        self.workflow_results['execution'] = vm_info
                        return vm_info
                    
                    await asyncio.sleep(10)
                else:
                    print(f"   ⚠️ Status check failed: HTTP {response.status_code}")
                    await asyncio.sleep(10)
            
            print(f"   ⏰ Execution monitoring timeout (may still be running)")
            return None
            
        except Exception as e:
            print(f"   ❌ Execution monitoring exception: {e}")
            return None
    
    async def step6_check_ecs_data(self):
        """Step 6: Check ECS data generation in Elasticsearch."""
        print("\n📊 Step 6: Checking ECS data generation...")
        
        try:
            # Query Elasticsearch for our UUID
            es_url = f"http://elasticsearch.turdparty.localhost/turdparty-*/_search"
            
            query = {
                "query": {
                    "term": {"file_uuid.keyword": self.notepadpp_uuid}
                },
                "size": 100,
                "sort": [{"@timestamp": {"order": "desc"}}]
            }
            
            response = await self.session.post(es_url, json=query)
            
            if response.status_code == 200:
                es_result = response.json()
                hits = es_result.get('hits', {}).get('hits', [])
                
                print(f"   📊 Found {len(hits)} ECS log entries")
                
                if hits:
                    print(f"   ✅ ECS data is being generated!")
                    
                    # Show sample events
                    event_types = {}
                    for hit in hits:
                        source = hit['_source']
                        event_action = source.get('event', {}).get('action', 'unknown')
                        event_types[event_action] = event_types.get(event_action, 0) + 1
                    
                    print(f"   📈 Event types:")
                    for event_type, count in event_types.items():
                        print(f"      - {event_type}: {count}")
                    
                    self.workflow_results['ecs_data'] = {
                        'total_events': len(hits),
                        'event_types': event_types,
                        'sample_events': hits[:3]
                    }
                    return True
                else:
                    print(f"   ⚠️ No ECS data found yet (may still be processing)")
                    return False
            else:
                print(f"   ❌ Elasticsearch query failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ ECS data check exception: {e}")
            return False
    
    async def step7_generate_report(self):
        """Step 7: Generate comprehensive report using Reporting API."""
        print("\n📋 Step 7: Generating comprehensive report...")
        
        try:
            # Wait a bit for ECS data to be fully indexed
            print("   ⏳ Waiting for ECS data indexing...")
            await asyncio.sleep(30)
            
            # Generate report
            report_url = self.url_manager.get_api_endpoint('reporting', 'binary_report', file_uuid=self.notepadpp_uuid)
            print(f"   📊 Generating report: {report_url}")
            
            response = await self.session.get(report_url)
            
            if response.status_code == 200:
                report = response.json()
                print(f"   ✅ Report generated successfully!")
                
                # Show key metrics
                file_info = report.get('file_info', {})
                execution_summary = report.get('execution_summary', {})
                footprint = report.get('installation_footprint', {})
                security = report.get('security_analysis', {})
                
                print(f"   📁 Filename: {file_info.get('filename', 'N/A')}")
                print(f"   📊 Executions: {execution_summary.get('total_executions', 0)}")
                print(f"   📈 Files Created: {footprint.get('filesystem_changes', {}).get('files_created', 0)}")
                print(f"   🔍 Risk Level: {security.get('threat_indicators', {}).get('risk_level', 'N/A')}")
                
                self.workflow_results['report'] = report
                
                # Save report to file
                report_file = f"/tmp/notepadpp-report-{int(time.time())}.json"
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                print(f"   💾 Report saved: {report_file}")
                return report_file
            else:
                print(f"   ❌ Report generation failed: HTTP {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Report generation exception: {e}")
            return None
    
    async def run_complete_workflow(self):
        """Run the complete Notepad++ workflow."""
        print("🚀 Starting Notepad++ Complete Workflow")
        print(f"📅 Started at: {datetime.now()}")
        print(f"🎯 Target: {self.notepadpp_filename}")
        print(f"🔑 UUID: {self.notepadpp_uuid}")
        print("=" * 80)
        
        workflow_start = time.time()
        
        try:
            # Step 1: Upload file
            upload_result = await self.step1_upload_file_to_minio()
            if not upload_result:
                print("❌ Workflow failed at file upload")
                return
            
            # Step 2: Start workflow job
            workflow_result = await self.step2_start_workflow_job(upload_result)
            if not workflow_result:
                print("❌ Workflow failed at job creation")
                return
            
            # Step 3: Monitor VM allocation
            vm_status = await self.step3_monitor_vm_allocation(workflow_result)
            if not vm_status:
                print("❌ Workflow failed at VM allocation")
                return
            
            # Step 4: Monitor file injection
            injection_result = await self.step4_monitor_file_injection(vm_status)
            if not injection_result:
                print("❌ Workflow failed at file injection")
                return
            
            # Step 5: Monitor execution
            execution_result = await self.step5_monitor_execution(injection_result)
            if not execution_result:
                print("⚠️ Execution monitoring incomplete (may still be running)")
            
            # Step 6: Check ECS data
            ecs_available = await self.step6_check_ecs_data()
            
            # Step 7: Generate report
            report_file = await self.step7_generate_report()
            
            # Final summary
            await self.generate_workflow_summary(workflow_start, report_file)
            
        except Exception as e:
            print(f"❌ Workflow exception: {e}")
        finally:
            await self.session.aclose()
    
    async def generate_workflow_summary(self, workflow_start, report_file):
        """Generate final workflow summary."""
        total_time = time.time() - workflow_start
        
        print(f"\n{'='*80}")
        print("📊 NOTEPAD++ WORKFLOW SUMMARY")
        print(f"{'='*80}")
        
        print(f"\n⏱️ TIMING:")
        print(f"   Total Workflow Time: {total_time:.1f} seconds")
        print(f"   Started: {datetime.fromtimestamp(workflow_start)}")
        print(f"   Completed: {datetime.now()}")
        
        print(f"\n🎯 RESULTS:")
        for step, result in self.workflow_results.items():
            if result:
                print(f"   ✅ {step.title()}: Success")
            else:
                print(f"   ❌ {step.title()}: Failed")
        
        print(f"\n📋 REPORT LOCATION:")
        if report_file:
            print(f"   📄 JSON Report: {report_file}")
            print(f"   🌐 API Endpoint: http://api.turdparty.localhost/api/v1/reports/binary/{self.notepadpp_uuid}")
            print(f"   📊 Summary: http://api.turdparty.localhost/api/v1/reports/binary/{self.notepadpp_uuid}/summary")
        else:
            print(f"   ⚠️ Report generation failed - check ECS data availability")
        
        print(f"\n🔍 WHERE TO VIEW RESULTS:")
        print(f"   📊 Kibana: http://kibana.turdparty.localhost/app/discover")
        print(f"   🔍 Elasticsearch: http://elasticsearch.turdparty.localhost/turdparty-*/_search")
        print(f"   📈 Status Dashboard: http://status.turdparty.localhost")
        
        print(f"\n✅ Notepad++ Workflow Complete!")
        print(f"🎉 Ready for comprehensive binary analysis and reporting!")

async def main():
    """Main entry point."""
    runner = NotepadPPWorkflowRunner()
    await runner.run_complete_workflow()

if __name__ == "__main__":
    asyncio.run(main())
