#!/bin/bash

# Comprehensive Test Runner for TurdParty VM WebSocket Features
# This script runs all test suites with proper environment setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
API_PORT=8000
API_HOST="localhost"
TEST_TIMEOUT=300
COVERAGE_MIN_THRESHOLD=80

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✅ $test_name: PASS${NC} $details"
    else
        echo -e "${RED}❌ $test_name: FAIL${NC} $details"
    fi
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    local missing_deps=()
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        missing_deps+=("pip3")
    fi
    
    # Check Docker (optional)
    if ! command -v docker &> /dev/null; then
        print_warning "Docker not found - Docker tests will be skipped"
    fi
    
    # Check Vagrant (optional)
    if ! command -v vagrant &> /dev/null; then
        print_warning "Vagrant not found - Vagrant tests will be skipped"
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    print_success "All required dependencies found"
    return 0
}

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Enter nix shell if available
    if [ -f "shell.nix" ] && command -v nix-shell &> /dev/null; then
        print_status "Using Nix shell environment"
        export NIX_SHELL_ACTIVE=1
    else
        # Setup Python virtual environment
        if [ ! -d "venv" ]; then
            print_status "Creating Python virtual environment..."
            python3 -m venv venv
        fi
        
        print_status "Activating virtual environment..."
        source venv/bin/activate
        
        # Install dependencies
        print_status "Installing test dependencies..."
        pip install -r requirements.txt > /dev/null 2>&1 || true
        pip install pytest pytest-asyncio pytest-cov pytest-benchmark websockets httpx > /dev/null 2>&1
    fi
    
    # Set test environment variables
    export TESTING=true
    export LOG_LEVEL=DEBUG
    export TEST_API_URL="http://${API_HOST}:${API_PORT}"
    export TEST_WS_URL="ws://${API_HOST}:${API_PORT}"
    
    print_success "Test environment ready"
}

# Function to start API server
start_api_server() {
    print_status "Starting API server..."
    
    # Check if API is already running
    if curl -s "http://${API_HOST}:${API_PORT}/health" > /dev/null 2>&1; then
        print_warning "API server is already running"
        return 0
    fi
    
    # Start the server
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "uvicorn api.main:app --host 0.0.0.0 --port $API_PORT" &
    else
        source venv/bin/activate
        uvicorn api.main:app --host 0.0.0.0 --port $API_PORT &
    fi
    
    API_PID=$!
    
    # Wait for server to start
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://${API_HOST}:${API_PORT}/health" > /dev/null 2>&1; then
            print_success "API server started successfully (PID: $API_PID)"
            return 0
        fi
        
        print_status "Waiting for API to start... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "API server failed to start within timeout"
    kill $API_PID 2>/dev/null || true
    return 1
}

# Function to run unit tests
run_unit_tests() {
    print_header "Running Unit Tests"
    
    local test_cmd="pytest tests/unit/ -v --tb=short --cov=api --cov-report=term-missing"
    
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "$test_cmd"
    else
        source venv/bin/activate
        $test_cmd
    fi
    
    local result=$?
    
    if [ $result -eq 0 ]; then
        print_test_result "Unit Tests" "PASS" ""
        return 0
    else
        print_test_result "Unit Tests" "FAIL" ""
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_header "Running Integration Tests"
    
    local test_cmd="pytest tests/integration/ -v --tb=short -m 'not slow'"
    
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "$test_cmd"
    else
        source venv/bin/activate
        $test_cmd
    fi
    
    local result=$?
    
    if [ $result -eq 0 ]; then
        print_test_result "Integration Tests" "PASS" ""
        return 0
    else
        print_test_result "Integration Tests" "FAIL" ""
        return 1
    fi
}

# Function to run WebSocket tests
run_websocket_tests() {
    print_header "Running WebSocket Tests"
    
    local test_cmd="pytest tests/ -v --tb=short -m websocket"
    
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "$test_cmd"
    else
        source venv/bin/activate
        $test_cmd
    fi
    
    local result=$?
    
    if [ $result -eq 0 ]; then
        print_test_result "WebSocket Tests" "PASS" ""
        return 0
    else
        print_test_result "WebSocket Tests" "FAIL" ""
        return 1
    fi
}

# Function to run performance tests
run_performance_tests() {
    print_header "Running Performance Tests"
    
    local test_cmd="pytest tests/performance/ -v --tb=short --benchmark-only"
    
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "$test_cmd"
    else
        source venv/bin/activate
        $test_cmd
    fi
    
    local result=$?
    
    if [ $result -eq 0 ]; then
        print_test_result "Performance Tests" "PASS" ""
        return 0
    else
        print_test_result "Performance Tests" "FAIL" ""
        return 1
    fi
}

# Function to run custom WebSocket test script
run_custom_websocket_tests() {
    print_header "Running Custom WebSocket Tests"
    
    if [ -f "scripts/test-vm-websockets.py" ]; then
        local test_cmd="python scripts/test-vm-websockets.py --base-url ws://${API_HOST}:${API_PORT}"
        
        if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
            nix-shell --run "$test_cmd"
        else
            source venv/bin/activate
            $test_cmd
        fi
        
        local result=$?
        
        if [ $result -eq 0 ]; then
            print_test_result "Custom WebSocket Tests" "PASS" ""
            return 0
        else
            print_test_result "Custom WebSocket Tests" "FAIL" ""
            return 1
        fi
    else
        print_warning "Custom WebSocket test script not found"
        return 0
    fi
}

# Function to generate test report
generate_test_report() {
    print_header "Generating Test Report"
    
    local report_file="test_report_$(date +%Y%m%d_%H%M%S).html"
    
    local test_cmd="pytest tests/ --html=$report_file --self-contained-html --tb=short"
    
    if [ "$NIX_SHELL_ACTIVE" = "1" ]; then
        nix-shell --run "$test_cmd" > /dev/null 2>&1 || true
    else
        source venv/bin/activate
        $test_cmd > /dev/null 2>&1 || true
    fi
    
    if [ -f "$report_file" ]; then
        print_success "Test report generated: $report_file"
    else
        print_warning "Failed to generate test report"
    fi
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    
    if [ ! -z "$API_PID" ]; then
        print_status "Stopping API server (PID: $API_PID)..."
        kill $API_PID 2>/dev/null || true
        wait $API_PID 2>/dev/null || true
        print_success "API server stopped"
    fi
    
    # Clean up test artifacts
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --unit-only         Run only unit tests"
    echo "  --integration-only  Run only integration tests"
    echo "  --websocket-only    Run only WebSocket tests"
    echo "  --performance-only  Run only performance tests"
    echo "  --no-start-api      Don't start API server (assume it's running)"
    echo "  --generate-report   Generate HTML test report"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                     # Run all tests"
    echo "  $0 --unit-only        # Run only unit tests"
    echo "  $0 --websocket-only    # Run only WebSocket tests"
    echo "  $0 --generate-report   # Run all tests and generate report"
}

# Main execution
main() {
    local run_unit=true
    local run_integration=true
    local run_websocket=true
    local run_performance=true
    local start_api=true
    local generate_report=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                run_integration=false
                run_websocket=false
                run_performance=false
                shift
                ;;
            --integration-only)
                run_unit=false
                run_websocket=false
                run_performance=false
                shift
                ;;
            --websocket-only)
                run_unit=false
                run_integration=false
                run_performance=false
                shift
                ;;
            --performance-only)
                run_unit=false
                run_integration=false
                run_websocket=false
                shift
                ;;
            --no-start-api)
                start_api=false
                shift
                ;;
            --generate-report)
                generate_report=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header "TurdParty VM WebSocket Comprehensive Test Suite"
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Check dependencies
    if ! check_dependencies; then
        exit 1
    fi
    
    # Setup test environment
    if ! setup_test_environment; then
        exit 1
    fi
    
    # Start API server if needed
    if [ "$start_api" = true ]; then
        if ! start_api_server; then
            exit 1
        fi
    fi
    
    # Track test results
    local tests_run=0
    local tests_passed=0
    
    # Run test suites
    if [ "$run_unit" = true ]; then
        ((tests_run++))
        if run_unit_tests; then
            ((tests_passed++))
        fi
    fi
    
    if [ "$run_integration" = true ]; then
        ((tests_run++))
        if run_integration_tests; then
            ((tests_passed++))
        fi
    fi
    
    if [ "$run_websocket" = true ]; then
        ((tests_run++))
        if run_websocket_tests; then
            ((tests_passed++))
        fi
        
        ((tests_run++))
        if run_custom_websocket_tests; then
            ((tests_passed++))
        fi
    fi
    
    if [ "$run_performance" = true ]; then
        ((tests_run++))
        if run_performance_tests; then
            ((tests_passed++))
        fi
    fi
    
    # Generate report if requested
    if [ "$generate_report" = true ]; then
        generate_test_report
    fi
    
    # Print final results
    print_header "Test Results Summary"
    echo -e "${CYAN}Tests Run: $tests_run${NC}"
    echo -e "${GREEN}Tests Passed: $tests_passed${NC}"
    echo -e "${RED}Tests Failed: $((tests_run - tests_passed))${NC}"
    
    if [ $tests_passed -eq $tests_run ]; then
        print_success "🎉 All tests passed!"
        exit 0
    else
        print_error "❌ Some tests failed"
        exit 1
    fi
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
