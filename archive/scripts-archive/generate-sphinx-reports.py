#!/usr/bin/env python3
"""
Generate Sphinx Reports for TurdParty Binary Analysis
Creates comprehensive Sphinx documentation from ECS data and API reports.
"""

import asyncio
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.report_generator import SphinxReportGenerator

async def main():
    """Main entry point for report generation."""
    print("🚀 TurdParty Sphinx Report Generator")
    print("=" * 50)
    
    generator = SphinxReportGenerator()
    
    try:
        # Generate report for Notepad++ UUID
        notepadpp_uuid = "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"
        
        print(f"📊 Generating Sphinx report for Notepad++ UUID: {notepadpp_uuid}")
        
        result = await generator.generate_report_from_uuid(notepadpp_uuid)
        
        if result["success"]:
            print(f"✅ Report generated successfully!")
            print(f"   📄 RST File: {result['rst_file']}")
            print(f"   🌐 Report URL: {result['report_url']}")
            print(f"   📁 HTML Path: {result['html_path']}")
            
            # Show report summary
            report_data = result["report_data"]
            print(f"\n📋 Report Summary:")
            print(f"   📁 Filename: {report_data.get('file_info', {}).get('filename', 'N/A')}")
            print(f"   📊 Risk Level: {report_data.get('security_analysis', {}).get('threat_indicators', {}).get('risk_level', 'N/A')}")
            print(f"   🔍 Threat Score: {report_data.get('security_analysis', {}).get('threat_indicators', {}).get('suspicious_behavior_score', 0)}/10")
            
        else:
            print(f"❌ Report generation failed: {result['error']}")
        
        # Generate all available reports
        print(f"\n📊 Generating all available reports...")
        all_results = await generator.generate_all_reports()
        
        if all_results["success"]:
            print(f"✅ Batch generation complete!")
            print(f"   📊 Total UUIDs: {all_results['total_uuids']}")
            print(f"   ✅ Successful: {all_results['successful_reports']}")
        else:
            print(f"❌ Batch generation failed: {all_results['error']}")
        
        print(f"\n🌐 Access Reports:")
        print(f"   📊 Reports Platform: http://reports.turdparty.localhost")
        print(f"   📋 Notepad++ Report: http://reports.turdparty.localhost/notepadpp-analysis.html")
        print(f"   🔍 Search & Browse: http://reports.turdparty.localhost/search.html")
        
    except Exception as e:
        print(f"❌ Script failed: {e}")
    finally:
        await generator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
