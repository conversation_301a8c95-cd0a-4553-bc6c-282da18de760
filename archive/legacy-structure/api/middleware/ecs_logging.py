"""
ECS-compliant API request logging middleware for TurdParty
Logs all API calls in Elastic Common Schema format for ELK ingestion
"""
import time
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import logging

from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse

logger = logging.getLogger("turdparty.api.requests")


class ECSAPILogger:
    """ECS-compliant API request logger"""
    
    @staticmethod
    def create_ecs_log_entry(
        request: Request,
        response: Response,
        duration_ms: float,
        request_id: str,
        user_agent: Optional[str] = None,
        client_ip: Optional[str] = None,
        error: Optional[Exception] = None
    ) -> Dict[str, Any]:
        """Create ECS-compliant log entry for API requests"""
        
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # Base ECS structure
        ecs_entry = {
            "@timestamp": timestamp,
            "ecs": {
                "version": "8.11.0"
            },
            "event": {
                "kind": "event",
                "category": ["web"],
                "type": ["access"],
                "outcome": "success" if response.status_code < 400 else "failure",
                "duration": int(duration_ms * 1_000_000)  # nanoseconds
            },
            "service": {
                "name": "turdparty-api",
                "type": "web",
                "version": "1.0.0"
            },
            "http": {
                "request": {
                    "method": request.method,
                    "body": {
                        "bytes": getattr(request, '_body_size', 0)
                    }
                },
                "response": {
                    "status_code": response.status_code,
                    "body": {
                        "bytes": getattr(response, 'content_length', 0) or 0
                    }
                },
                "version": "1.1"
            },
            "url": {
                "path": str(request.url.path),
                "query": str(request.url.query) if request.url.query else None,
                "full": str(request.url),
                "scheme": request.url.scheme,
                "domain": request.url.hostname,
                "port": request.url.port
            },
            "user_agent": {
                "original": user_agent or request.headers.get("user-agent", "unknown")
            },
            "client": {
                "ip": client_ip or request.client.host if request.client else "unknown",
                "port": request.client.port if request.client else None
            },
            "labels": {
                "request_id": request_id,
                "api_version": "v1",
                "environment": "development"
            },
            "tags": ["api", "turdparty", "vm-management"]
        }
        
        # Add WebSocket specific fields
        if request.url.path.startswith("/api/v1/vms/") and any(ws_path in request.url.path for ws_path in ["/metrics/stream", "/commands/execute", "/files/"]):
            ecs_entry["labels"]["connection_type"] = "websocket"
            ecs_entry["tags"].append("websocket")
        
        # Add VM-specific context
        if "/vms/" in request.url.path:
            path_parts = request.url.path.split("/")
            if len(path_parts) >= 4 and path_parts[3] != "templates":
                vm_id = path_parts[4] if len(path_parts) > 4 else None
                if vm_id:
                    ecs_entry["labels"]["vm_id"] = vm_id
            ecs_entry["tags"].append("vm-operation")
        
        # Add error information
        if error:
            ecs_entry["error"] = {
                "message": str(error),
                "type": type(error).__name__,
                "stack_trace": None  # Don't log stack traces in production
            }
            ecs_entry["event"]["outcome"] = "failure"
        
        # Add performance metrics
        ecs_entry["performance"] = {
            "response_time_ms": round(duration_ms, 3),
            "slow_request": duration_ms > 1000,  # Flag slow requests
            "status_class": f"{response.status_code // 100}xx"
        }
        
        # Add request headers (filtered)
        safe_headers = {}
        for header, value in request.headers.items():
            if header.lower() not in ['authorization', 'cookie', 'x-api-key']:
                safe_headers[header.lower()] = value
        ecs_entry["http"]["request"]["headers"] = safe_headers
        
        return ecs_entry


class ECSLoggingMiddleware(BaseHTTPMiddleware):
    """FastAPI middleware for ECS-compliant request logging"""
    
    def __init__(self, app, log_level: str = "INFO"):
        super().__init__(app)
        self.logger = logging.getLogger("turdparty.api.ecs")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Configure ECS formatter
        self._setup_ecs_formatter()
    
    def _setup_ecs_formatter(self):
        """Setup ECS JSON formatter for the logger"""
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create new handler with JSON formatter
        handler = logging.StreamHandler()
        
        class ECSFormatter(logging.Formatter):
            def format(self, record):
                if hasattr(record, 'ecs_data'):
                    return json.dumps(record.ecs_data, separators=(',', ':'))
                return super().format(record)
        
        handler.setFormatter(ECSFormatter())
        self.logger.addHandler(handler)
        self.logger.propagate = False
    
    async def dispatch(self, request: Request, call_next) -> StarletteResponse:
        """Process request and log in ECS format"""
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Record start time
        start_time = time.time()
        
        # Get client info
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent")
        
        # Estimate request body size
        content_length = request.headers.get("content-length")
        if content_length:
            request._body_size = int(content_length)
        
        response = None
        error = None
        
        try:
            # Process request
            response = await call_next(request)
            
        except Exception as e:
            error = e
            # Create error response
            response = Response(
                content=json.dumps({"error": "Internal server error", "request_id": request_id}),
                status_code=500,
                media_type="application/json"
            )
        
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000
        
        # Create ECS log entry
        ecs_entry = ECSAPILogger.create_ecs_log_entry(
            request=request,
            response=response,
            duration_ms=duration_ms,
            request_id=request_id,
            user_agent=user_agent,
            client_ip=client_ip,
            error=error
        )
        
        # Log the entry
        log_record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="",
            args=(),
            exc_info=None
        )
        log_record.ecs_data = ecs_entry
        self.logger.handle(log_record)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"


# WebSocket logging helper
class WebSocketECSLogger:
    """ECS logging for WebSocket connections"""
    
    @staticmethod
    def log_websocket_event(
        event_type: str,
        vm_id: str,
        connection_id: str,
        client_ip: str = "unknown",
        user_agent: str = "unknown",
        data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ):
        """Log WebSocket events in ECS format"""
        
        timestamp = datetime.now(timezone.utc).isoformat()
        
        ecs_entry = {
            "@timestamp": timestamp,
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["network"],
                "type": ["connection"],
                "action": event_type,
                "outcome": "success" if not error else "failure"
            },
            "service": {
                "name": "turdparty-api",
                "type": "websocket"
            },
            "network": {
                "protocol": "websocket",
                "transport": "tcp"
            },
            "client": {"ip": client_ip},
            "user_agent": {"original": user_agent},
            "labels": {
                "connection_id": connection_id,
                "vm_id": vm_id,
                "event_type": event_type
            },
            "tags": ["websocket", "vm-management", "real-time"]
        }
        
        if data:
            ecs_entry["websocket"] = {
                "data_size": len(json.dumps(data)) if data else 0,
                "message_type": data.get("type") if data else None
            }
        
        if error:
            ecs_entry["error"] = {"message": error}
        
        # Log the entry
        logger = logging.getLogger("turdparty.websocket.ecs")
        log_record = logging.LogRecord(
            name=logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="",
            args=(),
            exc_info=None
        )
        log_record.ecs_data = ecs_entry
        
        # Use same formatter as API logger
        if not logger.handlers:
            handler = logging.StreamHandler()
            class ECSFormatter(logging.Formatter):
                def format(self, record):
                    if hasattr(record, 'ecs_data'):
                        return json.dumps(record.ecs_data, separators=(',', ':'))
                    return super().format(record)
            handler.setFormatter(ECSFormatter())
            logger.addHandler(handler)
            logger.propagate = False
        
        logger.handle(log_record)
