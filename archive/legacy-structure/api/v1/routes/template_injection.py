"""
API endpoints for template injection
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/template_injection",
    tags=["template_injection"],
    responses={404: {"description": "Template injection not found"}},
)

class TemplateInjectionRequest(BaseModel):
    file_upload_id: str
    template_id: str
    target_path: str
    permissions: str = "0755"

class TemplateInjectionResponse(BaseModel):
    injection_id: str
    file_upload_id: str
    template_id: str
    target_path: str
    permissions: str
    status: str
    created_at: str
    message: str

# Mock injection storage
mock_template_injections = []

@router.get("/", response_model=List[TemplateInjectionResponse])
async def list_template_injections(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
):
    """
    List all template injections with optional filtering.
    
    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    - **status_filter**: Optional status to filter by
    
    Returns:
    - List of template injections
    """
    try:
        filtered_injections = mock_template_injections
        if status_filter:
            filtered_injections = [inj for inj in mock_template_injections if inj["status"] == status_filter]
        
        injections = filtered_injections[skip:skip + limit]
        return injections
    except Exception as e:
        logger.error(f"Error listing template injections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list template injections"
        )

@router.get("/{injection_id}", response_model=TemplateInjectionResponse)
async def get_template_injection(injection_id: str):
    """
    Get a specific template injection by ID.
    
    Parameters:
    - **injection_id**: UUID of the template injection to retrieve
    
    Returns:
    - Template injection object with all details
    """
    try:
        injection = next((inj for inj in mock_template_injections if inj["injection_id"] == injection_id), None)
        if not injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template injection with id {injection_id} not found"
            )
        return injection
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving template injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve template injection"
        )

@router.post("/", response_model=TemplateInjectionResponse, status_code=status.HTTP_201_CREATED)
async def create_template_injection(request: TemplateInjectionRequest):
    """
    Create a new template injection.
    
    This endpoint creates a template injection task that will inject a file
    into a template and process it.
    
    Parameters:
    - **file_upload_id**: ID of the uploaded file to inject
    - **template_id**: ID of the template to use
    - **target_path**: Target path for the file
    - **permissions**: File permissions (default: 0755)
    
    Returns:
    - Created template injection object
    """
    try:
        # Generate unique injection ID
        injection_id = str(uuid.uuid4())
        
        # Create injection record
        injection = {
            "injection_id": injection_id,
            "file_upload_id": request.file_upload_id,
            "template_id": request.template_id,
            "target_path": request.target_path,
            "permissions": request.permissions,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat(),
            "message": "Template injection created successfully"
        }
        
        # Add to mock storage
        mock_template_injections.append(injection)
        
        logger.info(f"Template injection created successfully: {injection_id}")
        return injection
        
    except Exception as e:
        logger.error(f"Error creating template injection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template injection: {str(e)}"
        )

@router.post("/{injection_id}/process")
async def process_template_injection(injection_id: str):
    """
    Process a pending template injection.
    
    This endpoint triggers the actual template injection process.
    
    Parameters:
    - **injection_id**: UUID of the template injection to process
    
    Returns:
    - Updated injection status
    """
    try:
        injection = next((inj for inj in mock_template_injections if inj["injection_id"] == injection_id), None)
        if not injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template injection with id {injection_id} not found"
            )
        
        # Update status to processing
        injection["status"] = "processing"
        injection["message"] = "Template injection is being processed"
        
        logger.info(f"Template injection processing started: {injection_id}")
        return injection
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing template injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process template injection: {str(e)}"
        )

@router.delete("/{injection_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_template_injection(injection_id: str):
    """
    Delete a template injection record.
    
    Parameters:
    - **injection_id**: UUID of the template injection to delete
    
    Returns:
    - No content (204)
    """
    try:
        global mock_template_injections
        original_count = len(mock_template_injections)
        mock_template_injections = [inj for inj in mock_template_injections if inj["injection_id"] != injection_id]
        
        if len(mock_template_injections) == original_count:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template injection with id {injection_id} not found"
            )
        
        logger.info(f"Template injection deleted successfully: {injection_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete template injection"
        )
