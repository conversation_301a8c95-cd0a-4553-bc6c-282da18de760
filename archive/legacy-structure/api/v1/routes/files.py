"""
API endpoints for file management
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/files",
    tags=["files"],
    responses={404: {"description": "File not found"}},
)

class FileResponse(BaseModel):
    file_id: str
    filename: str
    file_size: int
    status: str
    created_at: str
    workflow_job_id: Optional[str] = None

class FileListResponse(BaseModel):
    files: List[FileResponse]
    total: int
    skip: int
    limit: int

# Mock file storage for demonstration
mock_files = [
    {
        "file_id": "0f19f562-f228-41d8-8203-25a2cebca620",
        "filename": "options.js",
        "file_size": 4259,
        "status": "stored",
        "created_at": "2025-06-10T17:22:50.602738",
        "workflow_job_id": None
    },
    {
        "file_id": "1d0ce052-98f7-4899-8b09-24d0c5fdc134",
        "filename": "test-file.txt",
        "file_size": 20,
        "status": "stored",
        "created_at": "2025-06-10T17:01:29.241279",
        "workflow_job_id": None
    }
]

@router.get("/", response_model=FileListResponse)
async def list_files(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """
    List all uploaded files with pagination.
    
    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    
    Returns:
    - Paginated list of files
    """
    try:
        total = len(mock_files)
        files = mock_files[skip:skip + limit]
        
        return FileListResponse(
            files=files,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list files"
        )

@router.get("/{file_id}", response_model=FileResponse)
async def get_file(file_id: str):
    """
    Get file metadata by ID.
    
    Parameters:
    - **file_id**: UUID of the file to retrieve
    
    Returns:
    - File metadata object
    """
    try:
        file_data = next((f for f in mock_files if f["file_id"] == file_id), None)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )
        return file_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )

@router.post("/upload", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None)
):
    """
    Upload a new file.
    
    Parameters:
    - **file**: File to upload
    - **description**: Optional description
    
    Returns:
    - Created file object
    """
    try:
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        
        # Create file record
        file_record = {
            "file_id": file_id,
            "filename": file.filename,
            "file_size": file.size or 0,
            "status": "stored",
            "created_at": datetime.utcnow().isoformat(),
            "workflow_job_id": None
        }
        
        # Add to mock storage
        mock_files.append(file_record)
        
        logger.info(f"File uploaded successfully: {file.filename} (ID: {file_id})")
        return file_record
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )

@router.get("/{file_id}/download")
async def download_file(file_id: str):
    """
    Download a file by ID.
    
    Parameters:
    - **file_id**: UUID of the file to download
    
    Returns:
    - File content as download
    """
    try:
        file_data = next((f for f in mock_files if f["file_id"] == file_id), None)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )
        
        # For demo purposes, return a simple text response
        # In real implementation, this would return the actual file from storage
        return {"message": f"Download would start for file: {file_data['filename']}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download file"
        )

@router.delete("/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file(file_id: str):
    """
    Delete a file by ID.
    
    Parameters:
    - **file_id**: UUID of the file to delete
    
    Returns:
    - No content (204)
    """
    try:
        global mock_files
        original_count = len(mock_files)
        mock_files = [f for f in mock_files if f["file_id"] != file_id]
        
        if len(mock_files) == original_count:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File with id {file_id} not found"
            )
        
        logger.info(f"File deleted successfully: {file_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )
