"""
VM File Operations with streaming support
"""
from fastapi import APIRouter, HTTPException, status, WebSocket, WebSocketDisconnect, UploadFile, File, Form
from typing import Optional
import logging
import asyncio
import json
import os
import tempfile
import hashlib
import time
from datetime import datetime

from api.services.vm_service import vm_service
from api.models.vm_management import FileUploadProgress, FileSystemEvent

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/vms",
    tags=["vm_files"],
    responses={404: {"description": "VM not found"}},
)


class FileTransferManager:
    """Manages file transfers with progress tracking"""
    
    def __init__(self):
        self.active_transfers = {}
    
    async def upload_file_with_progress(
        self, 
        vm_id: str, 
        file: UploadFile, 
        target_path: str,
        websocket: WebSocket
    ):
        """Upload file to VM with real-time progress tracking"""
        transfer_id = f"{vm_id}_{int(time.time())}"
        
        try:
            # Get VM info
            vm = await vm_service.get_vm(vm_id)
            if not vm:
                raise HTTPException(status_code=404, detail="VM not found")
            
            # Initialize progress tracking
            total_size = file.size or 0
            uploaded = 0
            chunk_size = 64 * 1024  # 64KB chunks
            start_time = time.time()
            hasher = hashlib.sha256()
            
            self.active_transfers[transfer_id] = {
                "vm_id": vm_id,
                "file_name": file.filename,
                "total_size": total_size,
                "uploaded": 0,
                "start_time": start_time
            }
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_path = temp_file.name
                
                # Read and write file in chunks
                while chunk := await file.read(chunk_size):
                    temp_file.write(chunk)
                    hasher.update(chunk)
                    uploaded += len(chunk)
                    
                    # Update progress
                    self.active_transfers[transfer_id]["uploaded"] = uploaded
                    
                    # Calculate speed
                    elapsed = time.time() - start_time
                    speed_mbps = (uploaded / (1024 * 1024)) / elapsed if elapsed > 0 else 0
                    
                    # Send progress update
                    progress = FileUploadProgress(
                        progress=(uploaded / total_size) * 100 if total_size > 0 else 0,
                        uploaded=uploaded,
                        total=total_size,
                        speed_mbps=speed_mbps
                    )
                    
                    await websocket.send_json({
                        "type": "upload_progress",
                        **progress.dict()
                    })
                    
                    # Small delay to prevent overwhelming the client
                    await asyncio.sleep(0.1)
            
            # Transfer file to VM
            success = await self._transfer_to_vm(vm_id, vm.vm_type, temp_path, target_path)
            
            # Clean up temp file
            os.unlink(temp_path)
            
            # Send completion message
            await websocket.send_json({
                "type": "upload_complete",
                "success": success,
                "file_path": target_path,
                "checksum": hasher.hexdigest(),
                "total_time": time.time() - start_time
            })
            
        except Exception as e:
            logger.error(f"Error uploading file to VM {vm_id}: {e}")
            await websocket.send_json({
                "type": "upload_error",
                "message": str(e)
            })
        finally:
            # Clean up transfer tracking
            self.active_transfers.pop(transfer_id, None)
    
    async def _transfer_to_vm(self, vm_id: str, vm_type: str, source_path: str, target_path: str) -> bool:
        """Transfer file to VM based on VM type"""
        try:
            if vm_type == "docker":
                return await self._transfer_to_docker(vm_id, source_path, target_path)
            elif vm_type == "vagrant":
                return await self._transfer_to_vagrant(vm_id, source_path, target_path)
            else:
                raise ValueError(f"Unsupported VM type: {vm_type}")
        except Exception as e:
            logger.error(f"Error transferring file to VM {vm_id}: {e}")
            return False
    
    async def _transfer_to_docker(self, vm_id: str, source_path: str, target_path: str) -> bool:
        """Transfer file to Docker container"""
        try:
            import docker
            import tarfile
            import io
            
            client = docker.from_env()
            
            # Find container by VM ID
            containers = client.containers.list(
                filters={"label": f"turdparty.vm_id={vm_id}"}
            )
            
            if not containers:
                raise ValueError(f"No Docker container found for VM {vm_id}")
            
            container = containers[0]
            
            # Create tar archive for docker cp
            tar_buffer = io.BytesIO()
            with tarfile.open(fileobj=tar_buffer, mode='w') as tar:
                tar.add(source_path, arcname=os.path.basename(target_path))
            
            tar_buffer.seek(0)
            
            # Copy to container
            container.put_archive(
                path=os.path.dirname(target_path) or "/tmp",
                data=tar_buffer.getvalue()
            )
            
            logger.info(f"File transferred to Docker container {container.id}: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error transferring file to Docker VM {vm_id}: {e}")
            return False
    
    async def _transfer_to_vagrant(self, vm_id: str, source_path: str, target_path: str) -> bool:
        """Transfer file to Vagrant VM (mock implementation)"""
        try:
            # Mock implementation - in reality would use vagrant upload or SCP
            logger.info(f"Mock file transfer to Vagrant VM {vm_id}: {source_path} -> {target_path}")
            await asyncio.sleep(1)  # Simulate transfer time
            return True
            
        except Exception as e:
            logger.error(f"Error transferring file to Vagrant VM {vm_id}: {e}")
            return False


file_manager = FileTransferManager()


@router.websocket("/{vm_id}/files/upload")
async def upload_file_stream(websocket: WebSocket, vm_id: str):
    """Upload file to VM with real-time progress via WebSocket"""
    await websocket.accept()
    
    try:
        # Wait for file upload initiation
        data = await websocket.receive_text()
        upload_info = json.loads(data)
        
        target_path = upload_info.get("target_path", "/tmp/uploaded_file")
        
        await websocket.send_json({
            "type": "upload_ready",
            "message": "Ready to receive file"
        })
        
        # Note: In a real implementation, you'd need to handle file upload differently
        # This is a simplified example showing the WebSocket communication pattern
        
    except WebSocketDisconnect:
        logger.info(f"File upload WebSocket disconnected for VM {vm_id}")
    except Exception as e:
        logger.error(f"Error in file upload for VM {vm_id}: {e}")
        await websocket.send_json({
            "type": "upload_error",
            "message": str(e)
        })


@router.post("/{vm_id}/files/upload")
async def upload_file_http(
    vm_id: str,
    file: UploadFile = File(...),
    target_path: str = Form(...),
    websocket_id: Optional[str] = Form(None)
):
    """HTTP endpoint for file upload (works with WebSocket for progress)"""
    try:
        # Get VM info
        vm = await vm_service.get_vm(vm_id)
        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")
        
        # For now, do a simple file transfer without WebSocket progress
        # In a full implementation, this would coordinate with the WebSocket
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_path = temp_file.name
        
        # Transfer to VM
        success = await file_manager._transfer_to_vm(vm_id, vm.vm_type, temp_path, target_path)
        
        # Clean up
        os.unlink(temp_path)
        
        if success:
            return {
                "success": True,
                "message": f"File uploaded successfully to {target_path}",
                "file_name": file.filename,
                "file_size": len(content),
                "target_path": target_path
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="File transfer failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file to VM {vm_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"File upload failed: {str(e)}"
        )


@router.websocket("/{vm_id}/files/watch")
async def watch_file_changes(websocket: WebSocket, vm_id: str, path: str = "/tmp"):
    """Watch file system changes in VM"""
    await websocket.accept()
    
    try:
        # Get VM info
        vm = await vm_service.get_vm(vm_id)
        if not vm:
            await websocket.send_json({
                "type": "error",
                "message": "VM not found"
            })
            return
        
        # Start file watching (mock implementation)
        await websocket.send_json({
            "type": "watch_started",
            "vm_id": vm_id,
            "path": path,
            "message": "File system monitoring started"
        })
        
        # Mock file system events
        event_count = 0
        while True:
            await asyncio.sleep(5)  # Check every 5 seconds
            
            # Generate mock file system event
            event_count += 1
            event = FileSystemEvent(
                vm_id=vm_id,
                event_type="modified" if event_count % 2 == 0 else "created",
                file_path=f"{path}/test_file_{event_count}.txt",
                timestamp=int(time.time() * 1000),
                file_size=1024 * event_count,
                permissions="644"
            )
            
            await websocket.send_json({
                "type": "file_event",
                **event.dict()
            })
            
            # Stop after 10 events for demo
            if event_count >= 10:
                break
        
        await websocket.send_json({
            "type": "watch_stopped",
            "message": "File system monitoring stopped"
        })
        
    except WebSocketDisconnect:
        logger.info(f"File watch WebSocket disconnected for VM {vm_id}")
    except Exception as e:
        logger.error(f"Error watching files for VM {vm_id}: {e}")
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })


@router.get("/{vm_id}/files/list")
async def list_vm_files(vm_id: str, path: str = "/tmp"):
    """List files in VM directory"""
    try:
        # Get VM info
        vm = await vm_service.get_vm(vm_id)
        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")
        
        # Mock file listing
        files = [
            {
                "name": "malware_sample.exe",
                "path": f"{path}/malware_sample.exe",
                "size": 2048576,
                "modified": datetime.now().isoformat(),
                "permissions": "755",
                "type": "file"
            },
            {
                "name": "analysis_results",
                "path": f"{path}/analysis_results",
                "size": 0,
                "modified": datetime.now().isoformat(),
                "permissions": "755",
                "type": "directory"
            }
        ]
        
        return {
            "vm_id": vm_id,
            "path": path,
            "files": files,
            "total": len(files)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing files for VM {vm_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list files: {str(e)}"
        )


@router.delete("/{vm_id}/files")
async def delete_vm_file(vm_id: str, file_path: str):
    """Delete file from VM"""
    try:
        # Get VM info
        vm = await vm_service.get_vm(vm_id)
        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")
        
        # Mock file deletion
        logger.info(f"Mock file deletion from VM {vm_id}: {file_path}")
        
        return {
            "success": True,
            "message": f"File deleted successfully: {file_path}",
            "vm_id": vm_id,
            "file_path": file_path
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file from VM {vm_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete file: {str(e)}"
        )
