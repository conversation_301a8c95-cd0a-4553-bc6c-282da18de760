"""
VM Metrics Service for real-time monitoring and gRPC communication
"""
import asyncio
import logging
import time
import psutil
import docker
import subprocess
from typing import Dict, Any, Optional, AsyncGenerator, List
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class VMMetricsService:
    """Service for collecting and streaming VM metrics"""
    
    def __init__(self):
        self.docker_client = None
        self.active_streams: Dict[str, bool] = {}
        
    async def initialize(self):
        """Initialize Docker client and other resources"""
        try:
            self.docker_client = docker.from_env()
            logger.info("VM Metrics Service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None
    
    async def get_vm_metrics(self, vm_id: str, vm_type: str = "docker") -> Dict[str, Any]:
        """Get current VM metrics"""
        try:
            if vm_type == "docker":
                return await self._get_docker_metrics(vm_id)
            elif vm_type == "vagrant":
                return await self._get_vagrant_metrics(vm_id)
            else:
                raise ValueError(f"Unsupported VM type: {vm_type}")
        except Exception as e:
            logger.error(f"Error getting metrics for VM {vm_id}: {e}")
            return self._get_error_metrics(str(e))
    
    async def _get_docker_metrics(self, container_id: str) -> Dict[str, Any]:
        """Get Docker container metrics via API"""
        try:
            if not self.docker_client:
                await self.initialize()
            
            container = self.docker_client.containers.get(container_id)
            stats = container.stats(stream=False)
            
            # Calculate CPU percentage
            cpu_percent = self._calculate_cpu_percent(stats)
            
            # Calculate memory percentage
            memory_stats = stats.get('memory_stats', {})
            memory_usage = memory_stats.get('usage', 0)
            memory_limit = memory_stats.get('limit', 1)
            memory_percent = (memory_usage / memory_limit) * 100 if memory_limit > 0 else 0
            
            # Network stats
            networks = stats.get('networks', {})
            network_rx = sum(net.get('rx_bytes', 0) for net in networks.values())
            network_tx = sum(net.get('tx_bytes', 0) for net in networks.values())
            
            # Get process list (simplified for Docker)
            top_processes = await self._get_docker_processes(container)
            
            return {
                "vm_id": container_id,
                "vm_type": "docker",
                "timestamp": int(time.time() * 1000),
                "status": container.status,
                "cpu_percent": round(cpu_percent, 2),
                "memory_percent": round(memory_percent, 2),
                "memory_used_bytes": memory_usage,
                "memory_limit_bytes": memory_limit,
                "memory_used_mb": round(memory_usage / (1024 * 1024), 2),
                "network_rx_bytes": network_rx,
                "network_tx_bytes": network_tx,
                "top_processes": top_processes,
                "uptime_seconds": self._get_container_uptime(container)
            }
            
        except Exception as e:
            logger.error(f"Error getting Docker metrics for {container_id}: {e}")
            return self._get_error_metrics(str(e))
    
    async def _get_vagrant_metrics(self, vm_id: str) -> Dict[str, Any]:
        """Get Vagrant VM metrics via SSH"""
        try:
            # For now, return mock data - will be replaced with actual SSH/gRPC implementation
            return {
                "vm_id": vm_id,
                "vm_type": "vagrant",
                "timestamp": int(time.time() * 1000),
                "status": "running",
                "cpu_percent": 25.5,
                "memory_percent": 45.2,
                "memory_used_bytes": 1073741824,  # 1GB
                "memory_limit_bytes": 2147483648,  # 2GB
                "memory_used_mb": 1024.0,
                "network_rx_bytes": 1048576,
                "network_tx_bytes": 524288,
                "top_processes": [
                    {"pid": 1234, "name": "systemd", "cpu_percent": 0.1, "memory_mb": 12.5},
                    {"pid": 5678, "name": "sshd", "cpu_percent": 0.2, "memory_mb": 8.3}
                ],
                "uptime_seconds": 3600
            }
        except Exception as e:
            logger.error(f"Error getting Vagrant metrics for {vm_id}: {e}")
            return self._get_error_metrics(str(e))
    
    def _calculate_cpu_percent(self, stats: Dict) -> float:
        """Calculate CPU percentage from Docker stats"""
        try:
            cpu_stats = stats.get('cpu_stats', {})
            precpu_stats = stats.get('precpu_stats', {})
            
            cpu_usage = cpu_stats.get('cpu_usage', {})
            precpu_usage = precpu_stats.get('cpu_usage', {})
            
            cpu_delta = cpu_usage.get('total_usage', 0) - precpu_usage.get('total_usage', 0)
            system_delta = cpu_stats.get('system_cpu_usage', 0) - precpu_stats.get('system_cpu_usage', 0)
            
            if system_delta > 0 and cpu_delta > 0:
                cpu_count = len(cpu_usage.get('percpu_usage', [1]))
                return (cpu_delta / system_delta) * cpu_count * 100.0
            return 0.0
        except Exception:
            return 0.0
    
    async def _get_docker_processes(self, container) -> List[Dict[str, Any]]:
        """Get process list from Docker container"""
        try:
            # Execute 'ps' command in container
            exec_result = container.exec_run("ps aux --no-headers", demux=True)
            if exec_result.exit_code == 0 and exec_result.output[0]:
                processes = []
                lines = exec_result.output[0].decode().strip().split('\n')
                
                for line in lines[:10]:  # Top 10 processes
                    parts = line.split(None, 10)
                    if len(parts) >= 11:
                        processes.append({
                            "pid": int(parts[1]),
                            "name": parts[10].split()[0] if parts[10] else "unknown",
                            "cpu_percent": float(parts[2]) if parts[2].replace('.', '').isdigit() else 0.0,
                            "memory_mb": float(parts[3]) if parts[3].replace('.', '').isdigit() else 0.0
                        })
                
                return processes
        except Exception as e:
            logger.error(f"Error getting Docker processes: {e}")
        
        return []
    
    def _get_container_uptime(self, container) -> int:
        """Get container uptime in seconds"""
        try:
            started_at = container.attrs['State']['StartedAt']
            start_time = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
            uptime = datetime.now(start_time.tzinfo) - start_time
            return int(uptime.total_seconds())
        except Exception:
            return 0
    
    def _get_error_metrics(self, error_message: str) -> Dict[str, Any]:
        """Return error metrics when collection fails"""
        return {
            "vm_id": "unknown",
            "vm_type": "unknown",
            "timestamp": int(time.time() * 1000),
            "status": "error",
            "error": error_message,
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "memory_used_bytes": 0,
            "memory_limit_bytes": 0,
            "memory_used_mb": 0.0,
            "network_rx_bytes": 0,
            "network_tx_bytes": 0,
            "top_processes": [],
            "uptime_seconds": 0
        }
    
    async def stream_vm_metrics(self, vm_id: str, vm_type: str = "docker", interval: float = 1.0) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream VM metrics in real-time"""
        stream_key = f"{vm_id}:{vm_type}"
        self.active_streams[stream_key] = True
        
        try:
            logger.info(f"Starting metrics stream for VM {vm_id} (type: {vm_type})")
            
            while self.active_streams.get(stream_key, False):
                metrics = await self.get_vm_metrics(vm_id, vm_type)
                yield metrics
                await asyncio.sleep(interval)
                
        except Exception as e:
            logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
            yield self._get_error_metrics(str(e))
        finally:
            self.active_streams.pop(stream_key, None)
            logger.info(f"Stopped metrics stream for VM {vm_id}")
    
    def stop_stream(self, vm_id: str, vm_type: str = "docker"):
        """Stop metrics streaming for a VM"""
        stream_key = f"{vm_id}:{vm_type}"
        self.active_streams[stream_key] = False
        logger.info(f"Requested stop for metrics stream: {stream_key}")


# Global instance
vm_metrics_service = VMMetricsService()
