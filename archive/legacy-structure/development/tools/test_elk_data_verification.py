#!/usr/bin/env python3
"""
ELK Data Verification Test

This script verifies that ELK data capture is working properly by:
1. Running a single integration test
2. Storing data in Elasticsearch
3. Retrieving and verifying the stored data
4. Confirming all expected fields are present
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from tests.conftest import load_test_config
from tests.integration.test_app_downloader import ApplicationDownloader, MinIOTestManager
from tests.integration.test_vm_injection import VagrantVMManager, ELKDataManager
from tests.integration.test_benchmark_persistence import BenchmarkManager


async def test_elk_data_capture():
    """Test complete ELK data capture and verification."""
    print("🔍 ELK Data Verification Test")
    print("=" * 40)
    
    # Load configuration
    config = load_test_config()
    
    # Initialize managers
    downloader = ApplicationDownloader(config)
    minio_manager = MinIOTestManager(config)
    vm_manager = VagrantVMManager(config)
    elk_manager = ELKDataManager(config)
    benchmark_manager = BenchmarkManager(config)
    
    # Test application (small file for quick testing)
    test_app = {
        "name": "test-firefox",
        "description": "Firefox for ELK testing",
        "url": "https://download.mozilla.org/?product=firefox-stub&os=win&lang=en-US",
        "filename": "Firefox Installer.exe",
        "type": "exe",
        "install_command": "file /tmp/'Firefox Installer.exe' && echo 'ELK test completed'"
    }
    
    try:
        print("\n📥 Step 1: Download test application...")
        file_path, metadata = await downloader.download_file(
            test_app['url'], test_app['filename']
        )
        print(f"✅ Downloaded: {metadata['file_size']} bytes")
        print(f"   Blake3: {metadata['blake3_hash'][:16]}...")
        print(f"   UUID: {metadata['file_uuid']}")
        
        print("\n📤 Step 2: Upload to MinIO...")
        bucket_name = minio_manager.create_test_bucket()
        upload_result = minio_manager.upload_file_with_metadata(
            bucket_name, file_path, metadata
        )
        print(f"✅ Uploaded to bucket: {bucket_name}")
        print(f"   Object: {upload_result['object_name']}")
        
        print("\n🐳 Step 3: Create test VM...")
        vm_name = f"elk-test-{int(time.time())}"
        vm_info = vm_manager.create_windows_vm(vm_name)
        print(f"✅ VM created: {vm_info['vm_name']}")
        print(f"   Boot time: {vm_info['boot_time_seconds']:.2f}s")
        
        print("\n💉 Step 4: Inject file to VM...")
        injection_result = vm_manager.inject_file_to_vm(
            vm_name, file_path, f"C:\\temp\\{file_path.name}"
        )
        print(f"✅ File injected in {injection_result['injection_time_seconds']:.2f}s")
        
        print("\n⚙️ Step 5: Install application...")
        install_result = vm_manager.install_application_in_vm(
            vm_name, test_app, file_path
        )
        print(f"✅ Installation completed in {install_result['install_time_seconds']:.2f}s")
        print(f"   Success: {install_result['install_success']}")
        print(f"   Footprint: {len(install_result['footprint']['filesystem_changes'])} files")
        
        print("\n📊 Step 6: Store data in ELK...")
        test_data = {
            'test_type': 'elk-verification',
            'platform': 'windows',
            'application': test_app,
            'download_metadata': metadata,
            'minio_upload': {
                'object_name': upload_result['object_name'],
                'upload_time_seconds': upload_result['upload_time_seconds'],
                'verified_size': upload_result['verified_size'],
                'etag': upload_result['etag'],
                'metadata': dict(upload_result['metadata'])  # Convert HTTPHeaderDict
            },
            'vm_info': vm_info,
            'injection_result': injection_result,
            'install_result': install_result,
            'verification_timestamp': time.time()
        }
        
        elk_doc_id = elk_manager.store_test_data('elk-verification', test_data)
        print(f"✅ Data stored in ELK")
        print(f"   Document ID: {elk_doc_id}")
        
        print("\n🔍 Step 7: Verify data in ELK...")
        # Wait a moment for indexing
        await asyncio.sleep(2)
        
        # Query the data back
        retrieved_data = elk_manager.query_test_data('elk-verification', hours_back=1)
        print(f"✅ Retrieved {len(retrieved_data)} documents from ELK")
        
        if retrieved_data:
            latest_doc = retrieved_data[0]
            print(f"   Latest document timestamp: {latest_doc.get('timestamp')}")
            print(f"   Application: {latest_doc.get('application', {}).get('name')}")
            print(f"   Platform: {latest_doc.get('platform')}")
            print(f"   File UUID: {latest_doc.get('download_metadata', {}).get('file_uuid')}")
            print(f"   Blake3 hash: {latest_doc.get('download_metadata', {}).get('blake3_hash', '')[:16]}...")
            
            # Verify all expected fields are present
            expected_fields = [
                'test_type', 'platform', 'application', 'download_metadata',
                'minio_upload', 'vm_info', 'injection_result', 'install_result'
            ]
            
            missing_fields = [field for field in expected_fields if field not in latest_doc]
            if missing_fields:
                print(f"❌ Missing fields: {missing_fields}")
                return False
            else:
                print("✅ All expected fields present in ELK document")
        
        print("\n📈 Step 8: Record benchmarks...")
        benchmark_manager.record_download_benchmark(
            test_app['name'], metadata['file_size'], metadata['download_time_seconds']
        )
        benchmark_manager.record_upload_benchmark(
            test_app['name'], metadata['file_size'], upload_result['upload_time_seconds']
        )
        benchmark_manager.record_vm_benchmark(
            platform='windows',
            boot_time=vm_info['boot_time_seconds'],
            injection_time=injection_result['injection_time_seconds'],
            install_time=install_result['install_time_seconds'],
            install_success=install_result['install_success'],
            footprint_size=len(install_result['footprint']['filesystem_changes'])
        )
        print("✅ Benchmarks recorded")
        
        print("\n🧹 Step 9: Cleanup...")
        vm_manager.destroy_vm(vm_name)
        minio_manager.cleanup_test_buckets()
        print("✅ Cleanup completed")
        
        print("\n🎉 ELK Data Verification SUCCESSFUL!")
        print("=" * 40)
        print("✅ File downloaded and hashed with Blake3")
        print("✅ File uploaded to MinIO with UUID metadata")
        print("✅ VM created and file injected successfully")
        print("✅ Application installation attempted")
        print("✅ Data stored in Elasticsearch successfully")
        print("✅ Data retrieved from Elasticsearch successfully")
        print("✅ All expected fields present in ELK document")
        print("✅ Benchmarks recorded persistently")
        print("✅ Resources cleaned up properly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ELK verification failed: {e}")
        # Cleanup on failure
        try:
            vm_manager.destroy_vm(vm_name)
            minio_manager.cleanup_test_buckets()
        except:
            pass
        return False


async def verify_elk_query_capabilities():
    """Test ELK query capabilities."""
    print("\n🔍 Testing ELK Query Capabilities...")
    
    config = load_test_config()
    elk_manager = ELKDataManager(config)
    
    try:
        # Test basic connectivity
        from elasticsearch import Elasticsearch
        es_client = Elasticsearch([config['ELASTICSEARCH_URL']])
        
        # Check cluster health
        health = es_client.cluster.health()
        print(f"✅ Cluster status: {health['status']}")
        print(f"   Nodes: {health['number_of_nodes']}")
        
        # List indices
        indices = es_client.cat.indices(format='json')
        print(f"✅ Available indices: {len(indices)}")
        for index in indices:
            if 'turdparty' in index['index']:
                print(f"   - {index['index']}: {index['docs.count']} docs")
        
        # Query recent data
        recent_data = elk_manager.query_test_data('elk-verification', hours_back=24)
        print(f"✅ Recent verification data: {len(recent_data)} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ ELK query test failed: {e}")
        return False


async def main():
    """Run ELK verification tests."""
    print("🚀 TurdParty ELK Data Verification Suite")
    print("=" * 50)
    
    # Test 1: ELK query capabilities
    query_success = await verify_elk_query_capabilities()
    
    # Test 2: Complete data capture workflow
    if query_success:
        capture_success = await test_elk_data_capture()
    else:
        print("⚠️ Skipping data capture test due to ELK connectivity issues")
        capture_success = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ELK Verification Summary")
    print("=" * 50)
    print(f"ELK Query Test: {'✅ PASS' if query_success else '❌ FAIL'}")
    print(f"Data Capture Test: {'✅ PASS' if capture_success else '❌ FAIL'}")
    
    if query_success and capture_success:
        print("\n🎉 ALL ELK VERIFICATION TESTS PASSED!")
        print("The complete API→MinIO→VM→ELK pipeline is working perfectly!")
        return 0
    else:
        print("\n⚠️ Some ELK verification tests failed.")
        print("Check Elasticsearch connectivity and configuration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
