#!/usr/bin/env node

// Simple frontend test using puppeteer-like approach
// Tests the TurdParty React UI workflow pages

const { execSync } = require('child_process');
const fs = require('fs');

// Colors for output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function printColored(color, text) {
    console.log(`${colors[color]}${text}${colors.reset}`);
}

async function testFrontendPages() {
    printColored('blue', '🧪 Testing TurdParty React UI Workflow Pages');
    console.log('');

    const baseUrl = 'http://localhost:3000';
    let passedTests = 0;
    let totalTests = 0;

    // Test 1: Main page accessibility
    totalTests++;
    printColored('yellow', '📋 Test 1: Main Page Accessibility');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/"`, { encoding: 'utf8' });
        if (response.includes('TurdParty') && response.includes('root')) {
            printColored('green', '✅ Main page loads: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Main page content: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Main page accessibility: FAIL');
    }

    // Test 2: Static assets loading
    totalTests++;
    printColored('yellow', '📋 Test 2: Static Assets (React Bundle)');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/"`, { encoding: 'utf8' });
        if (response.includes('static/js/main') && response.includes('static/css/main')) {
            printColored('green', '✅ React bundle assets: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ React bundle assets: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Static assets loading: FAIL');
    }

    // Test 3: Health endpoint
    totalTests++;
    printColored('yellow', '📋 Test 3: Health Endpoint');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/health"`, { encoding: 'utf8' });
        if (response.trim() === 'healthy') {
            printColored('green', '✅ Health endpoint: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Health endpoint response: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Health endpoint: FAIL');
    }

    // Test 4: API proxy configuration
    totalTests++;
    printColored('yellow', '📋 Test 4: API Proxy Configuration');
    try {
        // Test if API proxy is configured (should get a response, even if 404)
        execSync(`curl -f -s "${baseUrl}/api/v1/health"`, { encoding: 'utf8' });
        printColored('green', '✅ API proxy responds: PASS');
        passedTests++;
    } catch (error) {
        // Check if it's a proper proxy error (404, 502, etc.) vs connection error
        try {
            const response = execSync(`curl -s "${baseUrl}/api/v1/health"`, { encoding: 'utf8', stdio: 'pipe' });
            if (response.includes('404') || response.includes('502') || response.includes('503')) {
                printColored('green', '✅ API proxy configured: PASS (expected error)');
                passedTests++;
            } else {
                printColored('red', '❌ API proxy configuration: FAIL');
            }
        } catch (e) {
            printColored('red', '❌ API proxy configuration: FAIL');
        }
    }

    // Test 5: React Router configuration
    totalTests++;
    printColored('yellow', '📋 Test 5: React Router (SPA routing)');
    try {
        // Test that unknown routes return the main HTML (React Router handles routing)
        const response = execSync(`curl -f -s "${baseUrl}/file_upload"`, { encoding: 'utf8' });
        if (response.includes('TurdParty') && response.includes('root')) {
            printColored('green', '✅ React Router SPA: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ React Router SPA: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ React Router configuration: FAIL');
    }

    // Test 6: Workflow page routes
    totalTests++;
    printColored('yellow', '📋 Test 6: Workflow Page Routes');
    const routes = ['/file_selection', '/vm_injection', '/vm_status'];
    let routesPassed = 0;
    
    for (const route of routes) {
        try {
            const response = execSync(`curl -f -s "${baseUrl}${route}"`, { encoding: 'utf8' });
            if (response.includes('TurdParty') && response.includes('root')) {
                routesPassed++;
            }
        } catch (error) {
            // Route failed
        }
    }
    
    if (routesPassed === routes.length) {
        printColored('green', '✅ Workflow page routes: PASS');
        passedTests++;
    } else {
        printColored('red', `❌ Workflow page routes: FAIL (${routesPassed}/${routes.length})`);
    }

    // Test 7: Container status
    totalTests++;
    printColored('yellow', '📋 Test 7: Container Health Status');
    try {
        const response = execSync(`docker ps --filter "name=turdpartycollab_frontend" --format "{{.Status}}"`, { encoding: 'utf8' });
        if (response.includes('healthy') || response.includes('Up')) {
            printColored('green', '✅ Container health: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Container health: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Container status check: FAIL');
    }

    // Test 8: Traefik integration
    totalTests++;
    printColored('yellow', '📋 Test 8: Traefik Service Discovery');
    try {
        const response = execSync(`curl -s http://localhost:8080/api/http/services`, { encoding: 'utf8' });
        if (response.includes('turdparty-frontend') && response.includes('***********:3000')) {
            printColored('green', '✅ Traefik service discovery: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Traefik service discovery: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Traefik integration: FAIL');
    }

    // Summary
    console.log('');
    printColored('blue', '📊 Test Results Summary:');
    printColored('blue', `   Total Tests: ${totalTests}`);
    printColored('green', `   Passed: ${passedTests}`);
    printColored('red', `   Failed: ${totalTests - passedTests}`);
    
    const successRate = Math.round((passedTests / totalTests) * 100);
    console.log('');
    
    if (successRate >= 80) {
        printColored('green', `🎉 Frontend Tests: ${successRate}% SUCCESS`);
        printColored('green', '✅ TurdParty React UI is working correctly!');
    } else if (successRate >= 60) {
        printColored('yellow', `⚠️  Frontend Tests: ${successRate}% PARTIAL SUCCESS`);
        printColored('yellow', '⚠️  Some issues detected but core functionality works');
    } else {
        printColored('red', `❌ Frontend Tests: ${successRate}% FAILED`);
        printColored('red', '❌ Significant issues detected');
    }

    console.log('');
    printColored('blue', '🌐 Access URLs:');
    printColored('blue', '   Direct: http://localhost:3000');
    printColored('blue', '   Domain: http://frontend.turdparty.localhost (requires /etc/hosts)');
    console.log('');
    printColored('blue', '📋 Workflow Pages Available:');
    printColored('blue', '   📁 File Upload: http://localhost:3000/file_upload');
    printColored('blue', '   📋 File Selection: http://localhost:3000/file_selection');
    printColored('blue', '   🖥️ VM Injection: http://localhost:3000/vm_injection');
    printColored('blue', '   📊 VM Status: http://localhost:3000/vm_status');
    console.log('');

    return successRate >= 80;
}

// Run the tests
testFrontendPages().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    printColored('red', `❌ Test execution failed: ${error.message}`);
    process.exit(1);
});
