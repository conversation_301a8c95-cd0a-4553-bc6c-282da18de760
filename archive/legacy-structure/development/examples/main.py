import sys
import os
import uvicorn
import logging
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent.absolute()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

def main():
    """Main application entry point."""
    logger.info("Starting TurdParty FastAPI application")
    logger.info(f"Current directory: {os.getcwd()}")
    logger.info(f"Python path: {sys.path}")

    try:
        # Import application module
        from api.v1.application import get_application
        logger.info("Successfully imported get_application")

        # Initialize application
        app = get_application()
        logger.info("Application initialized successfully")
        return app
    except Exception as e:
        logger.error(f"Application initialization failed: {e}", exc_info=True)
        raise

# Create the application
app = main()

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
