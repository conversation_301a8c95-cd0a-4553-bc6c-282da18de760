# Docker Configuration

This directory contains all Docker-related files for the TurdParty project.

## Dockerfiles

### `Dockerfile`
Main production Dockerfile for the TurdParty API service.
- **Purpose**: Production deployment of the API
- **Base**: Python 3.10 slim
- **Usage**: Used by `docker-compose.yml` for the `api` service
- **Features**: Optimised for production with minimal dependencies

### `Dockerfile.dev`
Development Dockerfile for the devcontainer environment.
- **Purpose**: VS Code devcontainer with full development toolchain
- **Base**: Microsoft devcontainer Python 3.10
- **Usage**: Used by `.devcontainer/docker-compose.dev.yml`
- **Features**: 
  - Docker-in-Docker support
  - Full development tools (ruff, mypy, pytest, playwright)
  - VS Code extensions and configuration
  - Development utilities and aliases

### `Dockerfile.frontend`
Frontend application Dockerfile.
- **Purpose**: Production deployment of the frontend
- **Base**: Node.js with nginx
- **Usage**: Used by `docker-compose.yml` for the `frontend` service
- **Features**: Multi-stage build for optimised production bundle

## Directory Structure

```
docker/
├── README.md           # This file
├── Dockerfile          # Production API container
├── Dockerfile.dev      # Development container
└── Dockerfile.frontend # Frontend container
```

## Usage

### Development Environment
The development container is automatically used when opening the project in VS Code with the Dev Containers extension:

```bash
# VS Code will automatically detect and offer to reopen in container
code .
```

### Production Deployment
Use docker-compose from the project root:

```bash
# Start all services
docker-compose up -d

# Build and start specific service
docker-compose up --build api
```

### Building Individual Images

```bash
# Build API image
docker build -f docker/Dockerfile -t turdparty-api .

# Build development image
docker build -f docker/Dockerfile.dev -t turdparty-dev .

# Build frontend image
docker build -f docker/Dockerfile.frontend -t turdparty-frontend ./frontend
```

## Network Configuration

All containers use the `turdpartycollab_net` network and are also connected to `traefik_network` for reverse proxy support.

## Volumes

Development containers use persistent volumes for:
- VS Code extensions
- Command history
- Python packages
- Node modules

See `.devcontainer/docker-compose.dev.yml` for detailed volume configuration.
