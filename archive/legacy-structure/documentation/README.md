# TurdParty Documentation

This directory contains organized documentation for the TurdParty project.

## Structure

- `status/` - Current project status and progress reports
- `guides/` - Development and user guides
- `references/` - Technical references and specifications

## Main Documentation

The primary documentation is built using Sphinx and located in the `docs/` directory.
Access it via:

- **Local**: http://localhost:8000/docs/
- **Build**: `cd docs && source venv/bin/activate && sphinx-build -b html . _build/html`

## Quick Links

- [Getting Started](../docs/getting-started/index.rst)
- [API Reference](../docs/getting-started/api-reference.rst)
- [Platform Components](../docs/platform/components.rst)
- [Testing Documentation](../tests/docs/)
