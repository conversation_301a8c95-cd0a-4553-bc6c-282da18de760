# Development Workflow

## Branch Strategy

### Local Development Branch: `develop/local`
- **Purpose:** Local development and experimentation
- **Rule:** NEVER push this branch to remote
- **Usage:** All local commits and work-in-progress changes

### Feature Branches: `feature/xxx`
- **Purpose:** Specific features ready for remote collaboration
- **Rule:** Push to remote when ready for review/collaboration
- **Usage:** Clean, tested, documented features

## Current Branch Status

```bash
* develop/local                           # ← Current branch (LOCAL ONLY)
  feature/001-add-dockers-for-basic-eval  # ← Last pushed feature branch
  master                                  # ← Main branch
```

## Workflow Commands

### Local Development
```bash
# Work on develop/local (current)
git add .
git commit -m "Local development changes"
# ⚠️  NEVER: git push origin develop/local

# Switch between branches
git checkout develop/local              # Local work
git checkout feature/001-add-dockers-for-basic-eval  # Feature work
git checkout master                     # Main branch
```

### Feature Development
```bash
# Create new feature branch from current state
git checkout -b feature/new-feature-name

# When ready to share
git push origin feature/new-feature-name
```

### Merging Local Work to Feature Branch
```bash
# From develop/local, create a new feature branch
git checkout -b feature/my-new-feature

# Clean up commits if needed
git rebase -i HEAD~n  # where n is number of commits to squash

# Push feature branch
git push origin feature/my-new-feature
```

## Important Reminders

1. **develop/local** = Local experimentation only
2. **feature/xxx** = Ready for remote collaboration
3. Always commit locally first, then decide if it should go to remote
4. Use meaningful commit messages even for local commits
5. Keep develop/local as a safe space for trying things out

## Git Configuration

- **Email:** <EMAIL>
- **Name:** CvR
- **Remote:** **************:tenbahtsecurity/turdparty.git
