[tool:pytest]
# Pytest configuration for TurdParty VM WebSocket testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=services.api.src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=70
    --durations=10
    --asyncio-mode=auto
    --numprocesses=auto
    --maxfail=5
    --dist=worksteal

# Markers
markers =
    unit: Unit tests
    integration: Integration tests  
    websocket: WebSocket functionality tests
    performance: Performance and benchmark tests
    benchmark: Benchmark tests (requires pytest-benchmark)
    scalability: Scalability tests
    resource: Resource utilization tests
    slow: Slow running tests (use --runslow to run)
    docker: Tests requiring Docker
    vagrant: Tests requiring Vagrant
    api: API endpoint tests
    real_vm: Tests using real VMs (not mocked)
    real: Tests using real implementations (no mocks)
    parallel: Tests safe for parallel execution
    serial: Tests that must run serially
    concurrent: Tests that test concurrent behavior
    stress: Stress tests with high resource usage
    mock: Tests using mocked components (discouraged)
    stress: Stress tests
    security: Security tests

# Test timeout
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:websockets.*
    ignore::UserWarning:httpx.*
    ignore::pydantic.warnings.PydanticDeprecatedSince20
    ignore::pytest.PytestUnhandledCoroutineWarning
    ignore::RuntimeWarning:asyncio.*
    # Warn about mock usage to encourage real implementations
    default::UserWarning:.*mock.*

# Coverage configuration
[coverage:run]
source = services.api.src
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */settings/*
    */manage.py
    */wsgi.py
    */asgi.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
