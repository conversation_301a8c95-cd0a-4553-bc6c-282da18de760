# TurdParty Core Services
# Phase 1: API, Database, Cache, Storage

services:
  # API Service
  api:
    container_name: turdpartycollab_api
    build:
      context: ../services/api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@database:5432/turdparty

      # MinIO
      - MINIO_HOST=storage
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false

      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0

      # Application
      - PYTHONPATH=/app
      - DEBUG=true
      - PORT=8000
      - HOST=0.0.0.0
    volumes:
      - ../data/uploads:/app/uploads
      - ../data/logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
      storage:
        condition: service_healthy
      cache:
        condition: service_healthy
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-api.rule=Host(`api.turdparty.localhost`)"
      - "traefik.http.routers.turdparty-api.entrypoints=web"
      - "traefik.http.services.turdparty-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik_network"

  # PostgreSQL Database
  database:
    container_name: turdpartycollab_database
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=turdparty
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - database_data:/var/lib/postgresql/data
      - ../config/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d turdparty"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.turdparty-database.rule=HostSNI(`database.turdparty.localhost`)"
      - "traefik.tcp.routers.turdparty-database.entrypoints=postgres"
      - "traefik.tcp.services.turdparty-database.loadbalancer.server.port=5432"
      - "traefik.docker.network=traefik_network"

  # Redis Cache/Queue
  cache:
    container_name: turdpartycollab_cache
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - cache_data:/data
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    command: redis-server --appendonly yes
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.turdparty-cache.rule=HostSNI(`cache.turdparty.localhost`)"
      - "traefik.tcp.routers.turdparty-cache.entrypoints=redis"
      - "traefik.tcp.services.turdparty-cache.loadbalancer.server.port=6379"
      - "traefik.docker.network=traefik_network"

  # MinIO Storage
  storage:
    container_name: turdpartycollab_storage
    image: minio/minio:latest
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_BROWSER_REDIRECT_URL=http://storage.turdparty.localhost:9001
    volumes:
      - storage_data:/data
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    command: server /data --console-address ":9001"
    labels:
      - "traefik.enable=true"
      # MinIO API
      - "traefik.http.routers.turdparty-storage-api.rule=Host(`storage.turdparty.localhost`)"
      - "traefik.http.routers.turdparty-storage-api.entrypoints=web"
      - "traefik.http.routers.turdparty-storage-api.service=turdparty-storage-api"
      - "traefik.http.services.turdparty-storage-api.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.turdparty-storage-console.rule=Host(`storage.turdparty.localhost`) && PathPrefix(`/console`)"
      - "traefik.http.routers.turdparty-storage-console.entrypoints=web"
      - "traefik.http.routers.turdparty-storage-console.service=turdparty-storage-console"
      - "traefik.http.services.turdparty-storage-console.loadbalancer.server.port=9001"
      - "traefik.docker.network=traefik_network"

  # Status Page - System Monitoring Dashboard
  status:
    container_name: turdpartycollab_status
    image: nginx:alpine
    volumes:
      - ../services/status:/usr/share/nginx/html:ro
      - ../services/status/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8090:80"
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    depends_on:
      - database
      - cache
      - storage
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-status.rule=Host(`status.turdparty.localhost`)"
      - "traefik.http.routers.turdparty-status.entrypoints=web"
      - "traefik.http.services.turdparty-status.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"

  # React Frontend - User Interface
  frontend:
    container_name: turdpartycollab_frontend
    build:
      context: ..
      dockerfile: services/frontend/Dockerfile
    ports:
      - "3000:3000"
    networks:
      - turdpartycollab_network
      - traefik_network
    restart: unless-stopped
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-frontend.rule=Host(`frontend.turdparty.localhost`)"
      - "traefik.http.routers.turdparty-frontend.entrypoints=web"
      - "traefik.http.services.turdparty-frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik_network"

  # Vagrant gRPC service for VM management
  # Set VAGRANT_MODE=container to use this service, or VAGRANT_MODE=host (default) to use host vagrant
  vagrant:
    build:
      context: ../services/vagrant
      dockerfile: Dockerfile
    container_name: turdpartycollab_vagrant
    privileged: true
    ports:
      - "${VAGRANT_GRPC_PORT:-40000}:40000"
    environment:
      - VAGRANT_SERVE_HOST=0.0.0.0
      - VAGRANT_SERVE_PORT=40000
      - VAGRANT_HOME=/vagrant/.vagrant
    volumes:
      - ../vagrant:/vagrant
      - vagrant_data:/vagrant/.vagrant
      - /var/run/libvirt:/var/run/libvirt
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    networks:
      - turdpartycollab_network
    profiles:
      - vagrant-container
    labels:
      - "traefik.enable=false"
    healthcheck:
      test: ["CMD", "/usr/local/bin/entrypoint.sh", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Named volumes for data persistence
volumes:
  database_data:
    name: turdpartycollab_database_data
  cache_data:
    name: turdpartycollab_cache_data
  storage_data:
    name: turdpartycollab_storage_data
  vagrant_data:
    name: turdpartycollab_vagrant_data

# Networks for service communication
networks:
  turdpartycollab_network:
    name: turdpartycollab_network
    driver: bridge
  traefik_network:
    name: traefik_network
    external: true
