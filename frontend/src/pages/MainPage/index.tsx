import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './styles.css';
import {
  Card, Button, Spin, Alert, Upload, Form, Input, Select,
  Steps, Divider, Switch, Typography, message, Space, Row, Col, Radio, Tabs
} from 'antd';
import {
  UploadOutlined, SendOutlined, FileOutlined, FolderOutlined,
  DesktopOutlined, BulbOutlined, BulbFilled,
  RocketOutlined, CheckCircleOutlined, InboxOutlined,
  DashboardOutlined, ReloadOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import PerformanceWidget from '../../components/PerformanceWidget';
import ServiceStatusIndicator from '../../components/ServiceStatusIndicator';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import API_ENDPOINTS from '../../utils/apiConfig';
import type { RadioChangeEvent } from 'antd';
import FileUpload from '../../components/FileUpload';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { TextArea } = Input;
const { Dragger } = Upload;
const { TabPane } = Tabs;

interface FileInfo {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  download_url: string;
}

interface TemplateInfo {
  id: string;
  name: string;
  description: string;
  category: string;
  value?: string;
}

interface FileUploadResponse {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  file_hash: string;
  description: string;
  download_url: string;
  created_on: string;
}

const MainPage: React.FC = () => {
  const navigate = useNavigate();
  const { token, refreshToken } = useAuth();

  const [currentStep, setCurrentStep] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState<boolean>(localStorage.getItem('darkMode') === 'true');
  const [refreshingToken, setRefreshingToken] = useState(false);

  // File upload state
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [fileDescription, setFileDescription] = useState<string>('');
  const [uploadedFileId, setUploadedFileId] = useState<string | null>(null);
  const [uploadType, setUploadType] = useState<'file' | 'folder'>('file');

  // Template selection state
  const [templates, setTemplates] = useState<TemplateInfo[]>([
    { id: 'ubuntu_2004', name: 'Ubuntu 20.04 LTS', description: 'Ubuntu 20.04 LTS (Focal Fossa) - Long term support release', category: 'os', value: 'ubuntu_2004' },
    { id: 'ubuntu_2204', name: 'Ubuntu 22.04 LTS', description: 'Ubuntu 22.04 LTS (Jammy Jellyfish) - Latest LTS release', category: 'os', value: 'ubuntu_2204' },
    { id: 'debian_11', name: 'Debian 11', description: 'Debian 11 (Bullseye) - Stable Debian release', category: 'os', value: 'debian_11' },
    { id: 'centos_7', name: 'CentOS 7', description: 'CentOS 7 - Enterprise-focused Linux distribution', category: 'os', value: 'centos_7' },
    { id: 'centos_stream_9', name: 'CentOS Stream 9', description: 'CentOS Stream 9 - Continuous delivery distribution', category: 'os', value: 'centos_stream_9' },
    { id: 'fedora_37', name: 'Fedora 37', description: 'Fedora 37 - Latest Fedora Workstation release', category: 'os', value: 'fedora_37' },
    { id: 'windows_10', name: 'Windows 10', description: 'Windows 10 Professional - Modern Windows desktop OS', category: 'os', value: 'windows_10' },
    { id: 'windows_server_2019', name: 'Windows Server 2019', description: 'Windows Server 2019 - Enterprise server OS', category: 'os', value: 'windows_server_2019' }
  ]);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [selectedTemplateName, setSelectedTemplateName] = useState<string | null>(null);
  const [templateCategory, setTemplateCategory] = useState<string>('os');

  // Target configuration state
  const [targetPath, setTargetPath] = useState<string>('/app');
  const [targetPermissions, setTargetPermissions] = useState<string>('0755');

  useEffect(() => {
    // Apply dark mode to /docs iframe if it exists
    const docsFrame = document.getElementById('docs-frame') as HTMLIFrameElement;
    if (docsFrame) {
      try {
        const frameDoc = docsFrame.contentDocument || docsFrame.contentWindow?.document;
        if (frameDoc) {
          if (darkMode) {
            frameDoc.body.classList.add('dark-mode');
          } else {
            frameDoc.body.classList.remove('dark-mode');
          }
        }
      } catch (e) {
        console.error('Cannot access iframe document:', e);
      }
    }

    // Save preference
    localStorage.setItem('darkMode', darkMode.toString());
  }, [darkMode]);

  // Template selection handlers
  const handleTemplateSelect = (templateId: string) => {
    console.log('Selected template ID:', templateId);

    // Find the template in our templates array
    const template = templates.find(t => t.id === templateId);
    if (template) {
      console.log('Found template:', template);
      setSelectedTemplate(templateId);
      setSelectedTemplateName(template.name);

      // Store in localStorage to preserve across steps
      localStorage.setItem('selectedTemplate', templateId);
      localStorage.setItem('selectedTemplateName', template.name);

      // Debug log state after update
      setTimeout(() => {
        console.log('After selection - State:', {
          selectedTemplate,
          selectedTemplateName,
          localStorage: {
            selectedTemplate: localStorage.getItem('selectedTemplate'),
            selectedTemplateName: localStorage.getItem('selectedTemplateName')
          }
        });
      }, 0);
    } else {
      console.log('Template not found in templates array. Templates:', templates);
    }
  };

  // Ensure template selection persists between steps
  useEffect(() => {
    // Only try to restore template when moving to the configuration step
    if (currentStep === 2) {
      const storedTemplate = localStorage.getItem('selectedTemplate');
      const storedTemplateName = localStorage.getItem('selectedTemplateName');

      console.log('Checking localStorage on step 2:', {
        storedTemplate,
        storedTemplateName
      });

      if (storedTemplate) {
        console.log('Restoring template from localStorage on step change to 2');
        setSelectedTemplate(storedTemplate);

        if (storedTemplateName) {
          setSelectedTemplateName(storedTemplateName);
        } else {
          // If we somehow have template ID but not name, look it up
          const template = templates.find(t => t.id === storedTemplate);
          if (template) {
            setSelectedTemplateName(template.name);
            localStorage.setItem('selectedTemplateName', template.name);
          }
        }
      } else {
        console.warn('No template found in localStorage on step 2');
      }
    }
  }, [currentStep, templates]);

  const handleDarkModeToggle = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());

    // Dispatch a custom event to notify App component
    window.dispatchEvent(new Event('darkModeChange'));
  };

  const handleRefreshToken = async () => {
    setRefreshingToken(true);
    setError(null);

    try {
      console.log('MainPage: Attempting to refresh token...');
      const success = await refreshToken();
      console.log('MainPage: Token refresh result:', success);

      if (success) {
        message.success('Successfully refreshed authentication token');
        // Clear any previous errors
        setError(null);
        // Update UI to show success
        setSuccess('Authentication successful. You can now upload files.');
      } else {
        console.error('MainPage: Token refresh failed');
        setError('Failed to refresh authentication token. Please try again or reload the page.');
        message.error('Failed to refresh authentication token');
      }
    } catch (err) {
      console.error('MainPage: Error refreshing token:', err);
      setError('Error refreshing authentication token. Please reload the page and try again.');
    } finally {
      setRefreshingToken(false);
    }
  };

  const handleFileUploadSuccess = (response: any) => {
    console.log('MainPage: handleFileUploadSuccess called with response:', response);
    console.log('MainPage: Setting uploadedFileId to:', response.id);
    setUploadedFileId(response.id);
    setSuccess(`File uploaded successfully: ${response.filename}`);
    setCurrentStep(1); // Move to next step

    // Store in localStorage as backup
    if (response.id) {
      localStorage.setItem('uploadedFileId', response.id);
      localStorage.setItem('uploadedFileName', response.filename || 'unknown');
    }
  };

  const handleFileUploadError = (error: any) => {
    console.error('Upload error:', error);
    // Error handling is managed by the FileUpload component
  };

  const handleTemplateConfirm = () => {
    if (!selectedTemplate) {
      message.error('Please select a template');
      return;
    }

    console.log('Confirming template selection:', selectedTemplate, selectedTemplateName);
    setCurrentStep(2);
  };

  const filteredTemplates = templateCategory === 'all'
    ? templates
    : templates.filter(t => t.category === templateCategory);

  // Target configuration handlers
  const handleTargetConfirm = async () => {
    console.log('MainPage: handleTargetConfirm called');
    console.log('MainPage: uploadedFileId:', uploadedFileId);
    console.log('MainPage: selectedTemplate:', selectedTemplate);
    console.log('MainPage: localStorage uploadedFileId:', localStorage.getItem('uploadedFileId'));
    console.log('MainPage: localStorage selectedTemplate:', localStorage.getItem('selectedTemplate'));

    // Try to recover from localStorage if state is missing
    const fileId = uploadedFileId || localStorage.getItem('uploadedFileId');
    const templateId = selectedTemplate || localStorage.getItem('selectedTemplate');

    if (!fileId || !templateId) {
      console.error('MainPage: Missing required data:', { fileId, templateId });
      message.error('Missing upload or template selection');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Debug logging to help diagnose API issues
      console.log('Submitting VM injection with data:', {
        file_upload_id: uploadedFileId,
        template_id: selectedTemplate,
        target_path: targetPath,
        permissions: targetPermissions
      });

      // Use the correct API endpoint with proper error handling
      const apiUrl = process.env.REACT_APP_API_URL || '';
      console.log(`Using API URL: ${apiUrl}`);

      try {
        // Use the consolidated API endpoint for VM injection
        console.log('Sending VM injection request with data:', {
          file_upload_id: uploadedFileId,
          template_id: selectedTemplate,
          target_path: targetPath,
          permissions: targetPermissions
        });

        const response = await axios.post(`${apiUrl}/api/v1/virtual-machines/injections/`, {
          file_upload_id: fileId,
          template_id: templateId,
          target_path: targetPath,
          permissions: targetPermissions
        }, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 15000 // Increase timeout to 15 seconds
        });

      console.log('VM injection created successfully:', response.data);
      setSuccess(`VM injection created successfully with ID: ${response.data.id}`);
      setCurrentStep(3);
      } catch (err: any) {
        console.error('Error creating VM injection:', err);

        // Enhanced error handling with more specific messages
        let errorMessage = 'Failed to create VM injection. Please try again.';

        if (err.message && err.message.includes('Network Error')) {
          errorMessage = 'Network error: Unable to connect to the VM injection service. Please check your connection and try again.';
        } else if (err.response) {
          // Server responded with an error
          if (err.response.status === 404) {
            errorMessage = 'VM injection service not found. The service might be unavailable.';
          } else if (err.response.status === 401) {
            errorMessage = 'Authentication error. Please refresh your token and try again.';
          } else if (err.response.data && err.response.data.detail) {
            errorMessage = `Error: ${err.response.data.detail}`;
          }
        } else if (err.request) {
          // Request was made but no response received
          errorMessage = 'No response from server. The VM injection service might be down.';
        }

        setError(errorMessage);
      }
    } catch (error: any) {
      console.error('Outer error handling:', error);
      setError('An unexpected error occurred. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const resetWorkflow = () => {
    setCurrentStep(0);
    setFileList([]);
    setFileDescription('');
    setUploadedFileId(null);
    setSelectedTemplate(null);
    setSelectedTemplateName(null);
    setTargetPath('/app');
    setTargetPermissions('0755');
    setSuccess(null);
    setError(null);

    // Clear localStorage template storage
    localStorage.removeItem('selectedTemplate');
    localStorage.removeItem('selectedTemplateName');
  };

  return (
    <div className={`main-page ${darkMode ? 'dark-mode' : ''}`}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card className="welcome-card">
            <div className="welcome-header">
              <div>
                <Title level={2}>Welcome to TurdParty</Title>
                <Paragraph>
                  Your one-stop solution for file management, template injection, and VM operations.
                </Paragraph>
              </div>
              <div className="dark-mode-toggle">
                <Space>
                  <Text>Dark Mode</Text>
                  <Switch
                    checked={darkMode}
                    onChange={handleDarkModeToggle}
                    checkedChildren={<BulbFilled />}
                    unCheckedChildren={<BulbOutlined />}
                  />
                </Space>
              </div>
            </div>

            {error && (
              <Alert
                message="Error"
                description={
                  <div>
                    {error}
                    {error.includes('Authentication') && (
                      <div style={{ marginTop: '10px' }}>
                        <Button
                          type="primary"
                          icon={<ReloadOutlined />}
                          onClick={handleRefreshToken}
                          loading={refreshingToken}
                        >
                          Refresh Authentication
                        </Button>
                      </div>
                    )}
                  </div>
                }
                type="error"
                showIcon
                closable
                style={{ marginBottom: '16px' }}
              />
            )}

            {success && (
              <Alert
                message="Success"
                description={success}
                type="success"
                showIcon
                closable
                style={{ marginBottom: '16px' }}
              />
            )}

            <Steps current={currentStep} style={{ marginBottom: '24px' }}>
              <Step title="Upload" description="Upload file or folder" />
              <Step title="Template" description="Select a template" />
              <Step title="Configure" description="Set target options" />
              <Step title="Complete" description="Review and finish" />
            </Steps>

            {/* Step 0: File Upload */}
            {currentStep === 0 && (
              <Card
                title={<Title level={4}>Step 1: Upload File or Folder</Title>}
                className="main-card upload-card"
                bordered={false}
              >
                {loading ? (
                  <div className="centered-spinner">
                    <Spin size="large" />
                    <p>Processing upload...</p>
                  </div>
                ) : (
                  <>
                    {error && (
                      <Alert
                        message="Error"
                        description={error}
                        type="error"
                        showIcon
                        closable
                        onClose={() => setError(null)}
                        className="margin-bottom"
                      />
                    )}

                    {success && (
                      <Alert
                        message="Success"
                        description={success}
                        type="success"
                        showIcon
                        closable
                        onClose={() => setSuccess(null)}
                        className="margin-bottom"
                      />
                    )}

                    <Radio.Group
                      onChange={(e: RadioChangeEvent) => setUploadType(e.target.value)}
                      value={uploadType}
                      className="upload-type-selector"
                    >
                      <Radio.Button value="file">
                        <FileOutlined /> Single File
                      </Radio.Button>
                      <Radio.Button value="folder">
                        <FolderOutlined /> Folder
                      </Radio.Button>
                    </Radio.Group>

                    <div className="upload-container">
                      {token ? (
                        <FileUpload
                          onUploadSuccess={handleFileUploadSuccess}
                          onUploadError={handleFileUploadError}
                          multiple={uploadType === 'folder'}
                          directory={uploadType === 'folder'}
                          maxFileSize={200 * 1024 * 1024} // 200MB
                          token={token}
                        />
                      ) : (
                        <Alert
                          message="Authentication Error"
                          description={
                            <div>
                              Unable to upload files: Authentication token is missing. Please try refreshing your token or reload the page.
                              <div style={{ marginTop: '10px' }}>
                                <Button
                                  type="primary"
                                  icon={<ReloadOutlined />}
                                  onClick={handleRefreshToken}
                                  loading={refreshingToken}
                                >
                                  Get Authentication Token
                                </Button>
                              </div>
                            </div>
                          }
                          type="error"
                          showIcon
                        />
                      )}
                    </div>
                  </>
                )}
              </Card>
            )}

            {/* Step 2: Template Selection */}
            {currentStep === 1 && (
              <div className="step-content">
                <Title level={4}>Select a Template</Title>
                <div style={{ marginBottom: '16px' }}>
                  <Select
                    value={templateCategory}
                    onChange={(value: string) => setTemplateCategory(value)}
                    style={{ width: 200 }}
                  >
                    <Select.Option value="all">All Categories</Select.Option>
                    <Select.Option value="os">Operating Systems</Select.Option>
                  </Select>
                </div>

                <div className="template-grid">
                  {filteredTemplates.map(template => (
                    <Card
                      key={template.id}
                      className={`template-card ${selectedTemplate === template.id ? 'selected' : ''}`}
                      hoverable
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <div className="template-icon">
                        {template.category === 'os' ? <DesktopOutlined /> : <DashboardOutlined />}
                      </div>
                      <div className="template-info">
                        <Title level={5}>{template.name}</Title>
                        <Text type="secondary">{template.description}</Text>
                      </div>
                      {selectedTemplate === template.id && (
                        <div className="template-selected">
                          <CheckCircleOutlined />
                        </div>
                      )}
                    </Card>
                  ))}
                </div>

                <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  <Button onClick={() => setCurrentStep(0)}>
                    Back
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleTemplateConfirm}
                    disabled={!selectedTemplate}
                    icon={<SendOutlined />}
                  >
                    Continue
                  </Button>
                </div>
              </div>
            )}

            {/* Step 3: Target Configuration */}
            {currentStep === 2 && (
              <div className="step-content">
                <Title level={4}>Configure Target</Title>
                {/* Force check localStorage again */}
                {(() => {
                  // Immediate function to allow local variables
                  const localStorageTemplate = localStorage.getItem('selectedTemplate');
                  const localStorageTemplateName = localStorage.getItem('selectedTemplateName');

                  // If localStorage has values but state doesn't, update state
                  if (localStorageTemplate && !selectedTemplate) {
                    console.log('Fixing missing selectedTemplate from localStorage');
                    setTimeout(() => setSelectedTemplate(localStorageTemplate), 0);
                  }

                  if (localStorageTemplateName && !selectedTemplateName) {
                    console.log('Fixing missing selectedTemplateName from localStorage');
                    setTimeout(() => setSelectedTemplateName(localStorageTemplateName), 0);
                  }

                  return null; // Don't render anything
                })()}

                {selectedTemplate || localStorage.getItem('selectedTemplate') ? (
                  <Alert
                    message={`Selected template: ${selectedTemplateName || localStorage.getItem('selectedTemplateName') || selectedTemplate || localStorage.getItem('selectedTemplate')}`}
                    description={`Template ID: ${selectedTemplate || localStorage.getItem('selectedTemplate')}`}
                    type="info"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                ) : (
                  <Alert
                    message="No template selected. Please go back and select a template."
                    description="This could be due to browser storage issues or a problem with template selection."
                    type="warning"
                    showIcon
                    style={{ marginBottom: '16px' }}
                    action={
                      <Button size="small" onClick={() => setCurrentStep(1)}>
                        Go Back to Template Selection
                      </Button>
                    }
                  />
                )}

                <div style={{ marginBottom: '16px', padding: '10px', border: '1px solid #d9d9d9', borderRadius: '4px' }}>
                  <Text strong>Debug Information:</Text>
                  <pre style={{ whiteSpace: 'pre-wrap', marginTop: '8px' }}>
                    {JSON.stringify({
                      uploadedFileId,
                      selectedTemplate,
                      selectedTemplateName,
                      localStorage: {
                        uploadedFileId: localStorage.getItem('uploadedFileId'),
                        uploadedFileName: localStorage.getItem('uploadedFileName'),
                        selectedTemplate: localStorage.getItem('selectedTemplate'),
                        selectedTemplateName: localStorage.getItem('selectedTemplateName')
                      }
                    }, null, 2)}
                  </pre>
                </div>

                <Form layout="vertical">
                  <Form.Item label="Target Path">
                    <Input
                      value={targetPath}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTargetPath(e.target.value)}
                      placeholder="/app"
                      prefix={<FolderOutlined />}
                    />
                  </Form.Item>

                  <Form.Item label="File Permissions">
                    <Input
                      value={targetPermissions}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTargetPermissions(e.target.value)}
                      placeholder="0755"
                    />
                  </Form.Item>

                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Button onClick={() => setCurrentStep(1)}>
                      Back
                    </Button>
                    <Button
                      type="primary"
                      onClick={handleTargetConfirm}
                      loading={loading}
                      icon={<RocketOutlined />}
                    >
                      Create Injection
                    </Button>
                  </div>
                </Form>
              </div>
            )}

            {/* Step 4: Completion */}
            {currentStep === 3 && (
              <div className="step-content">
                <div style={{ textAlign: 'center', padding: '24px' }}>
                  <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                  <Title level={3} style={{ marginTop: '16px' }}>Template Injection Complete!</Title>
                  <Paragraph>
                    Your template has been successfully injected. You can now proceed to the VM status page to check the results.
                  </Paragraph>
                  <div style={{ marginTop: '24px' }}>
                    <Space>
                      <Button onClick={resetWorkflow}>
                        Start New Workflow
                      </Button>
                      <Button type="primary" onClick={() => navigate('/vm_status')}>
                        Go to VM Status
                      </Button>
                      <Button
                        type="default"
                        onClick={async () => {
                          try {
                            const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3055';
                            const response = await axios.get(`${apiUrl}/api/v1/virtual-machines/injections/debug`, {
                              headers: {
                                'Authorization': `Bearer ${token}`,
                              }
                            });
                            console.log('Debug info:', response.data);
                            message.info('Debug info logged to console');
                          } catch (err) {
                            console.error('Error fetching debug info:', err);
                            message.error('Failed to fetch debug info');
                          }
                        }}
                      >
                        Debug Injections
                      </Button>
                    </Space>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </Col>

        <Col span={24}>
          <PerformanceWidget />
        </Col>

        <Col span={24}>
          <Card title="Quick Actions" className="quick-actions-card">
            <div className="quick-actions-grid">
              <Button type="primary" onClick={() => navigate('/file_upload')} icon={<UploadOutlined />}>
                File Upload
              </Button>
              <Button type="primary" onClick={() => navigate('/file_selection')} icon={<FileOutlined />}>
                File Selection
              </Button>
              <Button type="primary" onClick={() => navigate('/vagrant_vm')} icon={<DesktopOutlined />}>
                Vagrant VM
              </Button>
              <Button type="primary" onClick={() => navigate('/vm_injection')} icon={<RocketOutlined />}>
                VM Injection
              </Button>
              <Button type="primary" onClick={() => navigate('/vm_status')} icon={<DashboardOutlined />}>
                VM Status
              </Button>
              <Button type="primary" onClick={() => navigate('/docs')} icon={<FileOutlined />}>
                Documentation
              </Button>
              <Button type="primary" onClick={() => navigate('/status')} icon={<DashboardOutlined />}>
                System Status
              </Button>
              <Button type="primary" onClick={() => navigate('/performance')} icon={<DashboardOutlined />}>
                Performance
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
      <ServiceStatusIndicator />
    </div>
  );
};

export default MainPage;