"""
Service for handling file injection operations
"""
import os
import uuid
import hashlib
import logging
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

from services.api.src.models.file_injection import (
    FileInjectionCreate,
    FileInjectionResponse,
    FileInjectionStatus,
    InjectionStatus
)

logger = logging.getLogger(__name__)

class FileInjectionService:
    """Service for managing file injections"""
    
    def __init__(self):
        self.upload_dir = Path(os.getenv("FILE_UPLOAD_DIR", "/app/uploads"))
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # In-memory storage for demo purposes
        # In production, this would be a database
        self._injections: Dict[str, Dict[str, Any]] = {}
        
    async def get_all(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        status_filter: Optional[str] = None
    ) -> List[FileInjectionResponse]:
        """Get all file injections with optional filtering"""
        injections = list(self._injections.values())
        
        # Apply status filter if provided
        if status_filter:
            injections = [
                inj for inj in injections 
                if inj.get("status") == status_filter
            ]
        
        # Apply pagination
        injections = injections[skip:skip + limit]
        
        return [
            FileInjectionResponse(**injection) 
            for injection in injections
        ]
    
    async def get_by_id(self, injection_id: str) -> Optional[FileInjectionResponse]:
        """Get a file injection by ID"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            return None
        
        return FileInjectionResponse(**injection_data)
    
    async def create_injection(
        self, 
        injection_data: FileInjectionCreate, 
        file_content: bytes
    ) -> FileInjectionResponse:
        """Create a new file injection"""
        
        # Generate unique ID
        injection_id = str(uuid.uuid4())
        
        # Calculate file hash
        file_hash = hashlib.sha256(file_content).hexdigest()
        
        # Save file to upload directory
        file_path = self.upload_dir / f"{injection_id}_{injection_data.filename}"
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # Create injection record
        now = datetime.utcnow()
        injection_record = {
            "id": injection_id,
            "filename": injection_data.filename,
            "target_path": injection_data.target_path,
            "permissions": injection_data.permissions,
            "status": InjectionStatus.PENDING,
            "description": injection_data.description,
            "created_at": now,
            "updated_at": now,
            "file_size": len(file_content),
            "file_hash": file_hash,
            "file_path": str(file_path)
        }
        
        self._injections[injection_id] = injection_record
        
        logger.info(f"Created file injection {injection_id} for file {injection_data.filename}")
        
        return FileInjectionResponse(**injection_record)
    
    async def get_status(self, injection_id: str) -> Optional[FileInjectionStatus]:
        """Get the status of a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            return None
        
        # Calculate progress based on status
        progress_map = {
            InjectionStatus.PENDING: 0,
            InjectionStatus.IN_PROGRESS: 50,
            InjectionStatus.COMPLETED: 100,
            InjectionStatus.FAILED: 0
        }
        
        return FileInjectionStatus(
            id=injection_id,
            status=injection_data["status"],
            progress=progress_map.get(injection_data["status"], 0),
            message=injection_data.get("message", ""),
            details=injection_data.get("details", {}),
            updated_at=injection_data["updated_at"]
        )
    
    async def process_injection(self, injection_id: str) -> FileInjectionStatus:
        """Process a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            raise ValueError(f"Injection {injection_id} not found")
        
        if injection_data["status"] != InjectionStatus.PENDING:
            raise ValueError(f"Injection {injection_id} is not in pending status")
        
        try:
            # Update status to in progress
            injection_data["status"] = InjectionStatus.IN_PROGRESS
            injection_data["updated_at"] = datetime.utcnow()
            injection_data["message"] = "Processing file injection"
            
            logger.info(f"Processing injection {injection_id}")
            
            # Simulate processing steps
            await self._simulate_processing(injection_id)
            
            # Copy file to target location (simulated)
            target_path = Path(injection_data["target_path"])
            source_path = Path(injection_data["file_path"])
            
            # In a real implementation, this would copy to the actual target
            # For demo purposes, we'll just log the operation
            logger.info(f"Would copy {source_path} to {target_path}")
            
            # Set permissions (simulated)
            permissions = injection_data["permissions"]
            logger.info(f"Would set permissions {permissions} on {target_path}")
            
            # Update status to completed
            injection_data["status"] = InjectionStatus.COMPLETED
            injection_data["updated_at"] = datetime.utcnow()
            injection_data["message"] = "File injection completed successfully"
            injection_data["details"] = {
                "file_path": str(source_path),
                "target_path": str(target_path),
                "permissions": permissions,
                "processed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Completed injection {injection_id}")
            
        except Exception as e:
            # Update status to failed
            injection_data["status"] = InjectionStatus.FAILED
            injection_data["updated_at"] = datetime.utcnow()
            injection_data["message"] = f"Processing failed: {str(e)}"
            injection_data["details"] = {"error": str(e)}
            
            logger.error(f"Failed to process injection {injection_id}: {str(e)}")
            raise
        
        return await self.get_status(injection_id)
    
    async def _simulate_processing(self, injection_id: str):
        """Simulate processing steps with delays"""
        steps = [
            "Validating file",
            "Preparing target location",
            "Copying file",
            "Setting permissions"
        ]
        
        injection_data = self._injections[injection_id]
        
        for i, step in enumerate(steps):
            injection_data["message"] = step
            injection_data["details"] = {
                "current_step": step,
                "step_number": i + 1,
                "total_steps": len(steps)
            }
            injection_data["updated_at"] = datetime.utcnow()
            
            # Simulate processing time
            await asyncio.sleep(0.5)
    
    async def delete(self, injection_id: str):
        """Delete a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            raise ValueError(f"Injection {injection_id} not found")
        
        # Remove uploaded file
        file_path = Path(injection_data["file_path"])
        if file_path.exists():
            file_path.unlink()
        
        # Remove from storage
        del self._injections[injection_id]
        
        logger.info(f"Deleted injection {injection_id}")
