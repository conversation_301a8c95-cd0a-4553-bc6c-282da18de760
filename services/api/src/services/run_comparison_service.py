"""Service for managing multiple analysis runs and generating comparison reports."""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session

from ..models.analysis_run import AnalysisRun, RunComparison
from ..models.workflow_job import WorkflowJob

logger = logging.getLogger(__name__)


class RunComparisonService:
    """Service for tracking and comparing multiple analysis runs."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def create_analysis_run(self, file_uuid: str, workflow_job_id: str, 
                          binary_metadata: Dict[str, Any]) -> AnalysisRun:
        """Create a new analysis run record."""
        
        # Get next run number
        run_number = AnalysisRun.get_next_run_number(self.db, file_uuid)
        
        # Create new run
        analysis_run = AnalysisRun(
            file_uuid=file_uuid,
            run_number=run_number,
            workflow_job_id=workflow_job_id,
            binary_filename=binary_metadata.get('filename', 'unknown'),
            binary_hash_sha256=binary_metadata.get('sha256'),
            binary_size_bytes=binary_metadata.get('size_bytes'),
            vm_template=binary_metadata.get('vm_template', 'default')
        )
        
        self.db.add(analysis_run)
        self.db.commit()
        
        logger.info(f"Created analysis run #{run_number} for UUID {file_uuid}")
        return analysis_run
    
    def complete_analysis_run(self, run_id: str, results: Dict[str, Any]) -> AnalysisRun:
        """Complete an analysis run with results."""
        
        analysis_run = self.db.query(AnalysisRun).filter(
            AnalysisRun.id == run_id
        ).first()
        
        if not analysis_run:
            raise ValueError(f"Analysis run not found: {run_id}")
        
        # Update run with results
        analysis_run.completed_at = datetime.utcnow()
        analysis_run.status = "completed"
        
        # Extract metrics from results
        analysis_run.execution_duration_seconds = results.get('execution_duration_seconds')
        analysis_run.installer_runtime_seconds = results.get('installer_runtime_seconds')
        analysis_run.ecs_events_count = results.get('ecs_events_count', 0)
        analysis_run.files_created_count = results.get('files_created_count', 0)
        analysis_run.registry_keys_count = results.get('registry_keys_count', 0)
        analysis_run.processes_spawned_count = results.get('processes_spawned_count', 0)
        
        # Store detailed results
        analysis_run.analysis_results = results.get('analysis_results', {})
        analysis_run.performance_metrics = results.get('performance_metrics', {})
        analysis_run.security_indicators = results.get('security_indicators', {})
        
        # Calculate differences from previous run
        analysis_run.calculate_diff_from_previous(self.db)
        
        self.db.commit()
        
        logger.info(f"Completed analysis run #{analysis_run.run_number} for UUID {analysis_run.file_uuid}")
        return analysis_run
    
    def get_run_history(self, file_uuid: str, limit: int = 10) -> List[AnalysisRun]:
        """Get run history for a file UUID."""
        return AnalysisRun.get_run_history(self.db, file_uuid, limit)
    
    def compare_runs(self, run_a_id: str, run_b_id: str) -> RunComparison:
        """Create detailed comparison between two runs."""
        
        run_a = self.db.query(AnalysisRun).filter(AnalysisRun.id == run_a_id).first()
        run_b = self.db.query(AnalysisRun).filter(AnalysisRun.id == run_b_id).first()
        
        if not run_a or not run_b:
            raise ValueError("One or both runs not found")
        
        if run_a.file_uuid != run_b.file_uuid:
            raise ValueError("Cannot compare runs from different files")
        
        # Create comparison
        comparison = RunComparison(
            file_uuid=run_a.file_uuid,
            run_a_id=run_a.id,
            run_b_id=run_b.id,
            comparison_type="manual"
        )
        
        # Generate detailed diffs
        comparison.files_diff = self._compare_files(run_a, run_b)
        comparison.registry_diff = self._compare_registry(run_a, run_b)
        comparison.process_diff = self._compare_processes(run_a, run_b)
        comparison.performance_diff = self._compare_performance(run_a, run_b)
        
        # Calculate summary metrics
        total_changes = 0
        change_categories = []
        
        if comparison.files_diff.get('changes'):
            total_changes += len(comparison.files_diff['changes'])
            change_categories.append("files")
        
        if comparison.registry_diff.get('changes'):
            total_changes += len(comparison.registry_diff['changes'])
            change_categories.append("registry")
        
        if comparison.process_diff.get('changes'):
            total_changes += len(comparison.process_diff['changes'])
            change_categories.append("processes")
        
        if comparison.performance_diff.get('significant_changes'):
            total_changes += len(comparison.performance_diff['significant_changes'])
            change_categories.append("performance")
        
        comparison.total_changes = total_changes
        comparison.change_categories = change_categories
        comparison.severity_score = min(total_changes * 10, 100)  # 10 points per change, max 100
        
        self.db.add(comparison)
        self.db.commit()
        
        logger.info(f"Created comparison between runs {run_a.run_number} and {run_b.run_number}")
        return comparison
    
    def _compare_files(self, run_a: AnalysisRun, run_b: AnalysisRun) -> Dict[str, Any]:
        """Compare file creation between two runs."""
        
        files_a = run_a.analysis_results.get('files_created', []) if run_a.analysis_results else []
        files_b = run_b.analysis_results.get('files_created', []) if run_b.analysis_results else []
        
        # Convert to sets for comparison
        set_a = set(files_a)
        set_b = set(files_b)
        
        return {
            "added_files": list(set_b - set_a),
            "removed_files": list(set_a - set_b),
            "common_files": list(set_a & set_b),
            "changes": list((set_a - set_b) | (set_b - set_a)),
            "count_change": len(files_b) - len(files_a)
        }
    
    def _compare_registry(self, run_a: AnalysisRun, run_b: AnalysisRun) -> Dict[str, Any]:
        """Compare registry changes between two runs."""
        
        registry_a = run_a.analysis_results.get('registry_keys', []) if run_a.analysis_results else []
        registry_b = run_b.analysis_results.get('registry_keys', []) if run_b.analysis_results else []
        
        set_a = set(registry_a)
        set_b = set(registry_b)
        
        return {
            "added_keys": list(set_b - set_a),
            "removed_keys": list(set_a - set_b),
            "common_keys": list(set_a & set_b),
            "changes": list((set_a - set_b) | (set_b - set_a)),
            "count_change": len(registry_b) - len(registry_a)
        }
    
    def _compare_processes(self, run_a: AnalysisRun, run_b: AnalysisRun) -> Dict[str, Any]:
        """Compare process execution between two runs."""
        
        processes_a = run_a.analysis_results.get('processes', []) if run_a.analysis_results else []
        processes_b = run_b.analysis_results.get('processes', []) if run_b.analysis_results else []
        
        # Extract process names for comparison
        names_a = set([p.get('name', '') for p in processes_a])
        names_b = set([p.get('name', '') for p in processes_b])
        
        return {
            "added_processes": list(names_b - names_a),
            "removed_processes": list(names_a - names_b),
            "common_processes": list(names_a & names_b),
            "changes": list((names_a - names_b) | (names_b - names_a)),
            "count_change": len(processes_b) - len(processes_a)
        }
    
    def _compare_performance(self, run_a: AnalysisRun, run_b: AnalysisRun) -> Dict[str, Any]:
        """Compare performance metrics between two runs."""
        
        metrics = {}
        significant_changes = []
        
        # Compare execution duration (significant if >10% change)
        if run_a.execution_duration_seconds and run_b.execution_duration_seconds:
            duration_change = ((run_b.execution_duration_seconds - run_a.execution_duration_seconds) / 
                             run_a.execution_duration_seconds) * 100
            
            metrics['execution_duration'] = {
                'run_a': run_a.execution_duration_seconds,
                'run_b': run_b.execution_duration_seconds,
                'change_percent': duration_change
            }
            
            if abs(duration_change) > 10:
                significant_changes.append({
                    'metric': 'execution_duration',
                    'change_percent': duration_change,
                    'severity': 'high' if abs(duration_change) > 50 else 'medium'
                })
        
        # Compare installer runtime
        if run_a.installer_runtime_seconds and run_b.installer_runtime_seconds:
            installer_change = ((run_b.installer_runtime_seconds - run_a.installer_runtime_seconds) / 
                              run_a.installer_runtime_seconds) * 100
            
            metrics['installer_runtime'] = {
                'run_a': run_a.installer_runtime_seconds,
                'run_b': run_b.installer_runtime_seconds,
                'change_percent': installer_change
            }
            
            if abs(installer_change) > 10:
                significant_changes.append({
                    'metric': 'installer_runtime',
                    'change_percent': installer_change,
                    'severity': 'high' if abs(installer_change) > 50 else 'medium'
                })
        
        return {
            'metrics': metrics,
            'significant_changes': significant_changes,
            'has_performance_regression': any(c['change_percent'] > 20 for c in significant_changes)
        }
    
    def get_anomalous_runs(self, threshold: int = 50) -> List[AnalysisRun]:
        """Get runs with high anomaly scores."""
        return self.db.query(AnalysisRun).filter(
            AnalysisRun.anomaly_score >= threshold
        ).order_by(AnalysisRun.anomaly_score.desc()).all()
    
    def get_run_statistics(self, file_uuid: str) -> Dict[str, Any]:
        """Get statistics for all runs of a file UUID."""
        
        runs = self.get_run_history(file_uuid, limit=100)
        
        if not runs:
            return {"error": "No runs found"}
        
        # Calculate statistics
        execution_times = [r.execution_duration_seconds for r in runs if r.execution_duration_seconds]
        file_counts = [r.files_created_count for r in runs if r.files_created_count]
        registry_counts = [r.registry_keys_count for r in runs if r.registry_keys_count]
        
        return {
            "total_runs": len(runs),
            "successful_runs": len([r for r in runs if r.status == "completed"]),
            "failed_runs": len([r for r in runs if r.status == "failed"]),
            "anomalous_runs": len([r for r in runs if r.anomaly_score > 30]),
            "execution_time_stats": {
                "min": min(execution_times) if execution_times else None,
                "max": max(execution_times) if execution_times else None,
                "avg": sum(execution_times) / len(execution_times) if execution_times else None
            },
            "file_count_stats": {
                "min": min(file_counts) if file_counts else None,
                "max": max(file_counts) if file_counts else None,
                "avg": sum(file_counts) / len(file_counts) if file_counts else None
            },
            "registry_count_stats": {
                "min": min(registry_counts) if registry_counts else None,
                "max": max(registry_counts) if registry_counts else None,
                "avg": sum(registry_counts) / len(registry_counts) if registry_counts else None
            },
            "latest_run": runs[0].to_dict() if runs else None,
            "first_run": runs[-1].to_dict() if runs else None
        }
