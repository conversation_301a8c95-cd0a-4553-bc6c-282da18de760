"""Workflow orchestration endpoints."""

import logging
from typing import Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ...models.file_upload import FileUpload, FileStatus
from ...models.workflow_job import WorkflowJob, WorkflowStatus
from ...models.vm_instance import VMInstance
from ...services.database import get_db
from ...services.celery_app import get_celery_app

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/start")
async def start_workflow(
    file_id: UUID = Form(...),
    vm_template: str = Form("ubuntu/focal64"),
    vm_memory_mb: int = Form(1024),
    vm_cpus: int = Form(1),
    injection_path: str = Form("/tmp/injected_file"),
    description: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    Start the complete workflow: File → MinIO → VM → Injection → ELK → 30min → Terminate
    
    This orchestrates the entire process:
    1. Validate file exists in MinIO
    2. Create workflow job
    3. Queue VM creation task
    4. Queue file injection task
    5. Queue monitoring task (30min runtime)
    6. Return workflow job ID for tracking
    """
    try:
        # Validate file exists
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == file_id)
        )
        file_upload = result.scalar_one_or_none()
        
        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")
        
        if file_upload.status != FileStatus.STORED:
            raise HTTPException(
                status_code=400, 
                detail=f"File not ready for processing. Status: {file_upload.status.value}"
            )
        
        # Create workflow job
        workflow_job = WorkflowJob(
            name=f"Analysis-{file_upload.filename}",
            description=description or f"Automated analysis of {file_upload.filename}",
            status=WorkflowStatus.PENDING,
            file_upload_id=file_upload.id,
            vm_config={
                "template": vm_template,
                "memory_mb": vm_memory_mb,
                "cpus": vm_cpus,
                "disk_gb": 20
            },
            injection_config={
                "target_path": injection_path,
                "permissions": "0755"
            },
            monitoring_config={
                "runtime_minutes": 30,
                "elk_index_prefix": f"turdparty-analysis-{file_upload.id}"
            }
        )
        
        db.add(workflow_job)
        await db.commit()
        await db.refresh(workflow_job)
        
        # Update file upload with workflow job ID
        file_upload.workflow_job_id = workflow_job.id
        file_upload.status = FileStatus.PROCESSING
        await db.commit()
        
        # Get Celery app and start enhanced workflow
        celery_app = get_celery_app()

        # Start the complete file processing workflow using the orchestrator
        workflow_task = celery_app.send_task(
            "services.workers.tasks.workflow_orchestrator.start_file_processing_workflow",
            args=[str(workflow_job.id), str(file_upload.id)],
            queue="workflow_ops"
        )
        workflow_job.add_celery_task(workflow_task.id, "workflow_orchestrator")
        
        # Update workflow status
        workflow_job.status = WorkflowStatus.VM_CREATING
        workflow_job.update_progress("vm_creation", 10, "VM creation task queued")
        
        await db.commit()
        
        logger.info(f"Workflow started: {workflow_job.id} for file {file_upload.id}")
        
        return {
            "workflow_job_id": str(workflow_job.id),
            "file_id": str(file_upload.id),
            "status": workflow_job.status.value,
            "progress": workflow_job.progress_percentage,
            "current_step": workflow_job.current_step,
            "vm_config": workflow_job.vm_config,
            "injection_config": workflow_job.injection_config,
            "monitoring_config": workflow_job.monitoring_config,
            "celery_tasks": workflow_job.celery_task_ids,
            "created_at": workflow_job.created_at.isoformat(),
            "message": "Workflow started successfully. VM creation in progress."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start workflow: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Workflow start failed: {str(e)}")


@router.get("/active")
async def get_active_workflows(
    db: AsyncSession = Depends(get_db)
) -> List[dict]:
    """Get all active workflow jobs for dashboard monitoring."""
    try:
        # Query for active workflows (not completed or failed)
        active_statuses = [
            WorkflowStatus.PENDING,
            WorkflowStatus.FILE_UPLOADED,
            WorkflowStatus.FILE_STORED,
            WorkflowStatus.VM_CREATING,
            WorkflowStatus.VM_RUNNING,
            WorkflowStatus.FILE_INJECTING,
            WorkflowStatus.MONITORING
        ]

        result = await db.execute(
            select(WorkflowJob)
            .where(WorkflowJob.status.in_(active_statuses))
            .order_by(WorkflowJob.created_at.desc())
        )

        workflows = result.scalars().all()

        return [
            {
                "id": str(workflow.id),
                "name": workflow.name,
                "status": workflow.status.value,
                "current_step": workflow.current_step,
                "progress_percentage": int(workflow.progress_percentage),
                "created_at": workflow.created_at.isoformat(),
                "updated_at": workflow.updated_at.isoformat(),
                "file_upload_id": str(workflow.file_upload_id) if workflow.file_upload_id else None,
                "vm_instance_id": str(workflow.vm_instance_id) if workflow.vm_instance_id else None
            }
            for workflow in workflows
        ]

    except Exception as e:
        logger.error(f"Error fetching active workflows: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch active workflows")


@router.get("/{workflow_job_id}")
async def get_workflow_status(
    workflow_job_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> dict:
    """Get detailed status of a workflow job."""
    try:
        result = await db.execute(
            select(WorkflowJob).where(WorkflowJob.id == workflow_job_id)
        )
        workflow_job = result.scalar_one_or_none()
        
        if not workflow_job:
            raise HTTPException(status_code=404, detail="Workflow job not found")
        
        # Get associated VM if exists
        vm_info = None
        if workflow_job.vm_instance_id:
            vm_result = await db.execute(
                select(VMInstance).where(VMInstance.id == workflow_job.vm_instance_id)
            )
            vm_instance = vm_result.scalar_one_or_none()
            if vm_instance:
                vm_info = {
                    "vm_id": str(vm_instance.id),
                    "name": vm_instance.name,
                    "status": vm_instance.status.value,
                    "ip_address": vm_instance.ip_address,
                    "runtime_minutes": vm_instance.runtime_minutes,
                    "injection_completed": vm_instance.injection_completed,
                    "monitoring_active": vm_instance.monitoring_active
                }
        
        return {
            "workflow_job_id": str(workflow_job.id),
            "name": workflow_job.name,
            "status": workflow_job.status.value,
            "current_step": workflow_job.current_step,
            "progress": workflow_job.progress_percentage,
            "file_upload_id": str(workflow_job.file_upload_id) if workflow_job.file_upload_id else None,
            "vm_instance": vm_info,
            "vm_config": workflow_job.vm_config,
            "injection_config": workflow_job.injection_config,
            "monitoring_config": workflow_job.monitoring_config,
            "results": workflow_job.results,
            "elk_indices": workflow_job.elk_indices,
            "celery_tasks": workflow_job.celery_task_ids,
            "error_message": workflow_job.error_message,
            "created_at": workflow_job.created_at.isoformat(),
            "updated_at": workflow_job.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow status: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve workflow status")


@router.get("/")
async def list_workflows(
    skip: int = 0,
    limit: int = 100,
    status: Optional[WorkflowStatus] = None,
    db: AsyncSession = Depends(get_db)
) -> dict:
    """List workflow jobs with optional filtering."""
    try:
        query = select(WorkflowJob)
        
        if status:
            query = query.where(WorkflowJob.status == status)
        
        query = query.offset(skip).limit(limit).order_by(WorkflowJob.created_at.desc())
        
        result = await db.execute(query)
        workflows = result.scalars().all()
        
        return {
            "workflows": [
                {
                    "workflow_job_id": str(w.id),
                    "name": w.name,
                    "status": w.status.value,
                    "progress": w.progress_percentage,
                    "current_step": w.current_step,
                    "file_upload_id": str(w.file_upload_id) if w.file_upload_id else None,
                    "vm_instance_id": str(w.vm_instance_id) if w.vm_instance_id else None,
                    "created_at": w.created_at.isoformat()
                }
                for w in workflows
            ],
            "total": len(workflows),
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Failed to list workflows: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve workflows")
