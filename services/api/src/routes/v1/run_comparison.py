"""API endpoints for managing multiple analysis runs and comparisons."""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ...services.database import get_db
from ...services.run_comparison_service import RunComparisonService
from ...models.analysis_run import AnalysisRun, RunComparison

router = APIRouter(prefix="/runs", tags=["run-comparison"])


@router.get("/history/{file_uuid}")
async def get_run_history(
    file_uuid: str,
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get run history for a file UUID."""
    
    service = RunComparisonService(db)
    runs = service.get_run_history(file_uuid, limit)
    
    return {
        "file_uuid": file_uuid,
        "total_runs": len(runs),
        "runs": [run.to_dict() for run in runs]
    }


@router.get("/statistics/{file_uuid}")
async def get_run_statistics(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get comprehensive statistics for all runs of a file UUID."""
    
    service = RunComparisonService(db)
    stats = service.get_run_statistics(file_uuid)
    
    if "error" in stats:
        raise HTTPException(status_code=404, detail=stats["error"])
    
    return {
        "file_uuid": file_uuid,
        "statistics": stats
    }


@router.post("/compare")
async def compare_runs(
    run_a_id: str,
    run_b_id: str,
    db: Session = Depends(get_db)
):
    """Create detailed comparison between two runs."""
    
    try:
        service = RunComparisonService(db)
        comparison = service.compare_runs(run_a_id, run_b_id)
        
        return {
            "comparison_id": str(comparison.id),
            "file_uuid": comparison.file_uuid,
            "run_a_id": str(comparison.run_a_id),
            "run_b_id": str(comparison.run_b_id),
            "total_changes": comparison.total_changes,
            "severity_score": comparison.severity_score,
            "change_categories": comparison.change_categories,
            "files_diff": comparison.files_diff,
            "registry_diff": comparison.registry_diff,
            "process_diff": comparison.process_diff,
            "performance_diff": comparison.performance_diff,
            "created_at": comparison.created_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")


@router.get("/comparison/{comparison_id}")
async def get_comparison(
    comparison_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed comparison by ID."""
    
    comparison = db.query(RunComparison).filter(
        RunComparison.id == comparison_id
    ).first()
    
    if not comparison:
        raise HTTPException(status_code=404, detail="Comparison not found")
    
    return {
        "comparison_id": str(comparison.id),
        "file_uuid": comparison.file_uuid,
        "run_a": comparison.run_a.to_dict(),
        "run_b": comparison.run_b.to_dict(),
        "total_changes": comparison.total_changes,
        "severity_score": comparison.severity_score,
        "change_categories": comparison.change_categories,
        "files_diff": comparison.files_diff,
        "registry_diff": comparison.registry_diff,
        "process_diff": comparison.process_diff,
        "performance_diff": comparison.performance_diff,
        "created_at": comparison.created_at.isoformat()
    }


@router.get("/anomalous")
async def get_anomalous_runs(
    threshold: int = Query(50, ge=0, le=100),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get runs with high anomaly scores."""
    
    service = RunComparisonService(db)
    anomalous_runs = service.get_anomalous_runs(threshold)[:limit]
    
    return {
        "threshold": threshold,
        "total_anomalous": len(anomalous_runs),
        "runs": [run.to_dict() for run in anomalous_runs]
    }


@router.get("/latest/{file_uuid}")
async def get_latest_run(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get the latest run for a file UUID."""
    
    latest_run = AnalysisRun.get_latest_run(db, file_uuid)
    
    if not latest_run:
        raise HTTPException(status_code=404, detail="No runs found for this UUID")
    
    return {
        "file_uuid": file_uuid,
        "latest_run": latest_run.to_dict()
    }


@router.get("/diff-summary/{file_uuid}")
async def get_diff_summary(
    file_uuid: str,
    runs_limit: int = Query(5, ge=2, le=20),
    db: Session = Depends(get_db)
):
    """Get a summary of differences across recent runs."""
    
    service = RunComparisonService(db)
    runs = service.get_run_history(file_uuid, runs_limit)
    
    if len(runs) < 2:
        return {
            "file_uuid": file_uuid,
            "message": "Need at least 2 runs for comparison",
            "total_runs": len(runs)
        }
    
    # Generate summary of changes across runs
    changes_summary = []
    
    for i in range(len(runs) - 1):
        current_run = runs[i]
        previous_run = runs[i + 1]
        
        if current_run.differs_from_previous:
            changes_summary.append({
                "from_run": previous_run.run_number,
                "to_run": current_run.run_number,
                "changes_count": current_run.diff_summary.get('changes_count', 0),
                "anomaly_score": current_run.anomaly_score,
                "date": current_run.completed_at.isoformat() if current_run.completed_at else None,
                "key_changes": [c.get('metric') for c in current_run.diff_summary.get('changes', [])[:3]]
            })
    
    return {
        "file_uuid": file_uuid,
        "total_runs_analyzed": len(runs),
        "runs_with_changes": len(changes_summary),
        "changes_summary": changes_summary,
        "overall_stability": "stable" if len(changes_summary) == 0 else 
                           "moderate" if len(changes_summary) <= len(runs) * 0.3 else "unstable"
    }


@router.post("/mark-baseline/{run_id}")
async def mark_as_baseline(
    run_id: str,
    db: Session = Depends(get_db)
):
    """Mark a run as baseline for future comparisons."""
    
    run = db.query(AnalysisRun).filter(AnalysisRun.id == run_id).first()
    
    if not run:
        raise HTTPException(status_code=404, detail="Run not found")
    
    # Clear existing baselines for this file_uuid
    db.query(AnalysisRun).filter(
        AnalysisRun.file_uuid == run.file_uuid
    ).update({"is_baseline": False})
    
    # Mark this run as baseline
    run.is_baseline = True
    db.commit()
    
    return {
        "message": f"Run #{run.run_number} marked as baseline",
        "run_id": str(run.id),
        "file_uuid": run.file_uuid
    }


@router.get("/trending-changes")
async def get_trending_changes(
    days: int = Query(7, ge=1, le=30),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """Get trending changes across all files in recent days."""
    
    from datetime import datetime, timedelta
    
    since_date = datetime.utcnow() - timedelta(days=days)
    
    # Get runs with changes in the specified period
    runs_with_changes = db.query(AnalysisRun).filter(
        AnalysisRun.differs_from_previous == True,
        AnalysisRun.completed_at >= since_date
    ).order_by(AnalysisRun.anomaly_score.desc()).limit(limit).all()
    
    trending_data = []
    
    for run in runs_with_changes:
        trending_data.append({
            "file_uuid": run.file_uuid,
            "binary_filename": run.binary_filename,
            "run_number": run.run_number,
            "anomaly_score": run.anomaly_score,
            "changes_count": run.diff_summary.get('changes_count', 0) if run.diff_summary else 0,
            "completed_at": run.completed_at.isoformat() if run.completed_at else None,
            "key_changes": [c.get('metric') for c in run.diff_summary.get('changes', [])[:3]] if run.diff_summary else []
        })
    
    return {
        "period_days": days,
        "total_changes_detected": len(trending_data),
        "trending_changes": trending_data
    }
