"""API endpoints for enhanced analysis with installer/runtime distinction and artifact access."""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ...services.database import get_db
from ...models.workflow_job import WorkflowJob
from ...models.file_upload import FileUpload

router = APIRouter(prefix="/enhanced-analysis", tags=["enhanced-analysis"])


@router.get("/installation-verification/{file_uuid}")
async def get_installation_verification(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get installation wizard completion verification for a file UUID."""
    
    # Get latest workflow for this file
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow:
        raise HTTPException(status_code=404, detail="No workflow found for this UUID")
    
    if not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results available")
    
    # Extract installation verification data
    phased_analysis = workflow.results.get("phased_analysis", {})
    installer_footprint = phased_analysis.get("installer_footprint", {})
    installer_phases = installer_footprint.get("phases", {})
    installer_execution = installer_phases.get("installer_execution", {})
    installation_result = installer_execution.get("data", {}).get("installation_result", {})
    
    verification_data = {
        "file_uuid": file_uuid,
        "installation_verified": workflow.results.get("installation_verified", False),
        "installer_exit_code": installation_result.get("installer_exit_code"),
        "installation_successful": installation_result.get("installation_successful", False),
        "installation_duration_seconds": installation_result.get("installation_duration_seconds"),
        "wizard_completion_verified": installation_result.get("wizard_completion_verified", False),
        "verification_checks": installation_result.get("installation_phases", {}).get("verification", {}).get("verification_checks", []),
        "installation_artifacts": installation_result.get("installation_artifacts", []),
        "installer_footprint": installation_result.get("installation_footprint", {}),
        "analysis_timestamp": workflow.created_at.isoformat() if workflow.created_at else None
    }
    
    return verification_data


@router.get("/phase-breakdown/{file_uuid}")
async def get_phase_breakdown(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get detailed breakdown of installer vs runtime phases."""
    
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow or not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results found")
    
    phased_analysis = workflow.results.get("phased_analysis", {})
    
    return {
        "file_uuid": file_uuid,
        "total_analysis_duration_seconds": phased_analysis.get("total_analysis_duration_seconds"),
        "phase_breakdown": phased_analysis.get("phase_breakdown", {}),
        "installer_footprint": {
            "phases": phased_analysis.get("installer_footprint", {}).get("phases", {}),
            "elk_index": phased_analysis.get("installer_footprint", {}).get("elk_index"),
            "footprint_type": "INSTALLATION",
            "duration_seconds": phased_analysis.get("phase_breakdown", {}).get("installer_duration_seconds")
        },
        "runtime_footprint": {
            "phases": phased_analysis.get("runtime_footprint", {}).get("phases", {}),
            "elk_index": phased_analysis.get("runtime_footprint", {}).get("elk_index"),
            "footprint_type": "RUNTIME",
            "duration_seconds": phased_analysis.get("phase_breakdown", {}).get("runtime_duration_seconds")
        },
        "phase_transitions": phased_analysis.get("phase_transitions", [])
    }


@router.get("/artifacts/{file_uuid}")
async def get_artifacts_info(
    file_uuid: str,
    phase: Optional[str] = Query(None, description="Filter by phase: installer, runtime, system"),
    db: Session = Depends(get_db)
):
    """Get information about collected artifacts for a file UUID."""
    
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow or not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results found")
    
    artifact_collection = workflow.results.get("artifact_collection", {})
    
    if not artifact_collection.get("success"):
        raise HTTPException(status_code=404, detail="Artifact collection failed or not available")
    
    # Filter artifacts by phase if specified
    upload_results = artifact_collection.get("upload_results", [])
    
    if phase:
        upload_results = [
            result for result in upload_results 
            if phase in result.get("object_key", "")
        ]
    
    return {
        "file_uuid": file_uuid,
        "artifacts_collected": artifact_collection.get("artifacts_collected", 0),
        "artifacts_uploaded": artifact_collection.get("success", False),
        "installer_artifacts": artifact_collection.get("installer_artifacts", 0),
        "runtime_artifacts": artifact_collection.get("runtime_artifacts", 0),
        "system_artifacts": artifact_collection.get("system_artifacts", 0),
        "upload_results": upload_results,
        "minio_bucket": "turdparty-artifacts",
        "artifact_base_path": f"{file_uuid}/artifacts/",
        "collection_metadata": artifact_collection.get("metadata", {})
    }


@router.get("/artifacts/{file_uuid}/download-urls")
async def get_artifact_download_urls(
    file_uuid: str,
    phase: Optional[str] = Query(None, description="Filter by phase: installer, runtime, system"),
    db: Session = Depends(get_db)
):
    """Get MinIO download URLs for artifacts."""
    
    # Get artifact info first
    artifacts_info = await get_artifacts_info(file_uuid, phase, db)
    
    # Generate MinIO presigned URLs
    from ...services.minio_service import generate_presigned_url
    
    download_urls = []
    
    for upload_result in artifacts_info["upload_results"]:
        if upload_result.get("upload_success"):
            try:
                presigned_url = generate_presigned_url(
                    bucket_name=upload_result["bucket"],
                    object_key=upload_result["object_key"],
                    expiry_hours=24
                )
                
                download_urls.append({
                    "artifact_filename": upload_result["filename"],
                    "artifact_type": upload_result.get("artifact_type"),
                    "file_size": upload_result.get("file_size"),
                    "download_url": presigned_url,
                    "expires_in_hours": 24
                })
                
            except Exception as e:
                download_urls.append({
                    "artifact_filename": upload_result["filename"],
                    "error": f"Failed to generate download URL: {str(e)}"
                })
    
    return {
        "file_uuid": file_uuid,
        "download_urls": download_urls,
        "total_artifacts": len(download_urls),
        "expires_in_hours": 24
    }


@router.get("/footprint-comparison/{file_uuid}")
async def get_footprint_comparison(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Compare installer footprint vs runtime footprint."""
    
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow or not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results found")
    
    installer_footprint = workflow.results.get("installer_footprint", {})
    runtime_footprint = workflow.results.get("runtime_footprint", {})
    
    # Extract key metrics for comparison
    installer_metrics = {
        "files_added": 0,
        "registry_keys_added": 0,
        "processes_spawned": 0,
        "duration_seconds": 0
    }
    
    runtime_metrics = {
        "files_modified": 0,
        "registry_keys_modified": 0,
        "processes_spawned": 0,
        "duration_seconds": 0
    }
    
    # Extract actual metrics from footprint data
    if installer_footprint:
        installer_phases = installer_footprint.get("phases", {})
        if "installer_execution" in installer_phases:
            install_data = installer_phases["installer_execution"].get("data", {})
            installer_metrics.update(install_data.get("installation_footprint", {}))
    
    if runtime_footprint:
        runtime_phases = runtime_footprint.get("phases", {})
        if "runtime_execution" in runtime_phases:
            runtime_data = runtime_phases["runtime_execution"].get("data", {})
            runtime_metrics.update(runtime_data.get("runtime_footprint", {}))
    
    return {
        "file_uuid": file_uuid,
        "installer_footprint": {
            "type": "INSTALLATION",
            "metrics": installer_metrics,
            "elk_index": installer_footprint.get("elk_index"),
            "description": "Changes made during software installation"
        },
        "runtime_footprint": {
            "type": "RUNTIME",
            "metrics": runtime_metrics,
            "elk_index": runtime_footprint.get("elk_index"),
            "description": "Changes made during software runtime execution"
        },
        "comparison": {
            "installer_impact": sum(installer_metrics.values()) if all(isinstance(v, (int, float)) for v in installer_metrics.values()) else 0,
            "runtime_impact": sum(runtime_metrics.values()) if all(isinstance(v, (int, float)) for v in runtime_metrics.values()) else 0,
            "primary_impact_phase": "installer" if installer_metrics.get("files_added", 0) > runtime_metrics.get("files_modified", 0) else "runtime"
        }
    }


@router.get("/elk-indices/{file_uuid}")
async def get_elk_indices(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get ELK indices for installer and runtime data."""
    
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow or not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results found")
    
    phased_analysis = workflow.results.get("phased_analysis", {})
    
    return {
        "file_uuid": file_uuid,
        "installer_elk_index": phased_analysis.get("installer_footprint", {}).get("elk_index"),
        "runtime_elk_index": phased_analysis.get("runtime_footprint", {}).get("elk_index"),
        "elasticsearch_base_url": "http://elasticsearch.turdparty.localhost",
        "kibana_base_url": "http://kibana.turdparty.localhost",
        "suggested_queries": {
            "installer_events": f"GET /turdparty-installer-{file_uuid}/_search",
            "runtime_events": f"GET /turdparty-runtime-{file_uuid}/_search",
            "all_events": f"GET /turdparty-*-{file_uuid}/_search"
        }
    }


@router.get("/summary/{file_uuid}")
async def get_enhanced_analysis_summary(
    file_uuid: str,
    db: Session = Depends(get_db)
):
    """Get comprehensive summary of enhanced analysis."""
    
    workflow = db.query(WorkflowJob).join(FileUpload).filter(
        FileUpload.file_uuid == file_uuid
    ).order_by(WorkflowJob.created_at.desc()).first()
    
    if not workflow or not workflow.results:
        raise HTTPException(status_code=404, detail="No analysis results found")
    
    results = workflow.results
    file_upload = workflow.file_upload
    
    return {
        "file_uuid": file_uuid,
        "binary_filename": file_upload.filename if file_upload else "unknown",
        "analysis_timestamp": workflow.created_at.isoformat() if workflow.created_at else None,
        "enhanced_monitoring": results.get("enhanced_monitoring", False),
        "installation_verification": {
            "verified": results.get("installation_verified", False),
            "wizard_completed": results.get("phased_analysis", {}).get("installer_footprint", {}).get("phases", {}).get("installer_execution", {}).get("data", {}).get("installation_result", {}).get("wizard_completion_verified", False)
        },
        "phase_separation": {
            "installer_duration": results.get("phased_analysis", {}).get("phase_breakdown", {}).get("installer_duration_seconds"),
            "runtime_duration": results.get("phased_analysis", {}).get("phase_breakdown", {}).get("runtime_duration_seconds"),
            "total_duration": results.get("phased_analysis", {}).get("total_analysis_duration_seconds")
        },
        "artifact_collection": {
            "success": results.get("artifacts_uploaded", False),
            "total_artifacts": results.get("total_artifacts", 0),
            "minio_bucket": "turdparty-artifacts",
            "artifact_path": f"{file_uuid}/artifacts/"
        },
        "elk_monitoring": {
            "installer_index": results.get("phased_analysis", {}).get("installer_footprint", {}).get("elk_index"),
            "runtime_index": results.get("phased_analysis", {}).get("runtime_footprint", {}).get("elk_index")
        },
        "workflow_status": workflow.status.value if workflow.status else "unknown"
    }
