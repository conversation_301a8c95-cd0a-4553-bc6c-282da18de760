"""
Report Generation API Endpoints
Handles Sphinx report generation via API.
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from services.report_generator import SphinxReportGenerator
from ...services.database import get_db
from ...models.workflow_job import WorkflowJob
from ...models.file_upload import FileUpload

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()


class ReportGenerationRequest(BaseModel):
    """Request model for report generation."""
    file_uuid: str
    workflow_id: Optional[str] = None
    format: str = "sphinx"  # sphinx, json, html
    include_ecs_data: bool = True


class ReportGenerationResponse(BaseModel):
    """Response model for report generation."""
    success: bool
    file_uuid: str
    report_url: Optional[str] = None
    report_file: Optional[str] = None
    generation_time: Optional[float] = None
    error: Optional[str] = None


class ReportStatus(BaseModel):
    """Report generation status."""
    file_uuid: str
    status: str  # pending, generating, completed, failed
    progress: int  # 0-100
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


# In-memory status tracking (in production, use Redis or database)
report_status_cache: Dict[str, ReportStatus] = {}


@router.post("/generate", response_model=ReportGenerationResponse)
async def generate_report(
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Generate a Sphinx report for a file UUID.
    
    This endpoint triggers report generation and returns immediately.
    Use the status endpoint to check progress.
    """
    try:
        file_uuid = request.file_uuid
        
        # Validate that the file UUID exists
        from sqlalchemy import select
        result = await db.execute(
            select(FileUpload).where(FileUpload.id == file_uuid)
        )
        file_upload = result.scalar_one_or_none()

        if not file_upload:
            raise HTTPException(
                status_code=404,
                detail=f"File UUID not found: {file_uuid}"
            )
        
        # Check if report generation is already in progress
        if file_uuid in report_status_cache:
            status = report_status_cache[file_uuid]
            if status.status in ["pending", "generating"]:
                return ReportGenerationResponse(
                    success=False,
                    file_uuid=file_uuid,
                    error="Report generation already in progress"
                )
        
        # Initialize status
        report_status_cache[file_uuid] = ReportStatus(
            file_uuid=file_uuid,
            status="pending",
            progress=0,
            message="Report generation queued",
            started_at=datetime.utcnow()
        )
        
        # Queue background task
        background_tasks.add_task(
            _generate_report_background,
            file_uuid,
            request.format,
            request.include_ecs_data
        )
        
        return ReportGenerationResponse(
            success=True,
            file_uuid=file_uuid,
            report_url=f"/api/v1/reports/status/{file_uuid}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Report generation request failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Report generation failed: {str(e)}"
        )


@router.get("/status/{file_uuid}", response_model=ReportStatus)
async def get_report_status(file_uuid: str):
    """Get the status of report generation for a file UUID."""
    
    if file_uuid not in report_status_cache:
        raise HTTPException(
            status_code=404,
            detail="No report generation found for this UUID"
        )
    
    return report_status_cache[file_uuid]


@router.get("/download/{file_uuid}")
async def download_report(
    file_uuid: str,
    format: str = Query("html", description="Report format: html, rst, json")
):
    """Download the generated report for a file UUID."""
    
    # Check if report exists
    if file_uuid not in report_status_cache:
        raise HTTPException(
            status_code=404,
            detail="No report found for this UUID"
        )
    
    status = report_status_cache[file_uuid]
    if status.status != "completed":
        raise HTTPException(
            status_code=404,
            detail=f"Report not ready. Status: {status.status}"
        )
    
    # TODO: Implement actual file serving
    # For now, return the report URL
    return {
        "file_uuid": file_uuid,
        "format": format,
        "download_url": f"http://reports.turdparty.localhost/{file_uuid}.{format}",
        "message": "Report download URL (implementation pending)"
    }


@router.get("/list")
async def list_reports(
    limit: int = Query(50, le=1000),
    offset: int = Query(0, ge=0),
    status: Optional[str] = Query(None, description="Filter by status")
):
    """List all generated reports."""
    
    reports = list(report_status_cache.values())
    
    # Filter by status if provided
    if status:
        reports = [r for r in reports if r.status == status]
    
    # Apply pagination
    total = len(reports)
    reports = reports[offset:offset + limit]
    
    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "reports": reports
    }


@router.delete("/clear/{file_uuid}")
async def clear_report_status(file_uuid: str):
    """Clear the report status for a file UUID."""
    
    if file_uuid in report_status_cache:
        del report_status_cache[file_uuid]
        return {"message": f"Report status cleared for {file_uuid}"}
    else:
        raise HTTPException(
            status_code=404,
            detail="No report status found for this UUID"
        )


async def _generate_report_background(
    file_uuid: str,
    format: str,
    include_ecs_data: bool
):
    """Background task for report generation."""
    try:
        logger.info(f"Starting background report generation for {file_uuid}")
        
        # Update status
        report_status_cache[file_uuid].status = "generating"
        report_status_cache[file_uuid].progress = 10
        report_status_cache[file_uuid].message = "Initializing report generator"
        
        # Initialize report generator
        generator = SphinxReportGenerator()
        
        try:
            # Update progress
            report_status_cache[file_uuid].progress = 30
            report_status_cache[file_uuid].message = "Fetching analysis data"
            
            # Generate the report
            result = await generator.generate_report_from_uuid(file_uuid)
            
            if result.get("success"):
                # Update status - success
                report_status_cache[file_uuid].status = "completed"
                report_status_cache[file_uuid].progress = 100
                report_status_cache[file_uuid].message = "Report generated successfully"
                report_status_cache[file_uuid].completed_at = datetime.utcnow()
                
                logger.info(f"Report generated successfully for {file_uuid}")
                
            else:
                # Update status - failed
                report_status_cache[file_uuid].status = "failed"
                report_status_cache[file_uuid].progress = 0
                report_status_cache[file_uuid].message = f"Report generation failed: {result.get('error', 'Unknown error')}"
                report_status_cache[file_uuid].completed_at = datetime.utcnow()
                
                logger.error(f"Report generation failed for {file_uuid}: {result.get('error')}")
        
        finally:
            await generator.cleanup()
            
    except Exception as e:
        logger.error(f"Background report generation failed for {file_uuid}: {e}")
        
        # Update status - error
        if file_uuid in report_status_cache:
            report_status_cache[file_uuid].status = "failed"
            report_status_cache[file_uuid].progress = 0
            report_status_cache[file_uuid].message = f"Generation error: {str(e)}"
            report_status_cache[file_uuid].completed_at = datetime.utcnow()


@router.get("/health")
async def reports_health():
    """Health check for the reports service."""
    return {
        "status": "healthy",
        "service": "reports",
        "timestamp": datetime.utcnow().isoformat(),
        "active_generations": len([
            r for r in report_status_cache.values() 
            if r.status in ["pending", "generating"]
        ]),
        "total_reports": len(report_status_cache)
    }
