"""
Celery Health Endpoints for Status Dashboard Integration
Provides health and metrics endpoints for Celery workers and tasks
"""

import logging
from datetime import datetime
from typing import Dict, Any, List

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["health"])


class CeleryWorkerStatus(BaseModel):
    """
    Celery worker status response model.
    
    Attributes:
        status: Overall worker status (operational/degraded/outage)
        active_workers: Number of active workers
        total_workers: Total number of registered workers
        active_tasks: Number of currently executing tasks
        queues: Queue status information
        last_check: Timestamp of last health check
    """
    status: str
    active_workers: int
    total_workers: int
    active_tasks: int
    queues: Dict[str, Any]
    last_check: str


class TaskMetrics(BaseModel):
    """
    Task execution metrics response model.
    
    Attributes:
        status: Overall task health status
        success_rate: Task success rate percentage (24h)
        total_executions: Total task executions (24h)
        successful_executions: Successful task executions (24h)
        failed_executions: Failed task executions (24h)
        average_duration: Average task execution time
        last_updated: Timestamp of last metrics update
    """
    status: str
    success_rate: float
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_duration: float
    last_updated: str


@router.get("/celery", response_model=CeleryWorkerStatus)
async def get_celery_worker_status() -> CeleryWorkerStatus:
    """
    Get Celery worker health status.
    
    Returns comprehensive information about Celery worker availability,
    active tasks, and queue status for the status dashboard.
    
    Returns:
        CeleryWorkerStatus: Worker status and metrics
        
    Raises:
        HTTPException: If unable to connect to Celery
    """
    try:
        logger.info("Checking Celery worker status")
        
        # Import Celery here to avoid startup issues
        from celery import current_app
        
        # Get worker inspection interface
        inspect = current_app.control.inspect()
        
        # Get worker statistics
        try:
            active_workers = inspect.active()
            scheduled_tasks = inspect.scheduled()
            reserved_tasks = inspect.reserved()
            stats = inspect.stats()
            
            # Calculate metrics
            worker_count = len(stats) if stats else 0
            total_active_tasks = 0
            
            if active_workers:
                total_active_tasks = sum(len(tasks) for tasks in active_workers.values())
            
            # Determine status
            if worker_count == 0:
                status = "outage"
            elif worker_count < 2:  # Assuming we want at least 2 workers
                status = "degraded"
            else:
                status = "operational"
            
            # Get queue information
            queue_info = {}
            if stats:
                for worker_name, worker_stats in stats.items():
                    if 'pool' in worker_stats:
                        queue_info[worker_name] = {
                            "processes": worker_stats['pool'].get('processes', []),
                            "max_concurrency": worker_stats['pool'].get('max-concurrency', 0),
                            "current_load": len(worker_stats['pool'].get('processes', []))
                        }
            
            result = CeleryWorkerStatus(
                status=status,
                active_workers=worker_count,
                total_workers=worker_count,  # In this context, they're the same
                active_tasks=total_active_tasks,
                queues=queue_info,
                last_check=datetime.utcnow().isoformat()
            )
            
            logger.info(
                f"Celery worker status: {status}, {worker_count} workers, {total_active_tasks} tasks",
                extra={
                    "celery_status": status,
                    "worker_count": worker_count,
                    "active_tasks": total_active_tasks
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to inspect Celery workers: {e}")
            # Return degraded status if we can't inspect
            return CeleryWorkerStatus(
                status="outage",
                active_workers=0,
                total_workers=0,
                active_tasks=0,
                queues={},
                last_check=datetime.utcnow().isoformat()
            )
            
    except Exception as e:
        logger.error(f"Celery health check failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail=f"Unable to check Celery status: {str(e)}"
        )


@router.get("/tasks", response_model=TaskMetrics)
async def get_task_metrics() -> TaskMetrics:
    """
    Get Celery task execution metrics.
    
    Returns task success rates, execution counts, and performance
    metrics for the last 24 hours.
    
    Returns:
        TaskMetrics: Task execution metrics and status
        
    Raises:
        HTTPException: If unable to retrieve task metrics
    """
    try:
        logger.info("Retrieving task execution metrics")
        
        # Import task monitor here to avoid circular imports
        try:
            from services.workers.tasks.task_monitoring import task_monitor
            
            # Get task summary from our monitoring system
            task_summary = task_monitor.get_task_summary()
            
            # Calculate average duration (placeholder - would need actual implementation)
            average_duration = 30.0  # seconds, placeholder
            
            # Determine status based on success rate
            success_rate = task_summary.get("success_rate", 0)
            if success_rate >= 95:
                status = "operational"
            elif success_rate >= 80:
                status = "degraded"
            else:
                status = "outage"
            
            result = TaskMetrics(
                status=status,
                success_rate=success_rate,
                total_executions=task_summary.get("total_executions", 0),
                successful_executions=task_summary.get("successful_executions", 0),
                failed_executions=task_summary.get("failed_executions", 0),
                average_duration=average_duration,
                last_updated=task_summary.get("last_updated", datetime.utcnow().isoformat())
            )
            
            logger.info(
                f"Task metrics: {success_rate:.1f}% success rate, {result.total_executions} total",
                extra={
                    "task_status": status,
                    "success_rate": success_rate,
                    "total_executions": result.total_executions
                }
            )
            
            return result
            
        except ImportError:
            logger.warning("Task monitoring not available, returning default metrics")
            # Return default metrics if monitoring is not available
            return TaskMetrics(
                status="operational",
                success_rate=100.0,
                total_executions=0,
                successful_executions=0,
                failed_executions=0,
                average_duration=0.0,
                last_updated=datetime.utcnow().isoformat()
            )
            
    except Exception as e:
        logger.error(f"Task metrics retrieval failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail=f"Unable to retrieve task metrics: {str(e)}"
        )


@router.get("/celery/detailed")
async def get_detailed_celery_status() -> Dict[str, Any]:
    """
    Get detailed Celery status information.
    
    Provides comprehensive information about workers, queues,
    tasks, and system health for debugging and monitoring.
    
    Returns:
        Dict[str, Any]: Detailed Celery status information
        
    Raises:
        HTTPException: If unable to retrieve detailed status
    """
    try:
        logger.info("Retrieving detailed Celery status")
        
        from celery import current_app
        
        inspect = current_app.control.inspect()
        
        # Gather comprehensive status information
        detailed_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "workers": {},
            "queues": {},
            "tasks": {},
            "system": {}
        }
        
        try:
            # Worker information
            active_workers = inspect.active()
            scheduled_tasks = inspect.scheduled()
            reserved_tasks = inspect.reserved()
            stats = inspect.stats()
            registered_tasks = inspect.registered()
            
            detailed_status["workers"] = {
                "active": active_workers or {},
                "stats": stats or {},
                "count": len(stats) if stats else 0
            }
            
            detailed_status["tasks"] = {
                "active": active_workers or {},
                "scheduled": scheduled_tasks or {},
                "reserved": reserved_tasks or {},
                "registered": registered_tasks or {}
            }
            
            # Queue information
            if stats:
                queue_details = {}
                for worker_name, worker_stats in stats.items():
                    if 'pool' in worker_stats:
                        queue_details[worker_name] = worker_stats['pool']
                detailed_status["queues"] = queue_details
            
            # System health summary
            worker_count = len(stats) if stats else 0
            total_active_tasks = sum(len(tasks) for tasks in (active_workers or {}).values())
            
            detailed_status["system"] = {
                "healthy": worker_count > 0,
                "worker_count": worker_count,
                "active_task_count": total_active_tasks,
                "status": "operational" if worker_count > 0 else "outage"
            }
            
        except Exception as e:
            logger.error(f"Error gathering detailed status: {e}")
            detailed_status["error"] = str(e)
            detailed_status["system"]["healthy"] = False
            detailed_status["system"]["status"] = "error"
        
        return detailed_status
        
    except Exception as e:
        logger.error(f"Detailed Celery status failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail=f"Unable to retrieve detailed Celery status: {str(e)}"
        )


@router.get("/celery/tasks")
async def get_celery_tasks() -> Dict[str, Any]:
    """
    Get active and reserved Celery tasks for dashboard monitoring.

    Returns detailed information about currently running and queued tasks
    for the binary processing dashboard.

    Returns:
        Dict containing active and reserved tasks with details

    Raises:
        HTTPException: If unable to connect to Celery
    """
    try:
        logger.info("Fetching Celery task information for dashboard")

        # Import Celery here to avoid startup issues
        from celery import current_app

        # Get worker inspection interface
        inspect = current_app.control.inspect()

        if not inspect:
            raise HTTPException(
                status_code=503,
                detail="Unable to connect to Celery workers"
            )

        # Get active and reserved tasks
        active_tasks = inspect.active() or {}
        reserved_tasks = inspect.reserved() or {}

        # Process active tasks
        processed_active = []
        for worker, tasks in active_tasks.items():
            for task in tasks:
                processed_active.append({
                    "id": task.get("id"),
                    "name": task.get("name"),
                    "args": task.get("args", []),
                    "kwargs": task.get("kwargs", {}),
                    "worker": worker,
                    "time_start": task.get("time_start"),
                    "acknowledged": task.get("acknowledged", False)
                })

        # Process reserved tasks
        processed_reserved = []
        for worker, tasks in reserved_tasks.items():
            for task in tasks:
                processed_reserved.append({
                    "id": task.get("id"),
                    "name": task.get("name"),
                    "args": task.get("args", []),
                    "kwargs": task.get("kwargs", {}),
                    "worker": worker,
                    "eta": task.get("eta"),
                    "priority": task.get("priority", 0)
                })

        return {
            "active": processed_active,
            "reserved": processed_reserved,
            "active_count": len(processed_active),
            "reserved_count": len(processed_reserved),
            "total_count": len(processed_active) + len(processed_reserved),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get Celery tasks: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Unable to retrieve Celery task information: {str(e)}"
        )
