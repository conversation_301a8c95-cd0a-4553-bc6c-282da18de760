"""Analysis run tracking model for managing multiple executions of the same binary."""

from sqlalchemy import Column, String, DateTime, Integer, JSON, Text, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from .base import Base


class AnalysisRun(Base):
    """Track multiple analysis runs for the same binary/UUID."""
    
    __tablename__ = "analysis_runs"
    
    # Primary identifiers
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_uuid = Column(String(36), nullable=False, index=True)  # Links multiple runs
    run_number = Column(Integer, nullable=False)  # Sequential run number for this UUID
    
    # Run metadata
    started_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(20), default="running")  # running, completed, failed
    
    # Analysis details
    binary_filename = Column(String(255), nullable=False)
    binary_hash_sha256 = Column(String(64), nullable=True)
    binary_size_bytes = Column(Integer, nullable=True)
    
    # Runtime metrics
    execution_duration_seconds = Column(Integer, nullable=True)
    installer_runtime_seconds = Column(Integer, nullable=True)
    vm_template = Column(String(100), nullable=True)
    
    # Results and data
    ecs_events_count = Column(Integer, default=0)
    files_created_count = Column(Integer, default=0)
    registry_keys_count = Column(Integer, default=0)
    processes_spawned_count = Column(Integer, default=0)
    
    # Detailed results (JSON)
    analysis_results = Column(JSON, nullable=True)
    performance_metrics = Column(JSON, nullable=True)
    security_indicators = Column(JSON, nullable=True)
    
    # Comparison metadata
    differs_from_previous = Column(Boolean, default=False)
    diff_summary = Column(JSON, nullable=True)  # Summary of differences
    anomaly_score = Column(Integer, default=0)  # 0-100 anomaly rating
    
    # Relationships
    workflow_job_id = Column(UUID(as_uuid=True), ForeignKey('workflow_jobs.id'), nullable=True)
    workflow_job = relationship("WorkflowJob", back_populates="analysis_runs")
    
    def __repr__(self):
        return f"<AnalysisRun {self.file_uuid} run #{self.run_number}>"
    
    @classmethod
    def get_latest_run(cls, session, file_uuid: str):
        """Get the most recent run for a given file UUID."""
        return session.query(cls).filter(
            cls.file_uuid == file_uuid
        ).order_by(cls.run_number.desc()).first()
    
    @classmethod
    def get_run_history(cls, session, file_uuid: str, limit: int = 10):
        """Get run history for a file UUID."""
        return session.query(cls).filter(
            cls.file_uuid == file_uuid
        ).order_by(cls.run_number.desc()).limit(limit).all()
    
    @classmethod
    def get_next_run_number(cls, session, file_uuid: str) -> int:
        """Get the next run number for a file UUID."""
        latest = cls.get_latest_run(session, file_uuid)
        return (latest.run_number + 1) if latest else 1
    
    def calculate_diff_from_previous(self, session):
        """Calculate differences from the previous run."""
        previous_run = session.query(AnalysisRun).filter(
            AnalysisRun.file_uuid == self.file_uuid,
            AnalysisRun.run_number == self.run_number - 1
        ).first()
        
        if not previous_run:
            self.differs_from_previous = False
            self.diff_summary = {"type": "first_run", "changes": []}
            return
        
        changes = []
        
        # Compare key metrics
        if self.files_created_count != previous_run.files_created_count:
            changes.append({
                "metric": "files_created",
                "previous": previous_run.files_created_count,
                "current": self.files_created_count,
                "change": self.files_created_count - previous_run.files_created_count
            })
        
        if self.registry_keys_count != previous_run.registry_keys_count:
            changes.append({
                "metric": "registry_keys",
                "previous": previous_run.registry_keys_count,
                "current": self.registry_keys_count,
                "change": self.registry_keys_count - previous_run.registry_keys_count
            })
        
        if self.processes_spawned_count != previous_run.processes_spawned_count:
            changes.append({
                "metric": "processes_spawned",
                "previous": previous_run.processes_spawned_count,
                "current": self.processes_spawned_count,
                "change": self.processes_spawned_count - previous_run.processes_spawned_count
            })
        
        # Compare runtime performance (allow 10% variance)
        if (previous_run.execution_duration_seconds and 
            abs(self.execution_duration_seconds - previous_run.execution_duration_seconds) > 
            previous_run.execution_duration_seconds * 0.1):
            changes.append({
                "metric": "execution_duration",
                "previous": previous_run.execution_duration_seconds,
                "current": self.execution_duration_seconds,
                "change_percent": ((self.execution_duration_seconds - previous_run.execution_duration_seconds) / 
                                 previous_run.execution_duration_seconds) * 100
            })
        
        # Calculate anomaly score based on number and severity of changes
        anomaly_score = min(len(changes) * 15, 100)  # 15 points per change, max 100
        
        self.differs_from_previous = len(changes) > 0
        self.diff_summary = {
            "type": "comparison",
            "changes_count": len(changes),
            "changes": changes,
            "previous_run": previous_run.run_number,
            "comparison_timestamp": datetime.utcnow().isoformat()
        }
        self.anomaly_score = anomaly_score
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": str(self.id),
            "file_uuid": self.file_uuid,
            "run_number": self.run_number,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "status": self.status,
            "binary_filename": self.binary_filename,
            "binary_hash_sha256": self.binary_hash_sha256,
            "binary_size_bytes": self.binary_size_bytes,
            "execution_duration_seconds": self.execution_duration_seconds,
            "installer_runtime_seconds": self.installer_runtime_seconds,
            "vm_template": self.vm_template,
            "ecs_events_count": self.ecs_events_count,
            "files_created_count": self.files_created_count,
            "registry_keys_count": self.registry_keys_count,
            "processes_spawned_count": self.processes_spawned_count,
            "analysis_results": self.analysis_results,
            "performance_metrics": self.performance_metrics,
            "security_indicators": self.security_indicators,
            "differs_from_previous": self.differs_from_previous,
            "diff_summary": self.diff_summary,
            "anomaly_score": self.anomaly_score
        }


class RunComparison(Base):
    """Store detailed comparisons between analysis runs."""
    
    __tablename__ = "run_comparisons"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_uuid = Column(String(36), nullable=False, index=True)
    
    # Runs being compared
    run_a_id = Column(UUID(as_uuid=True), ForeignKey('analysis_runs.id'), nullable=False)
    run_b_id = Column(UUID(as_uuid=True), ForeignKey('analysis_runs.id'), nullable=False)
    
    # Comparison metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    comparison_type = Column(String(20), default="sequential")  # sequential, manual, automated
    
    # Detailed diff data
    files_diff = Column(JSON, nullable=True)  # Added/removed/modified files
    registry_diff = Column(JSON, nullable=True)  # Registry changes
    process_diff = Column(JSON, nullable=True)  # Process differences
    performance_diff = Column(JSON, nullable=True)  # Performance metrics diff
    
    # Summary
    total_changes = Column(Integer, default=0)
    severity_score = Column(Integer, default=0)  # 0-100
    change_categories = Column(JSON, nullable=True)  # ["files", "registry", "performance"]
    
    # Relationships
    run_a = relationship("AnalysisRun", foreign_keys=[run_a_id])
    run_b = relationship("AnalysisRun", foreign_keys=[run_b_id])
    
    def __repr__(self):
        return f"<RunComparison {self.file_uuid} runs {self.run_a_id} vs {self.run_b_id}>"
