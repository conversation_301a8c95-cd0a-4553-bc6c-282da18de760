"""Celery application for TurdParty workers."""

import os
from celery import Celery
from celery.schedules import crontab

# Configuration
REDIS_HOST = os.getenv("REDIS_HOST", "cache")  # Use 'cache' to match docker-compose service name
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

BROKER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB + 1}"

# Create Celery app
app = Celery(
    "turdparty_workers",
    broker=BROKER_URL,
    backend=RESULT_BACKEND,
    include=[
        "tasks.simple_file_ops",
        "tasks.simple_vm_ops",
        "tasks.simple_elk_ops",
        "tasks.simple_workflow",
        "tasks.report_generation"
    ]
)

# Configuration
app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    task_routes={
        "tasks.file_operations.*": {"queue": "file_ops"},
        "tasks.vm_management.*": {"queue": "vm_ops"},
        "tasks.injection_tasks.*": {"queue": "injection_ops"},
        "tasks.vm_pool_manager.*": {"queue": "pool_ops"},
        "tasks.workflow_orchestrator.*": {"queue": "workflow_ops"},
        "tasks.elk_integration.*": {"queue": "elk_ops"},
        "tasks.vm_agent_injector.*": {"queue": "injection_ops"},
        "tasks.report_generation.*": {"queue": "reports"},
        "tasks.scheduled_maintenance.*": {"queue": "maintenance"},
        "tasks.task_monitoring.*": {"queue": "monitoring"},
    },
    task_default_queue="default",
    task_default_exchange="default",
    task_default_routing_key="default",
    beat_schedule={
        # Simple health check - Every 10 minutes
        'simple-health-check': {
            'task': 'tasks.simple_elk_ops.simple_health_check',
            'schedule': 600.0,  # 10 minutes
            'options': {'queue': 'default'}
        },
    }
)

if __name__ == "__main__":
    app.start()
