"""Artifact collection and MinIO upload for created files and installation footprint."""

import logging
import os
import zipfile
import hashlib
import json
import subprocess
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


class ArtifactCollector:
    """Collect and upload all created files and artifacts to MinIO."""
    
    def __init__(self, vm_id: str, file_uuid: str, binary_name: str):
        self.vm_id = vm_id
        self.file_uuid = file_uuid
        self.binary_name = binary_name
        self.collection_timestamp = datetime.utcnow()
        
    def collect_all_artifacts(self, installer_footprint: Dict, runtime_footprint: Dict) -> Dict[str, Any]:
        """Collect all artifacts from installer and runtime phases."""
        
        logger.info(f"Starting artifact collection for {self.binary_name}")
        
        collection_result = {
            "success": False,
            "artifacts_collected": 0,
            "upload_results": [],
            "metadata": {
                "vm_id": self.vm_id,
                "file_uuid": self.file_uuid,
                "binary_name": self.binary_name,
                "collection_timestamp": self.collection_timestamp.isoformat()
            }
        }
        
        try:
            # Collect installer artifacts
            installer_artifacts = self._collect_installer_artifacts(installer_footprint)
            
            # Collect runtime artifacts
            runtime_artifacts = self._collect_runtime_artifacts(runtime_footprint)
            
            # Collect system logs and monitoring data
            system_artifacts = self._collect_system_artifacts()
            
            # Create comprehensive artifact package
            artifact_package = self._create_artifact_package(
                installer_artifacts, runtime_artifacts, system_artifacts
            )
            
            # Upload to MinIO
            upload_results = self._upload_artifacts_to_minio(artifact_package)
            
            collection_result.update({
                "success": True,
                "artifacts_collected": len(artifact_package),
                "upload_results": upload_results,
                "installer_artifacts": len(installer_artifacts),
                "runtime_artifacts": len(runtime_artifacts),
                "system_artifacts": len(system_artifacts)
            })
            
            logger.info(f"Artifact collection completed: {len(artifact_package)} artifacts")
            
        except Exception as e:
            logger.error(f"Artifact collection failed: {e}")
            collection_result["error"] = str(e)
        
        return collection_result
    
    def _collect_installer_artifacts(self, installer_footprint: Dict) -> List[Dict[str, Any]]:
        """Collect artifacts created during installation."""
        
        logger.info("Collecting installer artifacts")
        
        artifacts = []
        
        try:
            # Collect installed files
            installed_files = self._collect_installed_files(installer_footprint)
            artifacts.extend(installed_files)
            
            # Collect registry exports
            registry_artifacts = self._collect_registry_artifacts(installer_footprint)
            artifacts.extend(registry_artifacts)
            
            # Collect installation logs
            install_logs = self._collect_installation_logs()
            artifacts.extend(install_logs)
            
            # Collect shortcuts and menu items
            shortcuts = self._collect_shortcuts_and_menu_items()
            artifacts.extend(shortcuts)
            
            logger.info(f"Collected {len(artifacts)} installer artifacts")
            
        except Exception as e:
            logger.error(f"Failed to collect installer artifacts: {e}")
        
        return artifacts
    
    def _collect_runtime_artifacts(self, runtime_footprint: Dict) -> List[Dict[str, Any]]:
        """Collect artifacts created during runtime execution."""
        
        logger.info("Collecting runtime artifacts")
        
        artifacts = []
        
        try:
            # Collect runtime-created files
            runtime_files = self._collect_runtime_files(runtime_footprint)
            artifacts.extend(runtime_files)
            
            # Collect configuration files
            config_files = self._collect_configuration_files()
            artifacts.extend(config_files)
            
            # Collect user data files
            user_data = self._collect_user_data_files()
            artifacts.extend(user_data)
            
            # Collect temporary files
            temp_files = self._collect_temporary_files()
            artifacts.extend(temp_files)
            
            logger.info(f"Collected {len(artifacts)} runtime artifacts")
            
        except Exception as e:
            logger.error(f"Failed to collect runtime artifacts: {e}")
        
        return artifacts
    
    def _collect_system_artifacts(self) -> List[Dict[str, Any]]:
        """Collect system-level artifacts and monitoring data."""
        
        logger.info("Collecting system artifacts")
        
        artifacts = []
        
        try:
            # Collect event logs
            event_logs = self._collect_event_logs()
            artifacts.extend(event_logs)
            
            # Collect process dumps
            process_dumps = self._collect_process_dumps()
            artifacts.extend(process_dumps)
            
            # Collect network captures
            network_captures = self._collect_network_captures()
            artifacts.extend(network_captures)
            
            # Collect performance data
            performance_data = self._collect_performance_data()
            artifacts.extend(performance_data)
            
            logger.info(f"Collected {len(artifacts)} system artifacts")
            
        except Exception as e:
            logger.error(f"Failed to collect system artifacts: {e}")
        
        return artifacts
    
    def _collect_installed_files(self, installer_footprint: Dict) -> List[Dict[str, Any]]:
        """Collect all files created during installation."""
        
        artifacts = []
        
        # Common installation directories
        install_dirs = [
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\ProgramData",
            "C:\\Users\\<USER>\\AppData\\Local",
            "C:\\Users\\<USER>\\AppData\\Roaming"
        ]
        
        for install_dir in install_dirs:
            try:
                # Find directories related to our binary
                binary_base = self.binary_name.replace('.exe', '').replace('.msi', '')
                
                for root, dirs, files in os.walk(install_dir):
                    if binary_base.lower() in root.lower():
                        for file in files:
                            file_path = os.path.join(root, file)
                            
                            if os.path.exists(file_path):
                                artifact = self._create_file_artifact(
                                    file_path, "installer_file", "Installation file"
                                )
                                if artifact:
                                    artifacts.append(artifact)
                                    
            except Exception as e:
                logger.warning(f"Failed to collect from {install_dir}: {e}")
        
        return artifacts
    
    def _collect_registry_artifacts(self, installer_footprint: Dict) -> List[Dict[str, Any]]:
        """Collect registry changes as artifacts."""
        
        artifacts = []
        
        try:
            # Export registry keys related to the installation
            registry_keys = [
                f"HKEY_LOCAL_MACHINE\\SOFTWARE\\{self.binary_name.replace('.exe', '').replace('.msi', '')}",
                "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
                f"HKEY_CURRENT_USER\\SOFTWARE\\{self.binary_name.replace('.exe', '').replace('.msi', '')}"
            ]
            
            for key in registry_keys:
                try:
                    # Export registry key to file
                    export_file = f"C:\\temp\\registry_export_{key.replace('\\', '_').replace(':', '')}.reg"
                    
                    import subprocess
                    cmd = f'reg export "{key}" "{export_file}" /y'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    
                    if result.returncode == 0 and os.path.exists(export_file):
                        artifact = self._create_file_artifact(
                            export_file, "registry_export", f"Registry export for {key}"
                        )
                        if artifact:
                            artifacts.append(artifact)
                            
                except Exception as e:
                    logger.warning(f"Failed to export registry key {key}: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to collect registry artifacts: {e}")
        
        return artifacts
    
    def _create_file_artifact(self, file_path: str, artifact_type: str, description: str) -> Dict[str, Any]:
        """Create artifact metadata for a file."""
        
        try:
            if not os.path.exists(file_path):
                return None
            
            # Get file stats
            stat = os.stat(file_path)
            
            # Calculate file hash
            file_hash = self._calculate_file_hash(file_path)
            
            # Read file content (for small files) or create reference
            file_content = None
            if stat.st_size < 10 * 1024 * 1024:  # Less than 10MB
                try:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()
                except:
                    pass  # Binary files or permission issues
            
            return {
                "file_path": file_path,
                "artifact_type": artifact_type,
                "description": description,
                "file_size_bytes": stat.st_size,
                "file_hash_sha256": file_hash,
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "file_content": file_content,
                "collection_timestamp": self.collection_timestamp.isoformat(),
                "vm_id": self.vm_id,
                "file_uuid": self.file_uuid,
                "binary_name": self.binary_name
            }
            
        except Exception as e:
            logger.warning(f"Failed to create artifact for {file_path}: {e}")
            return None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file."""
        
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except:
            return "hash_calculation_failed"
    
    def _create_artifact_package(self, installer_artifacts: List, runtime_artifacts: List, system_artifacts: List) -> List[Dict[str, Any]]:
        """Create comprehensive artifact package."""
        
        all_artifacts = []
        
        # Add metadata to each artifact
        for artifact in installer_artifacts:
            artifact["phase"] = "installer"
            all_artifacts.append(artifact)
        
        for artifact in runtime_artifacts:
            artifact["phase"] = "runtime"
            all_artifacts.append(artifact)
        
        for artifact in system_artifacts:
            artifact["phase"] = "system"
            all_artifacts.append(artifact)
        
        # Create package metadata
        package_metadata = {
            "artifact_type": "package_metadata",
            "description": "Artifact collection metadata",
            "total_artifacts": len(all_artifacts),
            "installer_artifacts": len(installer_artifacts),
            "runtime_artifacts": len(runtime_artifacts),
            "system_artifacts": len(system_artifacts),
            "collection_timestamp": self.collection_timestamp.isoformat(),
            "vm_id": self.vm_id,
            "file_uuid": self.file_uuid,
            "binary_name": self.binary_name,
            "phase": "metadata"
        }
        
        all_artifacts.append(package_metadata)
        
        return all_artifacts
    
    def _upload_artifacts_to_minio(self, artifacts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Upload all artifacts to MinIO with proper organization."""
        
        upload_results = []
        
        try:
            # Import MinIO client
            from services.api.src.services.minio_service import upload_file
            
            for i, artifact in enumerate(artifacts):
                try:
                    # Create artifact filename
                    artifact_filename = self._generate_artifact_filename(artifact, i)
                    
                    # Create artifact content
                    artifact_content = self._prepare_artifact_content(artifact)
                    
                    # Upload to MinIO
                    bucket_name = "turdparty-artifacts"
                    object_key = f"{self.file_uuid}/artifacts/{artifact['phase']}/{artifact_filename}"
                    
                    # Upload artifact
                    upload_result = upload_file(
                        file_content=artifact_content,
                        bucket_name=bucket_name,
                        object_key=object_key,
                        content_type="application/octet-stream"
                    )
                    
                    upload_results.append({
                        "artifact_index": i,
                        "artifact_type": artifact.get("artifact_type"),
                        "filename": artifact_filename,
                        "bucket": bucket_name,
                        "object_key": object_key,
                        "upload_success": upload_result.get("success", False),
                        "file_size": len(artifact_content),
                        "upload_timestamp": datetime.utcnow().isoformat()
                    })
                    
                    logger.info(f"Uploaded artifact {i+1}/{len(artifacts)}: {artifact_filename}")
                    
                except Exception as e:
                    logger.error(f"Failed to upload artifact {i}: {e}")
                    upload_results.append({
                        "artifact_index": i,
                        "upload_success": False,
                        "error": str(e)
                    })
            
        except Exception as e:
            logger.error(f"Artifact upload failed: {e}")
        
        return upload_results
    
    def _generate_artifact_filename(self, artifact: Dict[str, Any], index: int) -> str:
        """Generate appropriate filename for artifact."""
        
        artifact_type = artifact.get("artifact_type", "unknown")
        timestamp = self.collection_timestamp.strftime("%Y%m%d_%H%M%S")
        
        if artifact.get("file_path"):
            # Use original filename with metadata
            original_name = os.path.basename(artifact["file_path"])
            return f"{timestamp}_{artifact_type}_{index:03d}_{original_name}"
        else:
            # Generate filename based on type
            return f"{timestamp}_{artifact_type}_{index:03d}.json"
    
    def _prepare_artifact_content(self, artifact: Dict[str, Any]) -> bytes:
        """Prepare artifact content for upload."""
        
        if artifact.get("file_content"):
            # Binary file content
            return artifact["file_content"]
        else:
            # JSON metadata
            artifact_copy = artifact.copy()
            artifact_copy.pop("file_content", None)  # Remove None file_content
            return json.dumps(artifact_copy, indent=2, default=str).encode('utf-8')
    
    # Real implementation methods for artifact collection
    def _collect_runtime_files(self, runtime_footprint: Dict) -> List[Dict[str, Any]]:
        """Collect real runtime-created files."""
        artifacts = []

        try:
            # Common runtime file locations
            runtime_paths = [
                "C:\\Users\\<USER>\\AppData\\Local",
                "C:\\Users\\<USER>\\AppData\\Roaming",
                "C:\\temp",
                "C:\\Windows\\Temp"
            ]

            binary_base = self.binary_name.replace('.exe', '').replace('.msi', '')

            for path in runtime_paths:
                try:
                    # Execute file search command in VM
                    cmd = [
                        "docker", "exec", self.vm_id,
                        "powershell", "-Command",
                        f"Get-ChildItem -Path '{path}' -Recurse -File | Where-Object {{$_.Name -like '*{binary_base}*'}} | Select-Object FullName, Length, CreationTime"
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                    if result.returncode == 0 and result.stdout.strip():
                        # Parse PowerShell output for files
                        lines = result.stdout.strip().split('\n')
                        for line in lines[2:]:  # Skip header lines
                            if line.strip():
                                parts = line.split()
                                if len(parts) >= 3:
                                    file_path = parts[0]
                                    artifact = self._create_file_artifact(
                                        file_path, "runtime_file", f"Runtime file from {path}"
                                    )
                                    if artifact:
                                        artifacts.append(artifact)

                except Exception as e:
                    logger.warning(f"Failed to collect runtime files from {path}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect runtime files: {e}")

        return artifacts

    def _collect_configuration_files(self) -> List[Dict[str, Any]]:
        """Collect real configuration files."""
        artifacts = []

        try:
            # Common config file patterns
            config_patterns = ["*.config", "*.ini", "*.xml", "*.json", "*.cfg"]
            binary_base = self.binary_name.replace('.exe', '').replace('.msi', '')

            for pattern in config_patterns:
                try:
                    cmd = [
                        "docker", "exec", self.vm_id,
                        "powershell", "-Command",
                        f"Get-ChildItem -Path 'C:\\Program Files', 'C:\\Users\\<USER>\\AppData' -Recurse -File -Include '{pattern}' | Where-Object {{$_.Directory.Name -like '*{binary_base}*'}} | Select-Object FullName"
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')
                        for line in lines[2:]:  # Skip header
                            if line.strip():
                                file_path = line.strip()
                                artifact = self._create_file_artifact(
                                    file_path, "config_file", f"Configuration file: {pattern}"
                                )
                                if artifact:
                                    artifacts.append(artifact)

                except Exception as e:
                    logger.warning(f"Failed to collect config files for pattern {pattern}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect configuration files: {e}")

        return artifacts

    def _collect_user_data_files(self) -> List[Dict[str, Any]]:
        """Collect real user data files."""
        artifacts = []

        try:
            # User data locations
            user_paths = [
                "C:\\Users\\<USER>\\Documents",
                "C:\\Users\\<USER>\\Desktop",
                "C:\\Users\\<USER>\\Downloads"
            ]

            binary_base = self.binary_name.replace('.exe', '').replace('.msi', '')

            for path in user_paths:
                try:
                    cmd = [
                        "docker", "exec", self.vm_id,
                        "powershell", "-Command",
                        f"Get-ChildItem -Path '{path}' -Recurse -File | Where-Object {{$_.Name -like '*{binary_base}*' -or $_.Extension -eq '.lnk'}} | Select-Object FullName"
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')
                        for line in lines[2:]:  # Skip header
                            if line.strip():
                                file_path = line.strip()
                                artifact = self._create_file_artifact(
                                    file_path, "user_data", f"User data file from {path}"
                                )
                                if artifact:
                                    artifacts.append(artifact)

                except Exception as e:
                    logger.warning(f"Failed to collect user data from {path}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect user data files: {e}")

        return artifacts

    def _collect_temporary_files(self) -> List[Dict[str, Any]]:
        """Collect real temporary files."""
        artifacts = []

        try:
            temp_paths = ["C:\\temp", "C:\\Windows\\Temp", "C:\\Users\\<USER>\\AppData\\Local\\Temp"]

            for path in temp_paths:
                try:
                    cmd = [
                        "docker", "exec", self.vm_id,
                        "powershell", "-Command",
                        f"Get-ChildItem -Path '{path}' -File | Where-Object {{$_.CreationTime -gt (Get-Date).AddHours(-1)}} | Select-Object FullName"
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')
                        for line in lines[2:]:  # Skip header
                            if line.strip():
                                file_path = line.strip()
                                artifact = self._create_file_artifact(
                                    file_path, "temp_file", f"Temporary file from {path}"
                                )
                                if artifact:
                                    artifacts.append(artifact)

                except Exception as e:
                    logger.warning(f"Failed to collect temp files from {path}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect temporary files: {e}")

        return artifacts

    def _collect_installation_logs(self) -> List[Dict[str, Any]]:
        """Collect real installation logs."""
        artifacts = []

        try:
            # Common log locations
            log_paths = [
                "C:\\Windows\\Logs",
                "C:\\temp",
                "C:\\Users\\<USER>\\AppData\\Local\\Temp"
            ]

            log_patterns = ["*.log", "*.txt"]

            for path in log_paths:
                for pattern in log_patterns:
                    try:
                        cmd = [
                            "docker", "exec", self.vm_id,
                            "powershell", "-Command",
                            f"Get-ChildItem -Path '{path}' -File -Include '{pattern}' | Where-Object {{$_.CreationTime -gt (Get-Date).AddHours(-1)}} | Select-Object FullName"
                        ]

                        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                        if result.returncode == 0 and result.stdout.strip():
                            lines = result.stdout.strip().split('\n')
                            for line in lines[2:]:  # Skip header
                                if line.strip():
                                    file_path = line.strip()
                                    artifact = self._create_file_artifact(
                                        file_path, "install_log", f"Installation log: {pattern}"
                                    )
                                    if artifact:
                                        artifacts.append(artifact)

                    except Exception as e:
                        logger.warning(f"Failed to collect logs from {path} with pattern {pattern}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect installation logs: {e}")

        return artifacts


@shared_task(bind=True, name="services.workers.tasks.artifact_collector.collect_and_upload_artifacts")
def collect_and_upload_artifacts(self, vm_id: str, file_uuid: str, binary_name: str, 
                                installer_footprint: Dict, runtime_footprint: Dict) -> Dict[str, Any]:
    """Celery task to collect and upload all artifacts."""
    
    try:
        collector = ArtifactCollector(vm_id, file_uuid, binary_name)
        result = collector.collect_all_artifacts(installer_footprint, runtime_footprint)
        
        logger.info(f"Artifact collection completed for {binary_name}")
        return result
        
    except Exception as e:
        logger.error(f"Artifact collection failed: {e}")
        raise
