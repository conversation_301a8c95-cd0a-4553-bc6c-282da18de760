"""Installation wizard completion monitoring and verification."""

import logging
import time
import subprocess
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


class InstallationMonitor:
    """Monitor installation wizard completion and verify success."""
    
    def __init__(self, vm_id: str, installer_path: str, installer_name: str):
        self.vm_id = vm_id
        self.installer_path = installer_path
        self.installer_name = installer_name
        self.installation_start_time = None
        self.installation_end_time = None
        self.installation_phase = "pre_install"  # pre_install, installing, post_install, completed
        
    def start_installation_monitoring(self, timeout_minutes: int = 15) -> Dict[str, Any]:
        """Start comprehensive installation monitoring."""
        
        logger.info(f"Starting installation monitoring for {self.installer_name}")
        self.installation_start_time = datetime.utcnow()
        
        # Phase 1: Pre-installation snapshot
        pre_install_snapshot = self._take_system_snapshot("pre_install")
        
        # Phase 2: Execute installer with monitoring
        installation_result = self._execute_installer_with_monitoring(timeout_minutes)
        
        # Phase 3: Post-installation verification
        post_install_snapshot = self._take_system_snapshot("post_install")
        
        # Phase 4: Verify installation completion
        completion_verification = self._verify_installation_completion()
        
        self.installation_end_time = datetime.utcnow()
        installation_duration = (self.installation_end_time - self.installation_start_time).total_seconds()
        
        return {
            "installation_successful": installation_result.get("success", False),
            "installation_duration_seconds": installation_duration,
            "installer_exit_code": installation_result.get("exit_code"),
            "installation_phases": {
                "pre_install": pre_install_snapshot,
                "installation": installation_result,
                "post_install": post_install_snapshot,
                "verification": completion_verification
            },
            "installation_footprint": self._calculate_installation_footprint(
                pre_install_snapshot, post_install_snapshot
            ),
            "wizard_completion_verified": completion_verification.get("wizard_completed", False),
            "installation_artifacts": completion_verification.get("artifacts", [])
        }
    
    def _take_system_snapshot(self, phase: str) -> Dict[str, Any]:
        """Take comprehensive system snapshot before/after installation."""
        
        logger.info(f"Taking system snapshot: {phase}")
        
        try:
            # Get file system snapshot
            files_snapshot = self._get_filesystem_snapshot()
            
            # Get registry snapshot (Windows)
            registry_snapshot = self._get_registry_snapshot()
            
            # Get running processes
            processes_snapshot = self._get_processes_snapshot()
            
            # Get services snapshot
            services_snapshot = self._get_services_snapshot()
            
            # Get network connections
            network_snapshot = self._get_network_snapshot()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "phase": phase,
                "files": files_snapshot,
                "registry": registry_snapshot,
                "processes": processes_snapshot,
                "services": services_snapshot,
                "network": network_snapshot,
                "snapshot_duration_seconds": 0  # Will be calculated
            }
            
        except Exception as e:
            logger.error(f"Failed to take system snapshot: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "phase": phase,
                "error": str(e),
                "snapshot_failed": True
            }
    
    def _execute_installer_with_monitoring(self, timeout_minutes: int) -> Dict[str, Any]:
        """Execute installer with real-time monitoring."""
        
        logger.info(f"Executing installer: {self.installer_path}")
        self.installation_phase = "installing"
        
        try:
            # Determine installer type and appropriate silent flags
            silent_flags = self._get_silent_installation_flags()
            
            # Build command
            cmd = [self.installer_path] + silent_flags
            
            logger.info(f"Installer command: {' '.join(cmd)}")
            
            # Execute with monitoring
            start_time = time.time()
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd="C:\\temp"
            )
            
            # Monitor process execution
            monitoring_data = self._monitor_installation_process(process, timeout_minutes)
            
            # Wait for completion
            try:
                stdout, stderr = process.communicate(timeout=timeout_minutes * 60)
                exit_code = process.returncode
                
            except subprocess.TimeoutExpired:
                logger.warning(f"Installer timed out after {timeout_minutes} minutes")
                process.kill()
                stdout, stderr = process.communicate()
                exit_code = -1
            
            end_time = time.time()
            execution_duration = end_time - start_time
            
            # Analyze exit code
            success = self._analyze_exit_code(exit_code)
            
            return {
                "success": success,
                "exit_code": exit_code,
                "execution_duration_seconds": execution_duration,
                "stdout": stdout,
                "stderr": stderr,
                "monitoring_data": monitoring_data,
                "installer_command": ' '.join(cmd),
                "timeout_occurred": exit_code == -1
            }
            
        except Exception as e:
            logger.error(f"Installer execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_failed": True
            }
    
    def _get_silent_installation_flags(self) -> List[str]:
        """Get appropriate silent installation flags based on installer type."""
        
        installer_lower = self.installer_name.lower()
        
        # Common silent installation flags
        if installer_lower.endswith('.msi'):
            return ['/quiet', '/norestart', '/l*v', 'C:\\temp\\install.log']
        elif installer_lower.endswith('.exe'):
            # Try common silent flags
            if 'nsis' in installer_lower or 'nullsoft' in installer_lower:
                return ['/S']  # NSIS installer
            elif 'innosetup' in installer_lower or 'inno' in installer_lower:
                return ['/SILENT', '/NORESTART']  # Inno Setup
            elif 'installshield' in installer_lower:
                return ['/s', '/v"/qn"']  # InstallShield
            else:
                # Try multiple common flags
                return ['/S', '/silent', '/quiet']
        else:
            return ['/silent']
    
    def _monitor_installation_process(self, process: subprocess.Popen, timeout_minutes: int) -> Dict[str, Any]:
        """Monitor installation process in real-time."""
        
        monitoring_data = {
            "process_monitoring": [],
            "file_changes": [],
            "registry_changes": [],
            "performance_metrics": []
        }
        
        start_time = time.time()
        
        while process.poll() is None and (time.time() - start_time) < (timeout_minutes * 60):
            try:
                # Monitor process tree
                if process.pid:
                    try:
                        parent = psutil.Process(process.pid)
                        children = parent.children(recursive=True)
                        
                        process_info = {
                            "timestamp": datetime.utcnow().isoformat(),
                            "parent_pid": process.pid,
                            "child_processes": len(children),
                            "memory_usage_mb": parent.memory_info().rss / 1024 / 1024,
                            "cpu_percent": parent.cpu_percent()
                        }
                        
                        monitoring_data["process_monitoring"].append(process_info)
                        
                    except psutil.NoSuchProcess:
                        pass
                
                # Sleep before next check
                time.sleep(2)
                
            except Exception as e:
                logger.warning(f"Process monitoring error: {e}")
        
        return monitoring_data
    
    def _analyze_exit_code(self, exit_code: int) -> bool:
        """Analyze installer exit code to determine success."""
        
        # Common successful exit codes
        success_codes = [0, 3010]  # 0 = success, 3010 = success with reboot required
        
        # Common error codes
        error_codes = {
            1: "General error",
            2: "Misuse of shell command",
            1602: "User cancelled installation",
            1603: "Fatal error during installation",
            1618: "Another installation is in progress",
            1619: "Installation package could not be opened",
            1620: "Installation package could not be opened",
            1633: "This installation package is not supported on this platform"
        }
        
        if exit_code in success_codes:
            logger.info(f"Installation successful with exit code: {exit_code}")
            return True
        elif exit_code in error_codes:
            logger.error(f"Installation failed: {error_codes[exit_code]} (exit code: {exit_code})")
            return False
        else:
            logger.warning(f"Unknown exit code: {exit_code}, treating as failure")
            return False
    
    def _verify_installation_completion(self) -> Dict[str, Any]:
        """Verify that installation completed successfully."""
        
        logger.info("Verifying installation completion")
        
        verification_results = {
            "wizard_completed": False,
            "artifacts": [],
            "verification_checks": []
        }
        
        try:
            # Check 1: Look for installation artifacts
            artifacts_check = self._check_installation_artifacts()
            verification_results["verification_checks"].append(artifacts_check)
            
            # Check 2: Verify registry entries
            registry_check = self._check_registry_entries()
            verification_results["verification_checks"].append(registry_check)
            
            # Check 3: Check for running processes
            process_check = self._check_installed_processes()
            verification_results["verification_checks"].append(process_check)
            
            # Check 4: Verify file permissions and integrity
            file_check = self._check_file_integrity()
            verification_results["verification_checks"].append(file_check)
            
            # Determine overall completion status
            successful_checks = sum(1 for check in verification_results["verification_checks"] if check.get("passed", False))
            total_checks = len(verification_results["verification_checks"])
            
            verification_results["wizard_completed"] = successful_checks >= (total_checks * 0.75)  # 75% success rate
            verification_results["success_rate"] = successful_checks / total_checks if total_checks > 0 else 0
            
            logger.info(f"Installation verification: {successful_checks}/{total_checks} checks passed")
            
        except Exception as e:
            logger.error(f"Installation verification failed: {e}")
            verification_results["error"] = str(e)
        
        return verification_results
    
    def _check_installation_artifacts(self) -> Dict[str, Any]:
        """Check for expected installation artifacts."""
        
        # Common installation locations
        common_paths = [
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\Users\\<USER>\\AppData\\Local",
            "C:\\Users\\<USER>\\AppData\\Roaming",
            "C:\\ProgramData"
        ]
        
        artifacts_found = []
        
        for path in common_paths:
            try:
                # Look for recently created directories/files
                cmd = f'dir "{path}" /ad /od /t:c'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # Parse output for recent directories
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if self.installer_name.replace('.exe', '').replace('.msi', '') in line:
                            artifacts_found.append(f"{path}\\{line.strip()}")
                            
            except Exception as e:
                logger.warning(f"Failed to check path {path}: {e}")
        
        return {
            "check_name": "installation_artifacts",
            "passed": len(artifacts_found) > 0,
            "artifacts_found": artifacts_found,
            "artifact_count": len(artifacts_found)
        }
    
    def _check_registry_entries(self) -> Dict[str, Any]:
        """Check for expected registry entries."""
        
        registry_keys = [
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
            "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
        ]
        
        entries_found = []
        
        for key in registry_keys:
            try:
                cmd = f'reg query "{key}" /s'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # Look for entries related to our installer
                    if self.installer_name.replace('.exe', '').replace('.msi', '') in result.stdout:
                        entries_found.append(key)
                        
            except Exception as e:
                logger.warning(f"Failed to check registry key {key}: {e}")
        
        return {
            "check_name": "registry_entries",
            "passed": len(entries_found) > 0,
            "registry_entries": entries_found,
            "entry_count": len(entries_found)
        }
    
    def _check_installed_processes(self) -> Dict[str, Any]:
        """Check for processes related to the installed application."""
        
        processes_found = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    proc_name = proc.info['name'].lower()
                    installer_base = self.installer_name.replace('.exe', '').replace('.msi', '').lower()
                    
                    if installer_base in proc_name:
                        processes_found.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "exe": proc.info['exe']
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.warning(f"Failed to check processes: {e}")
        
        return {
            "check_name": "installed_processes",
            "passed": len(processes_found) > 0,
            "processes_found": processes_found,
            "process_count": len(processes_found)
        }
    
    def _check_file_integrity(self) -> Dict[str, Any]:
        """Check file integrity of installed files."""
        
        # This is a simplified check - in production, you'd verify file signatures, checksums, etc.
        return {
            "check_name": "file_integrity",
            "passed": True,  # Simplified for demo
            "integrity_verified": True
        }
    
    def _get_filesystem_snapshot(self) -> Dict[str, Any]:
        """Get filesystem snapshot."""
        # Simplified implementation
        return {"file_count": 0, "directories": []}
    
    def _get_registry_snapshot(self) -> Dict[str, Any]:
        """Get registry snapshot."""
        # Simplified implementation
        return {"key_count": 0, "keys": []}
    
    def _get_processes_snapshot(self) -> Dict[str, Any]:
        """Get processes snapshot."""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                processes.append({"pid": proc.info['pid'], "name": proc.info['name']})
            return {"process_count": len(processes), "processes": processes}
        except:
            return {"process_count": 0, "processes": []}
    
    def _get_services_snapshot(self) -> Dict[str, Any]:
        """Get services snapshot."""
        # Simplified implementation
        return {"service_count": 0, "services": []}
    
    def _get_network_snapshot(self) -> Dict[str, Any]:
        """Get network connections snapshot."""
        # Simplified implementation
        return {"connection_count": 0, "connections": []}
    
    def _calculate_installation_footprint(self, pre_snapshot: Dict, post_snapshot: Dict) -> Dict[str, Any]:
        """Calculate the installation footprint by comparing snapshots."""
        
        return {
            "files_added": post_snapshot.get("files", {}).get("file_count", 0) - pre_snapshot.get("files", {}).get("file_count", 0),
            "registry_keys_added": post_snapshot.get("registry", {}).get("key_count", 0) - pre_snapshot.get("registry", {}).get("key_count", 0),
            "processes_added": post_snapshot.get("processes", {}).get("process_count", 0) - pre_snapshot.get("processes", {}).get("process_count", 0),
            "services_added": post_snapshot.get("services", {}).get("service_count", 0) - pre_snapshot.get("services", {}).get("service_count", 0)
        }


@shared_task(bind=True, name="services.workers.tasks.installation_monitor.monitor_installation")
def monitor_installation(self, vm_id: str, installer_path: str, installer_name: str, timeout_minutes: int = 15) -> Dict[str, Any]:
    """Celery task to monitor installation completion."""
    
    try:
        monitor = InstallationMonitor(vm_id, installer_path, installer_name)
        result = monitor.start_installation_monitoring(timeout_minutes)
        
        logger.info(f"Installation monitoring completed for {installer_name}")
        return result
        
    except Exception as e:
        logger.error(f"Installation monitoring failed: {e}")
        raise
