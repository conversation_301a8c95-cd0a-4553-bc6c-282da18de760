"""
Report Generation Celery Tasks
Handles queuing and processing of analysis reports after workflow completion.
"""

import asyncio
import json
import os
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from celery import shared_task
from celery.utils.log import get_task_logger
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .elk_integration import stream_workflow_event

# Database setup for workers (same pattern as other worker tasks)
DATABASE_URL = os.getenv("DATABASE_URL", "********************************************/turdparty")
engine = create_engine(DATABASE_URL)

logger = get_task_logger(__name__)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@shared_task(bind=True, name="services.workers.tasks.report_generation.queue_report_generation")
def queue_report_generation(self, workflow_job_id: str, delay_seconds: int = 30) -> Dict[str, Any]:
    """
    Queue report generation after workflow completion and VM cleanup.
    
    Args:
        workflow_job_id: The workflow job ID
        delay_seconds: Delay before starting report generation (default: 30 seconds)
    
    Returns:
        Dict containing task information and report generation details
    """
    try:
        logger.info(f"Queuing report generation for workflow: {workflow_job_id}")
        
        # Get workflow and file information
        with SessionLocal() as db:
            workflow_data = db.execute(
                text("""
                    SELECT 
                        wj.id as workflow_id,
                        wj.status,
                        wj.completed_at,
                        wj.terminated_at,
                        fu.file_uuid,
                        fu.filename,
                        fu.file_size,
                        fu.file_hash,
                        vm.vm_id,
                        vm.name as vm_name,
                        vm.status as vm_status
                    FROM workflow_jobs wj
                    JOIN file_uploads fu ON wj.file_upload_id = fu.id
                    LEFT JOIN vm_instances vm ON wj.vm_instance_id = vm.id
                    WHERE wj.id = :workflow_id
                """),
                {"workflow_id": workflow_job_id}
            ).fetchone()
            
            if not workflow_data:
                raise Exception(f"Workflow not found: {workflow_job_id}")
        
        # Convert to dict for easier access
        workflow_info = {
            "workflow_id": workflow_data.workflow_id,
            "status": workflow_data.status,
            "completed_at": workflow_data.completed_at,
            "terminated_at": workflow_data.terminated_at,
            "file_uuid": workflow_data.file_uuid,
            "filename": workflow_data.filename,
            "file_size": workflow_data.file_size,
            "file_hash": workflow_data.file_hash,
            "vm_id": workflow_data.vm_id,
            "vm_name": workflow_data.vm_name,
            "vm_status": workflow_data.vm_status
        }
        
        # Schedule the actual report generation with delay
        report_task = generate_analysis_report.apply_async(
            args=[workflow_info],
            countdown=delay_seconds
        )
        
        # Log the queuing event
        stream_workflow_event.delay(
            workflow_job_id,
            "report_generation_queued",
            {
                "file_uuid": workflow_info["file_uuid"],
                "filename": workflow_info["filename"],
                "report_task_id": report_task.id,
                "delay_seconds": delay_seconds,
                "scheduled_at": (datetime.now(timezone.utc).timestamp() + delay_seconds)
            }
        )
        
        logger.info(f"Report generation queued for {workflow_info['file_uuid']} with {delay_seconds}s delay")
        
        return {
            "success": True,
            "workflow_job_id": workflow_job_id,
            "file_uuid": workflow_info["file_uuid"],
            "filename": workflow_info["filename"],
            "report_task_id": report_task.id,
            "delay_seconds": delay_seconds,
            "message": f"Report generation queued for {workflow_info['filename']}"
        }
        
    except Exception as e:
        logger.error(f"Failed to queue report generation: {e}")
        
        # Log the failure
        stream_workflow_event.delay(
            workflow_job_id,
            "report_generation_queue_failed",
            {
                "error": str(e),
                "workflow_id": workflow_job_id
            }
        )
        
        return {
            "success": False,
            "workflow_job_id": workflow_job_id,
            "error": str(e),
            "message": "Failed to queue report generation"
        }


@shared_task(bind=True, name="services.workers.tasks.report_generation.generate_analysis_report")
def generate_analysis_report(self, workflow_id: str) -> Dict[str, Any]:
    """
    Generate comprehensive analysis report for a completed workflow.

    Args:
        workflow_id: The workflow ID to generate a report for

    Returns:
        Dict containing report generation results
    """
    try:
        logger.info(f"Starting report generation for workflow: {workflow_id}")

        # Get workflow information from database
        with SessionLocal() as db:
            workflow_data = db.execute(
                text("""
                    SELECT
                        wj.id as workflow_id,
                        wj.status,
                        wj.completed_at,
                        wj.terminated_at,
                        fu.file_uuid,
                        fu.filename,
                        fu.file_size,
                        fu.file_hash
                    FROM workflow_jobs wj
                    JOIN file_uploads fu ON wj.file_upload_id = fu.id
                    WHERE wj.id = :workflow_id
                """),
                {"workflow_id": workflow_id}
            ).fetchone()

            if not workflow_data:
                raise Exception(f"Workflow not found: {workflow_id}")

            # Convert to dict for easier access
            workflow_info = {
                "workflow_id": str(workflow_data.workflow_id),
                "file_uuid": workflow_data.file_uuid,
                "filename": workflow_data.filename,
                "file_size": workflow_data.file_size,
                "file_hash": workflow_data.file_hash,
                "status": workflow_data.status,
                "completed_at": workflow_data.completed_at,
                "terminated_at": workflow_data.terminated_at
            }

        file_uuid = workflow_info["file_uuid"]
        filename = workflow_info["filename"]
        
        logger.info(f"Starting report generation for {filename} (UUID: {file_uuid})")
        
        # Wait for ECS data to be fully indexed
        logger.info("Waiting for ECS data indexing to complete...")
        time.sleep(10)
        
        # Verify ECS data availability
        ecs_availability = _check_ecs_data_availability(file_uuid)
        
        if not ecs_availability["sufficient_data"]:
            logger.warning(f"Insufficient ECS data for {file_uuid}, proceeding with available data")
        
        # Generate the report using the SphinxReportGenerator
        report_result = _generate_sphinx_report(file_uuid, workflow_info)
        
        # Log report generation completion
        stream_workflow_event.delay(
            workflow_id,
            "report_generation_completed",
            {
                "file_uuid": file_uuid,
                "filename": filename,
                "report_success": report_result["success"],
                "report_url": report_result.get("report_url"),
                "ecs_events_processed": ecs_availability.get("total_events", 0),
                "generation_time": report_result.get("generation_time", 0)
            }
        )
        
        if report_result["success"]:
            logger.info(f"Report generated successfully for {filename}: {report_result.get('report_url')}")
        else:
            logger.error(f"Report generation failed for {filename}: {report_result.get('error')}")
        
        return {
            "success": report_result["success"],
            "file_uuid": file_uuid,
            "filename": filename,
            "workflow_id": workflow_id,
            "report_url": report_result.get("report_url"),
            "report_file": report_result.get("rst_file"),
            "ecs_data_summary": ecs_availability,
            "generation_time": report_result.get("generation_time", 0),
            "error": report_result.get("error") if not report_result["success"] else None
        }
        
    except Exception as e:
        logger.error(f"Report generation task failed: {e}")

        # Log the failure
        stream_workflow_event.delay(
            workflow_id,
            "report_generation_failed",
            {
                "workflow_id": workflow_id,
                "error": str(e)
            }
        )

        return {
            "success": False,
            "workflow_id": workflow_id,
            "error": str(e)
        }


def _check_ecs_data_availability(file_uuid: str) -> Dict[str, Any]:
    """Check if sufficient ECS data is available for report generation."""
    try:
        from elasticsearch import Elasticsearch
        
        es_client = Elasticsearch(
            hosts=["http://localhost:9200"],
            timeout=30
        )
        
        # Check for ECS data across all relevant indices
        indices = [
            "turdparty-install-ecs-*",
            "turdparty-runtime-ecs-*",
            "turdparty-installation-base-*",
            "turdparty-system-events-*"
        ]
        
        total_events = 0
        index_counts = {}
        
        for index_pattern in indices:
            try:
                result = es_client.count(
                    index=index_pattern,
                    body={
                        "query": {
                            "term": {
                                "file_uuid.keyword": file_uuid
                            }
                        }
                    }
                )
                count = result["count"]
                total_events += count
                index_counts[index_pattern] = count
                
            except Exception as e:
                logger.warning(f"Could not check {index_pattern}: {e}")
                index_counts[index_pattern] = 0
        
        # Determine if we have sufficient data
        sufficient_data = total_events >= 10  # Minimum threshold
        
        return {
            "sufficient_data": sufficient_data,
            "total_events": total_events,
            "index_counts": index_counts,
            "file_uuid": file_uuid,
            "checked_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to check ECS data availability: {e}")
        return {
            "sufficient_data": False,
            "total_events": 0,
            "error": str(e),
            "file_uuid": file_uuid
        }


def _generate_sphinx_report(file_uuid: str, workflow_info: Dict[str, Any]) -> Dict[str, Any]:
    """Generate Sphinx report using the existing report generator."""
    try:
        start_time = time.time()
        
        # Import and use the existing SphinxReportGenerator
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), "../../../"))
        
        from services.report_generator import SphinxReportGenerator
        
        # Run the async report generation
        async def generate_report():
            generator = SphinxReportGenerator()
            try:
                result = await generator.generate_report_from_uuid(file_uuid)
                return result
            finally:
                await generator.cleanup()
        
        # Execute the async function
        result = asyncio.run(generate_report())
        
        generation_time = time.time() - start_time
        result["generation_time"] = generation_time
        
        return result
        
    except Exception as e:
        logger.error(f"Sphinx report generation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_uuid": file_uuid
        }


@shared_task(bind=True, name="services.workers.tasks.report_generation.cleanup_old_reports")
def cleanup_old_reports(self, days_old: int = 30) -> Dict[str, Any]:
    """
    Clean up old report files to manage disk space.
    
    Args:
        days_old: Remove reports older than this many days
    
    Returns:
        Dict containing cleanup results
    """
    try:
        logger.info(f"Starting cleanup of reports older than {days_old} days")
        
        import os
        from pathlib import Path
        
        reports_dir = Path("docs/reports/reports")
        if not reports_dir.exists():
            return {
                "success": True,
                "message": "Reports directory does not exist",
                "files_removed": 0
            }
        
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        files_removed = 0
        
        for report_file in reports_dir.glob("*.rst"):
            if report_file.stat().st_mtime < cutoff_time:
                try:
                    report_file.unlink()
                    files_removed += 1
                    logger.info(f"Removed old report: {report_file.name}")
                except Exception as e:
                    logger.warning(f"Failed to remove {report_file.name}: {e}")
        
        logger.info(f"Cleanup completed: {files_removed} files removed")
        
        return {
            "success": True,
            "files_removed": files_removed,
            "days_old": days_old,
            "message": f"Removed {files_removed} old report files"
        }
        
    except Exception as e:
        logger.error(f"Report cleanup failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "files_removed": 0
        }
