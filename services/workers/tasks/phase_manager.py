"""Phase management for distinguishing installer footprint vs runtime footprint."""

import logging
import time
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, List
from enum import Enum
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)


class AnalysisPhase(Enum):
    """Analysis phases for binary execution."""
    PRE_INSTALL = "pre_install"
    INSTALLER_EXECUTION = "installer_execution"
    POST_INSTALL = "post_install"
    RUNTIME_PREPARATION = "runtime_preparation"
    RUNTIME_EXECUTION = "runtime_execution"
    POST_RUNTIME = "post_runtime"
    CLEANUP = "cleanup"


class PhaseManager:
    """Manage distinct phases of binary analysis to separate installer vs runtime footprints."""
    
    def __init__(self, vm_id: str, file_uuid: str, binary_name: str):
        self.vm_id = vm_id
        self.file_uuid = file_uuid
        self.binary_name = binary_name
        self.current_phase = None
        self.phase_data = {}
        self.phase_transitions = []
        
    def execute_full_analysis(self) -> Dict[str, Any]:
        """Execute complete phased analysis with clear installer/runtime separation."""
        
        logger.info(f"Starting phased analysis for {self.binary_name}")
        
        analysis_start = datetime.utcnow()
        
        try:
            # Phase 1: Pre-installation baseline
            phase1_result = self._execute_phase(AnalysisPhase.PRE_INSTALL)
            
            # Phase 2: Installer execution and monitoring
            phase2_result = self._execute_phase(AnalysisPhase.INSTALLER_EXECUTION)
            
            # Phase 3: Post-installation verification
            phase3_result = self._execute_phase(AnalysisPhase.POST_INSTALL)
            
            # Phase 4: Runtime preparation (if applicable)
            phase4_result = self._execute_phase(AnalysisPhase.RUNTIME_PREPARATION)
            
            # Phase 5: Runtime execution monitoring
            phase5_result = self._execute_phase(AnalysisPhase.RUNTIME_EXECUTION)
            
            # Phase 6: Post-runtime analysis
            phase6_result = self._execute_phase(AnalysisPhase.POST_RUNTIME)
            
            # Phase 7: Cleanup
            phase7_result = self._execute_phase(AnalysisPhase.CLEANUP)
            
            analysis_end = datetime.utcnow()
            total_duration = (analysis_end - analysis_start).total_seconds()
            
            # Generate comprehensive analysis report
            return self._generate_phase_analysis_report(total_duration)
            
        except Exception as e:
            logger.error(f"Phased analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "completed_phases": list(self.phase_data.keys())
            }
    
    def _execute_phase(self, phase: AnalysisPhase) -> Dict[str, Any]:
        """Execute a specific analysis phase."""
        
        logger.info(f"Executing phase: {phase.value}")
        
        phase_start = datetime.utcnow()
        self.current_phase = phase
        
        try:
            if phase == AnalysisPhase.PRE_INSTALL:
                result = self._execute_pre_install_phase()
            elif phase == AnalysisPhase.INSTALLER_EXECUTION:
                result = self._execute_installer_phase()
            elif phase == AnalysisPhase.POST_INSTALL:
                result = self._execute_post_install_phase()
            elif phase == AnalysisPhase.RUNTIME_PREPARATION:
                result = self._execute_runtime_prep_phase()
            elif phase == AnalysisPhase.RUNTIME_EXECUTION:
                result = self._execute_runtime_phase()
            elif phase == AnalysisPhase.POST_RUNTIME:
                result = self._execute_post_runtime_phase()
            elif phase == AnalysisPhase.CLEANUP:
                result = self._execute_cleanup_phase()
            else:
                raise ValueError(f"Unknown phase: {phase}")
            
            phase_end = datetime.utcnow()
            phase_duration = (phase_end - phase_start).total_seconds()
            
            # Store phase data
            self.phase_data[phase.value] = {
                "phase": phase.value,
                "start_time": phase_start.isoformat(),
                "end_time": phase_end.isoformat(),
                "duration_seconds": phase_duration,
                "success": result.get("success", True),
                "data": result
            }
            
            # Record phase transition
            self.phase_transitions.append({
                "from_phase": self.phase_transitions[-1]["to_phase"] if self.phase_transitions else None,
                "to_phase": phase.value,
                "transition_time": phase_start.isoformat(),
                "duration_seconds": phase_duration
            })
            
            logger.info(f"Phase {phase.value} completed in {phase_duration:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Phase {phase.value} failed: {e}")
            
            # Store failure data
            self.phase_data[phase.value] = {
                "phase": phase.value,
                "start_time": phase_start.isoformat(),
                "end_time": datetime.utcnow().isoformat(),
                "success": False,
                "error": str(e)
            }
            
            raise
    
    def _execute_pre_install_phase(self) -> Dict[str, Any]:
        """Execute pre-installation baseline capture."""
        
        logger.info("Capturing pre-installation baseline")
        
        # Start ELK monitoring for installer phase
        self._start_elk_monitoring("installer")
        
        # Take comprehensive system snapshot
        baseline_snapshot = {
            "filesystem": self._capture_filesystem_state(),
            "registry": self._capture_registry_state(),
            "processes": self._capture_process_state(),
            "services": self._capture_services_state(),
            "network": self._capture_network_state(),
            "performance": self._capture_performance_baseline()
        }
        
        return {
            "success": True,
            "baseline_snapshot": baseline_snapshot,
            "elk_monitoring_started": True,
            "monitoring_index": f"turdparty-installer-{self.file_uuid}"
        }
    
    def _execute_installer_phase(self) -> Dict[str, Any]:
        """Execute installer with comprehensive monitoring."""
        
        logger.info("Executing installer phase")
        
        # Import installation monitor
        from .installation_monitor import InstallationMonitor
        
        # Execute installer with full monitoring
        monitor = InstallationMonitor(self.vm_id, f"C:\\temp\\{self.binary_name}", self.binary_name)
        installation_result = monitor.start_installation_monitoring(timeout_minutes=15)
        
        # Capture installer-specific ELK data
        installer_elk_data = self._capture_elk_data("installer", installation_result["installation_duration_seconds"])
        
        return {
            "success": installation_result["installation_successful"],
            "installation_result": installation_result,
            "installer_footprint": installation_result["installation_footprint"],
            "elk_data": installer_elk_data,
            "phase_type": "INSTALLER_FOOTPRINT"
        }
    
    def _execute_post_install_phase(self) -> Dict[str, Any]:
        """Execute post-installation verification and footprint calculation."""
        
        logger.info("Executing post-installation verification")
        
        # Stop installer ELK monitoring
        self._stop_elk_monitoring("installer")
        
        # Take post-installation snapshot
        post_install_snapshot = {
            "filesystem": self._capture_filesystem_state(),
            "registry": self._capture_registry_state(),
            "processes": self._capture_process_state(),
            "services": self._capture_services_state(),
            "network": self._capture_network_state()
        }
        
        # Calculate installer footprint
        installer_footprint = self._calculate_installer_footprint(
            self.phase_data["pre_install"]["data"]["baseline_snapshot"],
            post_install_snapshot
        )
        
        # Wait for system stabilization
        time.sleep(30)  # Allow system to settle after installation
        
        return {
            "success": True,
            "post_install_snapshot": post_install_snapshot,
            "installer_footprint": installer_footprint,
            "system_stabilized": True,
            "phase_type": "INSTALLER_FOOTPRINT_COMPLETE"
        }
    
    def _execute_runtime_prep_phase(self) -> Dict[str, Any]:
        """Prepare for runtime execution phase."""
        
        logger.info("Preparing for runtime execution")
        
        # Start separate ELK monitoring for runtime
        self._start_elk_monitoring("runtime")
        
        # Take pre-runtime baseline (post-installation state)
        runtime_baseline = {
            "filesystem": self._capture_filesystem_state(),
            "registry": self._capture_registry_state(),
            "processes": self._capture_process_state(),
            "services": self._capture_services_state(),
            "network": self._capture_network_state()
        }
        
        # Identify installed executable for runtime testing
        installed_executable = self._find_installed_executable()
        
        return {
            "success": True,
            "runtime_baseline": runtime_baseline,
            "installed_executable": installed_executable,
            "elk_monitoring_started": True,
            "monitoring_index": f"turdparty-runtime-{self.file_uuid}",
            "phase_type": "RUNTIME_PREPARATION"
        }
    
    def _execute_runtime_phase(self) -> Dict[str, Any]:
        """Execute runtime behavior monitoring."""
        
        logger.info("Executing runtime behavior monitoring")
        
        runtime_start = datetime.utcnow()
        
        # Get installed executable path
        executable_path = self.phase_data["runtime_preparation"]["data"]["installed_executable"]
        
        if not executable_path:
            logger.warning("No installed executable found, skipping runtime phase")
            return {
                "success": False,
                "reason": "No installed executable found",
                "phase_type": "RUNTIME_SKIPPED"
            }
        
        # Execute the installed application
        runtime_result = self._execute_installed_application(executable_path)
        
        # Monitor runtime behavior for specified duration
        runtime_monitoring = self._monitor_runtime_behavior(duration_minutes=5)
        
        runtime_end = datetime.utcnow()
        runtime_duration = (runtime_end - runtime_start).total_seconds()
        
        # Capture runtime ELK data
        runtime_elk_data = self._capture_elk_data("runtime", runtime_duration)
        
        return {
            "success": True,
            "runtime_execution": runtime_result,
            "runtime_monitoring": runtime_monitoring,
            "runtime_duration_seconds": runtime_duration,
            "elk_data": runtime_elk_data,
            "phase_type": "RUNTIME_FOOTPRINT"
        }
    
    def _execute_post_runtime_phase(self) -> Dict[str, Any]:
        """Execute post-runtime analysis."""
        
        logger.info("Executing post-runtime analysis")
        
        # Stop runtime ELK monitoring
        self._stop_elk_monitoring("runtime")
        
        # Take post-runtime snapshot
        post_runtime_snapshot = {
            "filesystem": self._capture_filesystem_state(),
            "registry": self._capture_registry_state(),
            "processes": self._capture_process_state(),
            "services": self._capture_services_state(),
            "network": self._capture_network_state()
        }
        
        # Calculate runtime footprint
        runtime_footprint = self._calculate_runtime_footprint(
            self.phase_data["runtime_preparation"]["data"]["runtime_baseline"],
            post_runtime_snapshot
        )
        
        return {
            "success": True,
            "post_runtime_snapshot": post_runtime_snapshot,
            "runtime_footprint": runtime_footprint,
            "phase_type": "RUNTIME_FOOTPRINT_COMPLETE"
        }
    
    def _execute_cleanup_phase(self) -> Dict[str, Any]:
        """Execute cleanup and final data collection."""
        
        logger.info("Executing cleanup phase")
        
        # Collect all artifacts for MinIO upload
        artifacts_to_upload = self._collect_all_artifacts()
        
        # Generate final phase summary
        phase_summary = self._generate_phase_summary()
        
        return {
            "success": True,
            "artifacts_collected": len(artifacts_to_upload),
            "artifacts": artifacts_to_upload,
            "phase_summary": phase_summary,
            "phase_type": "CLEANUP_COMPLETE"
        }
    
    def _generate_phase_analysis_report(self, total_duration: float) -> Dict[str, Any]:
        """Generate comprehensive phase analysis report."""
        
        # Separate installer and runtime data
        installer_phases = ["pre_install", "installer_execution", "post_install"]
        runtime_phases = ["runtime_preparation", "runtime_execution", "post_runtime"]
        
        installer_data = {phase: self.phase_data.get(phase) for phase in installer_phases if phase in self.phase_data}
        runtime_data = {phase: self.phase_data.get(phase) for phase in runtime_phases if phase in self.phase_data}
        
        # Calculate phase durations
        installer_duration = sum(
            phase_data.get("duration_seconds", 0) 
            for phase_data in installer_data.values() 
            if phase_data
        )
        
        runtime_duration = sum(
            phase_data.get("duration_seconds", 0) 
            for phase_data in runtime_data.values() 
            if phase_data
        )
        
        return {
            "success": True,
            "total_analysis_duration_seconds": total_duration,
            "phase_breakdown": {
                "installer_duration_seconds": installer_duration,
                "runtime_duration_seconds": runtime_duration,
                "cleanup_duration_seconds": self.phase_data.get("cleanup", {}).get("duration_seconds", 0)
            },
            "installer_footprint": {
                "phases": installer_data,
                "elk_index": f"turdparty-installer-{self.file_uuid}",
                "footprint_type": "INSTALLATION"
            },
            "runtime_footprint": {
                "phases": runtime_data,
                "elk_index": f"turdparty-runtime-{self.file_uuid}",
                "footprint_type": "RUNTIME"
            },
            "phase_transitions": self.phase_transitions,
            "artifacts_for_upload": self.phase_data.get("cleanup", {}).get("data", {}).get("artifacts", [])
        }
    
    # Real implementation methods
    def _start_elk_monitoring(self, phase_type: str):
        """Start real ELK monitoring for specific phase."""
        try:
            from .elk_integration import stream_workflow_event

            # Create ELK index for this phase
            index_name = f"turdparty-{phase_type}-{self.file_uuid}"

            # Stream phase start event
            stream_workflow_event.delay(
                self.file_uuid,
                f"{phase_type}_phase_started",
                {
                    "phase_type": phase_type,
                    "vm_id": self.vm_id,
                    "binary_name": self.binary_name,
                    "index_name": index_name
                }
            )

            logger.info(f"Started ELK monitoring for {phase_type} phase: {index_name}")

        except Exception as e:
            logger.error(f"Failed to start ELK monitoring for {phase_type}: {e}")

    def _stop_elk_monitoring(self, phase_type: str):
        """Stop real ELK monitoring for specific phase."""
        try:
            from .elk_integration import stream_workflow_event

            # Stream phase end event
            stream_workflow_event.delay(
                self.file_uuid,
                f"{phase_type}_phase_completed",
                {
                    "phase_type": phase_type,
                    "vm_id": self.vm_id,
                    "binary_name": self.binary_name
                }
            )

            logger.info(f"Stopped ELK monitoring for {phase_type} phase")

        except Exception as e:
            logger.error(f"Failed to stop ELK monitoring for {phase_type}: {e}")

    def _capture_filesystem_state(self) -> Dict[str, Any]:
        """Capture real filesystem state via VM command execution."""
        try:
            # Execute filesystem snapshot command in VM
            cmd = [
                "docker", "exec", self.vm_id,
                "powershell", "-Command",
                "Get-ChildItem -Path 'C:\\Program Files', 'C:\\Program Files (x86)', 'C:\\ProgramData' -Recurse -File | Measure-Object | Select-Object Count"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                # Parse PowerShell output to get file count
                lines = result.stdout.strip().split('\n')
                file_count = 0
                for line in lines:
                    if 'Count' in line and ':' in line:
                        try:
                            file_count = int(line.split(':')[1].strip())
                            break
                        except:
                            pass

                return {
                    "file_count": file_count,
                    "capture_time": datetime.utcnow().isoformat(),
                    "vm_id": self.vm_id
                }
            else:
                logger.warning(f"Filesystem capture failed: {result.stderr}")
                return {"file_count": 0, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to capture filesystem state: {e}")
            return {"file_count": 0, "error": str(e)}

    def _capture_registry_state(self) -> Dict[str, Any]:
        """Capture real registry state via VM command execution."""
        try:
            # Execute registry snapshot command in VM
            cmd = [
                "docker", "exec", self.vm_id,
                "powershell", "-Command",
                "Get-ChildItem -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall' | Measure-Object | Select-Object Count"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                # Parse PowerShell output to get registry key count
                lines = result.stdout.strip().split('\n')
                key_count = 0
                for line in lines:
                    if 'Count' in line and ':' in line:
                        try:
                            key_count = int(line.split(':')[1].strip())
                            break
                        except:
                            pass

                return {
                    "key_count": key_count,
                    "capture_time": datetime.utcnow().isoformat(),
                    "vm_id": self.vm_id
                }
            else:
                logger.warning(f"Registry capture failed: {result.stderr}")
                return {"key_count": 0, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to capture registry state: {e}")
            return {"key_count": 0, "error": str(e)}

    def _capture_process_state(self) -> Dict[str, Any]:
        """Capture real process state via VM command execution."""
        try:
            # Execute process snapshot command in VM
            cmd = [
                "docker", "exec", self.vm_id,
                "powershell", "-Command",
                "Get-Process | Measure-Object | Select-Object Count"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                # Parse PowerShell output to get process count
                lines = result.stdout.strip().split('\n')
                process_count = 0
                for line in lines:
                    if 'Count' in line and ':' in line:
                        try:
                            process_count = int(line.split(':')[1].strip())
                            break
                        except:
                            pass

                return {
                    "process_count": process_count,
                    "capture_time": datetime.utcnow().isoformat(),
                    "vm_id": self.vm_id
                }
            else:
                logger.warning(f"Process capture failed: {result.stderr}")
                return {"process_count": 0, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to capture process state: {e}")
            return {"process_count": 0, "error": str(e)}

    def _capture_services_state(self) -> Dict[str, Any]:
        """Capture real services state via VM command execution."""
        try:
            # Execute services snapshot command in VM
            cmd = [
                "docker", "exec", self.vm_id,
                "powershell", "-Command",
                "Get-Service | Measure-Object | Select-Object Count"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                # Parse PowerShell output to get service count
                lines = result.stdout.strip().split('\n')
                service_count = 0
                for line in lines:
                    if 'Count' in line and ':' in line:
                        try:
                            service_count = int(line.split(':')[1].strip())
                            break
                        except:
                            pass

                return {
                    "service_count": service_count,
                    "capture_time": datetime.utcnow().isoformat(),
                    "vm_id": self.vm_id
                }
            else:
                logger.warning(f"Services capture failed: {result.stderr}")
                return {"service_count": 0, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to capture services state: {e}")
            return {"service_count": 0, "error": str(e)}

    def _capture_network_state(self) -> Dict[str, Any]:
        """Capture real network state via VM command execution."""
        try:
            # Execute network snapshot command in VM
            cmd = [
                "docker", "exec", self.vm_id,
                "powershell", "-Command",
                "Get-NetTCPConnection | Measure-Object | Select-Object Count"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                # Parse PowerShell output to get connection count
                lines = result.stdout.strip().split('\n')
                connection_count = 0
                for line in lines:
                    if 'Count' in line and ':' in line:
                        try:
                            connection_count = int(line.split(':')[1].strip())
                            break
                        except:
                            pass

                return {
                    "connection_count": connection_count,
                    "capture_time": datetime.utcnow().isoformat(),
                    "vm_id": self.vm_id
                }
            else:
                logger.warning(f"Network capture failed: {result.stderr}")
                return {"connection_count": 0, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to capture network state: {e}")
            return {"connection_count": 0, "error": str(e)}


@shared_task(bind=True, name="services.workers.tasks.phase_manager.execute_phased_analysis")
def execute_phased_analysis(self, vm_id: str, file_uuid: str, binary_name: str) -> Dict[str, Any]:
    """Celery task to execute phased analysis."""
    
    try:
        manager = PhaseManager(vm_id, file_uuid, binary_name)
        result = manager.execute_full_analysis()
        
        logger.info(f"Phased analysis completed for {binary_name}")
        return result
        
    except Exception as e:
        logger.error(f"Phased analysis failed: {e}")
        raise
