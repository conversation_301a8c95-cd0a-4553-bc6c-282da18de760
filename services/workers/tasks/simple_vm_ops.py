"""
Simplified VM operations for TurdParty workflow.
Mock VM operations for demo purposes.
"""

import logging
import os
import time
import uuid
from typing import Dict, Any
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.get_vm_for_processing")
def get_vm_for_processing(self, template: str = "gusztavvargadr/windows-10") -> Dict[str, Any]:
    """
    Get a VM from the pool for file processing.
    Mock implementation for demo.
    """
    try:
        logger.info(f"Getting VM for processing (template: {template})")

        # Import VM manager
        from services.workers.vm_manager import VMManager
        vm_manager = VMManager()

        # Generate unique VM name
        vm_id = str(uuid.uuid4())
        vm_name = f"turdparty-vm-{vm_id[:8]}"

        # VM configuration
        vm_config = {
            "name": vm_name,
            "template": template,
            "memory_mb": 4096,
            "cpus": 2,
            "vm_type": "docker"  # Use Docker for faster provisioning
        }

        # Create VM using actual VM manager
        if template.startswith("gusztavvargadr/"):
            # Use Docker for Windows templates
            import asyncio
            vm_result = asyncio.run(vm_manager.create_docker_vm(vm_config))
        else:
            # Use Vagrant for other templates
            import asyncio
            vm_result = asyncio.run(vm_manager.create_vagrant_vm(vm_config))

        if not vm_result.get("success"):
            raise Exception(f"VM creation failed: {vm_result.get('error')}")

        vm_info = {
            "id": vm_result.get("vm_id", vm_id),
            "name": vm_name,
            "template": template,
            "status": "running",
            "ip_address": vm_result.get("ip_address", "*************"),
            "memory_mb": 4096,
            "cpus": 2,
            "created_at": time.time(),
            "vm_dir": vm_result.get("vm_dir"),
            "container_id": vm_result.get("container_id")
        }

        logger.info(f"VM allocated successfully: {vm_name}")

        return {
            "success": True,
            "vm": vm_info
        }

    except Exception as e:
        logger.error(f"Failed to get VM for processing: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.inject_file")
def inject_file(self, workflow_job_id: str, file_uuid: str, file_path: str, vm_id: str, target_path: str) -> Dict[str, Any]:
    """
    Inject file into VM.
    Mock implementation for demo.
    """
    try:
        logger.info(f"Injecting file into VM {vm_id}: {file_path} -> {target_path}")

        # Use existing injection task implementation
        from services.workers.tasks.injection_tasks import inject_file_into_vm

        # Call actual injection task
        injection_result = inject_file_into_vm.delay(
            workflow_job_id=workflow_job_id,
            file_uuid=file_uuid,
            vm_id=vm_id,
            target_path=target_path
        )

        # Wait for injection to complete
        result = injection_result.get(timeout=300)  # 5 minute timeout

        if result.get("success"):
            logger.info(f"File injection completed for VM {vm_id}")
            return {
                "success": True,
                "injection_path": target_path,
                "vm_id": vm_id,
                "source_path": file_path,
                "file_uuid": file_uuid,
                "workflow_job_id": workflow_job_id,
                "injected_at": time.time(),
                **result
            }
        else:
            raise Exception(f"Injection failed: {result.get('error')}")

    except Exception as e:
        logger.error(f"File injection failed for VM {vm_id}: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.execute_command_in_vm")
def execute_command_in_vm(self, vm_id: str, command: str, working_directory: str = "C:\\temp", timeout_seconds: int = 300) -> Dict[str, Any]:
    """
    Execute command in VM.
    Mock implementation for demo.
    """
    try:
        logger.info(f"Executing command in VM {vm_id}: {command}")

        # Import Docker client for actual execution
        import docker
        import subprocess

        start_time = time.time()

        # Check if VM is a Docker container
        if vm_id.startswith("turdparty_vm_"):
            # Execute in Docker container
            client = docker.from_env()
            try:
                container = client.containers.get(vm_id)

                # Execute command in container
                exec_result = container.exec_run(
                    cmd=command,
                    workdir=working_directory,
                    stdout=True,
                    stderr=True
                )

                execution_time = time.time() - start_time

                execution_result = {
                    "vm_id": vm_id,
                    "command": command,
                    "working_directory": working_directory,
                    "exit_code": exec_result.exit_code,
                    "stdout": exec_result.output.decode('utf-8') if exec_result.output else "",
                    "stderr": "",
                    "execution_time": execution_time,
                    "executed_at": time.time()
                }

                logger.info(f"Command executed in Docker VM {vm_id}: exit code {exec_result.exit_code}")

            except docker.errors.NotFound:
                raise Exception(f"Docker container {vm_id} not found")

        else:
            # Execute in Vagrant VM
            vm_dir = f"/tmp/turdparty-vm-{vm_id}"
            vagrant_cmd = [
                'vagrant', 'ssh', '-c', f"cd {working_directory} && {command}"
            ]

            result = subprocess.run(
                vagrant_cmd,
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            execution_time = time.time() - start_time

            execution_result = {
                "vm_id": vm_id,
                "command": command,
                "working_directory": working_directory,
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "executed_at": time.time()
            }

            logger.info(f"Command executed in Vagrant VM {vm_id}: exit code {result.returncode}")

        return {
            "success": True,
            **execution_result
        }

    except Exception as e:
        logger.error(f"Command execution failed in VM {vm_id}: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.start_vm_monitoring")
def start_vm_monitoring(self, vm_id: str, workflow_job_id: str, duration_minutes: int = 30) -> Dict[str, Any]:
    """
    Start VM monitoring for data collection.
    Mock implementation for demo.
    """
    try:
        logger.info(f"Starting VM monitoring for {duration_minutes} minutes: {vm_id}")

        # Start actual monitoring using existing VM agent injector
        from services.workers.tasks.vm_agent_injector import inject_monitoring_agent

        # Inject monitoring agent into VM
        agent_result = inject_monitoring_agent.delay(
            workflow_id=workflow_job_id,
            vm_id=vm_id
        )

        # Wait for agent injection
        agent_injection = agent_result.get(timeout=120)

        if not agent_injection.get("success"):
            raise Exception(f"Monitoring agent injection failed: {agent_injection.get('error')}")

        # Start ELK data streaming
        from services.workers.tasks.simple_elk_ops import start_vm_data_streaming

        streaming_result = start_vm_data_streaming.delay(
            vm_id=vm_id,
            workflow_job_id=workflow_job_id,
            duration_minutes=duration_minutes
        )

        monitoring_result = {
            "vm_id": vm_id,
            "workflow_job_id": workflow_job_id,
            "duration_minutes": duration_minutes,
            "monitoring_started_at": time.time(),
            "monitoring_agents": ["file_monitor", "process_monitor", "registry_monitor", "network_monitor"],
            "data_collection_active": True,
            "agent_injection_result": agent_injection,
            "streaming_task_id": streaming_result.id
        }

        logger.info(f"VM monitoring started for {vm_id}")

        return {
            "success": True,
            **monitoring_result
        }

    except Exception as e:
        logger.error(f"VM monitoring failed for {vm_id}: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.terminate_vm")
def terminate_vm(self, vm_id: str) -> Dict[str, Any]:
    """
    Terminate VM and clean up resources.
    Mock implementation for demo.
    """
    try:
        logger.info(f"Terminating VM: {vm_id}")

        # Import VM manager for actual termination
        from services.workers.vm_manager import VMManager
        import docker
        import subprocess
        import shutil

        vm_manager = VMManager()

        # Check if VM is a Docker container
        if vm_id.startswith("turdparty_vm_"):
            # Terminate Docker container
            client = docker.from_env()
            try:
                container = client.containers.get(vm_id)
                container.stop(timeout=30)
                container.remove()
                logger.info(f"Docker container {vm_id} terminated and removed")
            except docker.errors.NotFound:
                logger.warning(f"Docker container {vm_id} not found, may already be terminated")
        else:
            # Terminate Vagrant VM
            vm_dir = f"/tmp/turdparty-vm-{vm_id}"
            if os.path.exists(vm_dir):
                # Destroy Vagrant VM
                result = subprocess.run(
                    ['vagrant', 'destroy', '-f'],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=120
                )

                # Clean up VM directory
                shutil.rmtree(vm_dir, ignore_errors=True)
                logger.info(f"Vagrant VM {vm_id} destroyed and cleaned up")

        termination_result = {
            "vm_id": vm_id,
            "terminated_at": time.time(),
            "cleanup_completed": True,
            "resources_freed": True
        }

        logger.info(f"VM terminated successfully: {vm_id}")

        return {
            "success": True,
            **termination_result
        }

    except Exception as e:
        logger.error(f"VM termination failed for {vm_id}: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_vm_ops.test_volume_mapping")
def test_volume_mapping(self) -> Dict[str, Any]:
    """
    Test function to demonstrate volume mapping works.
    This function was added without rebuilding the container!
    """
    logger.info("🔥 VOLUME MAPPING TEST: This function was added without rebuilding!")

    result = {
        "volume_mapping_test": "SUCCESS",
        "message": "Code changes are immediately available without container rebuild!",
        "timestamp": time.time(),
        "emoji": "🎉"
    }

    logger.info(f"🎉 Volume mapping test completed: {result}")
    return result
