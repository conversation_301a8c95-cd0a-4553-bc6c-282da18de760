"""Enhanced workflow orchestrator for TurdParty file processing."""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from uuid import UUID

from celery import shared_task, chain, group
from celery.utils.log import get_task_logger
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Database setup for workers (same pattern as other worker tasks)
DATABASE_URL = os.getenv("DATABASE_URL", "********************************************/turdparty")
engine = create_engine(DATABASE_URL)
# Import worker tasks
from .vm_pool_manager import get_vm_for_processing, maintain_pool
from .file_operations import download_file_from_minio, validate_file
from .injection_tasks import inject_file
from .vm_management import terminate_vm
from .elk_integration import stream_workflow_event, stream_vm_metrics
from .vm_agent_injector import inject_monitoring_agent
from services.workers.tasks.elk_integration import stream_workflow_event, stream_vm_metrics
from services.workers.tasks.report_generation import queue_report_generation

logger = get_task_logger(__name__)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.start_file_processing_workflow")
def start_file_processing_workflow(self, workflow_job_id: str, file_upload_id: str) -> Dict[str, Any]:
    """
    Start the complete file processing workflow.
    
    Workflow Steps:
    1. Get VM from pool
    2. Download and validate file
    3. Inject file into VM
    4. Monitor execution (30 minutes)
    5. Terminate VM and return to pool
    """
    try:
        logger.info(f"Starting file processing workflow: {workflow_job_id}")
        
        with SessionLocal() as db:
            # Get workflow job and file upload
            workflow_job = db.execute(
                text("SELECT * FROM workflow_jobs WHERE id = :id"),
                {"id": workflow_job_id}
            ).fetchone()
            
            file_upload = db.execute(
                text("SELECT * FROM file_uploads WHERE id = :id"),
                {"id": file_upload_id}
            ).fetchone()
            
            if not workflow_job or not file_upload:
                raise ValueError("Workflow job or file upload not found")
            
            # Update workflow status
            db.execute(
                text("UPDATE workflow_jobs SET status = :status WHERE id = :id"),
                {"status": WorkflowStatus.VM_ALLOCATING.value, "id": workflow_job_id}
            )
            db.commit()
        
        # Step 1: Get VM from pool
        vm_result = get_vm_for_processing.delay(
            template=workflow_job.vm_config.get("template", "ubuntu:20.04")
        )
        
        # Create workflow chain
        workflow_chain = chain(
            # Step 2: Download and validate file
            download_and_validate_file.s(workflow_job_id, file_upload_id),
            
            # Step 3: Inject file into VM
            inject_file_into_vm.s(workflow_job_id),
            
            # Step 4: Monitor and execute
            monitor_vm_execution.s(workflow_job_id),
            
            # Step 5: Cleanup and terminate
            cleanup_workflow.s(workflow_job_id)
        )
        
        # Execute the workflow chain
        result = workflow_chain.apply_async()

        # Schedule 30-minute termination
        terminate_workflow.apply_async(
            args=[workflow_job_id],
            countdown=30 * 60  # 30 minutes
        )

        # Stream workflow start event to ELK
        stream_workflow_event.delay(
            workflow_job_id,
            "workflow_started",
            {
                "outcome": "success",
                "chain_id": result.id,
                "vm_allocation_task": vm_result.id
            }
        )

        logger.info(f"Workflow chain started: {result.id}")

        return {
            "workflow_job_id": workflow_job_id,
            "chain_id": result.id,
            "vm_allocation_task": vm_result.id,
            "status": "started"
        }
    
    except Exception as e:
        logger.error(f"Failed to start workflow: {e}")
        
        # Update workflow status to failed
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status, error_message = :error WHERE id = :id"),
                {"status": WorkflowStatus.FAILED.value, "error": str(e), "id": workflow_job_id}
            )
            db.commit()
        
        raise


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.download_and_validate_file")
def download_and_validate_file(self, workflow_job_id: str, file_upload_id: str) -> Dict[str, Any]:
    """Download file from MinIO and validate it."""
    try:
        logger.info(f"Downloading and validating file for workflow: {workflow_job_id}")
        
        # Update status
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status WHERE id = :id"),
                {"status": WorkflowStatus.FILE_DOWNLOADING.value, "id": workflow_job_id}
            )
            db.commit()
        
        # Download file
        download_result = download_file_from_minio.delay(file_upload_id).get()
        
        if not download_result.get("success"):
            raise Exception(f"File download failed: {download_result.get('error')}")
        
        # Validate file
        validation_result = validate_file.delay(
            download_result["file_path"],
            download_result["file_size"]
        ).get()
        
        if not validation_result.get("valid"):
            raise Exception(f"File validation failed: {validation_result.get('error')}")
        
        logger.info(f"File downloaded and validated successfully: {download_result['file_path']}")

        # Stream file download completion to ELK
        stream_workflow_event.delay(
            workflow_job_id,
            "file_downloaded",
            {
                "outcome": "success",
                "file_path": download_result["file_path"],
                "file_size": download_result["file_size"],
                "validation": validation_result
            }
        )

        return {
            "workflow_job_id": workflow_job_id,
            "file_path": download_result["file_path"],
            "file_size": download_result["file_size"],
            "validation": validation_result
        }
    
    except Exception as e:
        logger.error(f"File download/validation failed: {e}")
        
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status, error_message = :error WHERE id = :id"),
                {"status": WorkflowStatus.FAILED.value, "error": str(e), "id": workflow_job_id}
            )
            db.commit()
        
        raise


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.inject_file_into_vm")
def inject_file_into_vm(self, previous_result: Dict[str, Any], workflow_job_id: str) -> Dict[str, Any]:
    """Inject the downloaded file into the allocated VM."""
    try:
        logger.info(f"Injecting file into VM for workflow: {workflow_job_id}")
        
        # Update status
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status WHERE id = :id"),
                {"status": WorkflowStatus.FILE_INJECTING.value, "id": workflow_job_id}
            )
            db.commit()
        
        # Get VM for this workflow
        with SessionLocal() as db:
            vm_instance = db.execute(
                text("""
                    SELECT vm.* FROM vm_instances vm
                    JOIN workflow_jobs wj ON wj.vm_instance_id = vm.id
                    WHERE wj.id = :workflow_id
                """),
                {"workflow_id": workflow_job_id}
            ).fetchone()
            
            if not vm_instance:
                raise Exception("No VM allocated for workflow")
        
        # Inject file
        injection_result = inject_file.delay(
            workflow_job_id,
            previous_result["file_path"]
        ).get()
        
        if not injection_result.get("success"):
            raise Exception(f"File injection failed: {injection_result.get('error')}")
        
        logger.info(f"File injected successfully into VM: {vm_instance.name}")

        # Stream file injection completion to ELK
        stream_workflow_event.delay(
            workflow_job_id,
            "file_injected",
            {
                "outcome": "success",
                "vm_id": str(vm_instance.id),
                "vm_name": vm_instance.name,
                "injection_path": injection_result["injection_path"]
            }
        )

        # Inject monitoring agent into VM
        inject_monitoring_agent.delay(str(vm_instance.id), workflow_job_id)

        return {
            "workflow_job_id": workflow_job_id,
            "vm_id": str(vm_instance.id),
            "injection_path": injection_result["injection_path"],
            "file_path": previous_result["file_path"]
        }
    
    except Exception as e:
        logger.error(f"File injection failed: {e}")
        
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status, error_message = :error WHERE id = :id"),
                {"status": WorkflowStatus.FAILED.value, "error": str(e), "id": workflow_job_id}
            )
            db.commit()
        
        raise


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.monitor_vm_execution")
def monitor_vm_execution(self, previous_result: Dict[str, Any], workflow_job_id: str) -> Dict[str, Any]:
    """Enhanced VM execution monitoring with installer/runtime phase distinction."""
    try:
        logger.info(f"Starting enhanced VM execution monitoring for workflow: {workflow_job_id}")

        # Update status
        with SessionLocal() as db:
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()

            if not workflow_job:
                raise ValueError(f"Workflow job not found: {workflow_job_id}")

            workflow_job.status = WorkflowStatus.VM_EXECUTING
            workflow_job.update_progress("vm_execution", 85, "Starting enhanced execution monitoring")
            db.commit()

            # Get VM and file information
            vm_instance = db.query(VMInstance).filter(
                VMInstance.id == workflow_job.vm_instance_id
            ).first()

            file_upload = db.query(FileUpload).filter(
                FileUpload.id == workflow_job.file_upload_id
            ).first()

            if not vm_instance or not file_upload:
                raise ValueError("VM instance or file upload not found")

            # Execute phased analysis with installer/runtime distinction
            # Only run enhanced analysis if VM is actually running
            phased_result = {}

            if vm_instance.status == VMStatus.RUNNING and vm_instance.vm_id:
                try:
                    from .phase_manager import execute_phased_analysis

                    phased_result = execute_phased_analysis.delay(
                        vm_instance.vm_id,  # Use actual VM ID, not database ID
                        file_upload.file_uuid,
                        file_upload.filename
                    ).get(timeout=1800)  # 30 minutes timeout

                except Exception as e:
                    logger.error(f"Phased analysis failed: {e}")
                    phased_result = {
                        "success": False,
                        "error": str(e),
                        "installer_footprint": {},
                        "runtime_footprint": {}
                    }
            else:
                logger.warning(f"VM not running, skipping enhanced analysis. VM status: {vm_instance.status}")
                phased_result = {
                    "success": False,
                    "reason": "VM not running",
                    "installer_footprint": {},
                    "runtime_footprint": {}
                }

            # Collect and upload artifacts (only if VM is running)
            artifact_result = {}

            if vm_instance.status == VMStatus.RUNNING and vm_instance.vm_id:
                try:
                    from .artifact_collector import collect_and_upload_artifacts

                    artifact_result = collect_and_upload_artifacts.delay(
                        vm_instance.vm_id,  # Use actual VM ID
                        file_upload.file_uuid,
                        file_upload.filename,
                        phased_result.get("installer_footprint", {}),
                        phased_result.get("runtime_footprint", {})
                    ).get(timeout=600)  # 10 minutes timeout

                except Exception as e:
                    logger.error(f"Artifact collection failed: {e}")
                    artifact_result = {
                        "success": False,
                        "error": str(e),
                        "artifacts_collected": 0
                    }
            else:
                logger.warning("VM not running, skipping artifact collection")
                artifact_result = {
                    "success": False,
                    "reason": "VM not running",
                    "artifacts_collected": 0
                }

            # Update workflow with comprehensive results
            workflow_job.status = WorkflowStatus.COMPLETED
            workflow_job.update_progress("execution_complete", 95, "Enhanced execution monitoring completed")

            if not workflow_job.results:
                workflow_job.results = {}

            workflow_job.results.update({
                "enhanced_monitoring": True,
                "phased_analysis": phased_result,
                "artifact_collection": artifact_result,
                "installer_footprint": phased_result.get("installer_footprint", {}),
                "runtime_footprint": phased_result.get("runtime_footprint", {}),
                "installation_verified": phased_result.get("installer_footprint", {}).get("phases", {}).get("installer_execution", {}).get("data", {}).get("installation_result", {}).get("wizard_completion_verified", False),
                "artifacts_uploaded": artifact_result.get("success", False),
                "total_artifacts": artifact_result.get("artifacts_collected", 0)
            })

            db.commit()

            logger.info(f"Enhanced VM execution monitoring completed for workflow: {workflow_job_id}")

            return {
                "success": True,
                "workflow_job_id": workflow_job_id,
                "phased_analysis": phased_result,
                "artifact_collection": artifact_result,
                "installation_verified": workflow_job.results["installation_verified"],
                "artifacts_uploaded": workflow_job.results["artifacts_uploaded"]
            }
        
        # TODO: Implement actual monitoring logic
        # For now, just simulate monitoring
        logger.info(f"VM execution monitoring active for 30 minutes")
        
        return {
            "workflow_job_id": workflow_job_id,
            "vm_id": previous_result["vm_id"],
            "monitoring_started": datetime.utcnow().isoformat(),
            "execution_timeout": 30 * 60  # 30 minutes
        }
    
    except Exception as e:
        logger.error(f"VM monitoring failed: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.cleanup_workflow")
def cleanup_workflow(self, previous_result: Dict[str, Any], workflow_job_id: str) -> Dict[str, Any]:
    """Clean up workflow resources."""
    try:
        logger.info(f"Cleaning up workflow: {workflow_job_id}")
        
        # Update status
        with SessionLocal() as db:
            db.execute(
                text("UPDATE workflow_jobs SET status = :status, completed_at = :completed_at WHERE id = :id"),
                {"status": WorkflowStatus.COMPLETED.value, "completed_at": datetime.utcnow(), "id": workflow_job_id}
            )
            db.commit()
        
        # Stream workflow completion to ELK
        stream_workflow_event.delay(
            workflow_job_id,
            "workflow_completed",
            {
                "outcome": "success",
                "status": "completed"
            }
        )

        # Queue report generation after workflow completion
        # This will wait for VM cleanup and ECS data indexing before generating reports
        report_queue_result = queue_report_generation.delay(
            workflow_job_id,
            delay_seconds=60  # Wait 60 seconds for VM cleanup and ECS indexing
        )

        logger.info(f"Workflow completed successfully: {workflow_job_id}")
        logger.info(f"Report generation queued with task ID: {report_queue_result.id}")

        return {
            "workflow_job_id": workflow_job_id,
            "status": "completed",
            "completed_at": datetime.utcnow().isoformat(),
            "report_queue_task_id": report_queue_result.id
        }
    
    except Exception as e:
        logger.error(f"Workflow cleanup failed: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.workflow_orchestrator.terminate_workflow")
def terminate_workflow(self, workflow_job_id: str) -> Dict[str, Any]:
    """Terminate workflow after 30 minutes."""
    try:
        logger.info(f"Terminating workflow after 30 minutes: {workflow_job_id}")
        
        with SessionLocal() as db:
            # Get VM for this workflow
            vm_instance = db.execute(
                text("""
                    SELECT vm.* FROM vm_instances vm
                    JOIN workflow_jobs wj ON wj.vm_instance_id = vm.id
                    WHERE wj.id = :workflow_id
                """),
                {"workflow_id": workflow_job_id}
            ).fetchone()
            
            if vm_instance:
                # Terminate VM
                terminate_vm.delay(str(vm_instance.id), "docker")
                
                # Update workflow status
                db.execute(
                    text("UPDATE workflow_jobs SET status = :status, terminated_at = :terminated_at WHERE id = :id"),
                    {"status": WorkflowStatus.TERMINATED.value, "terminated_at": datetime.utcnow(), "id": workflow_job_id}
                )
                db.commit()
                
                # Trigger pool maintenance to replace terminated VM
                maintain_pool.delay()

                # Queue report generation after VM termination and cleanup
                # This ensures the report is generated even for terminated workflows
                report_queue_result = queue_report_generation.delay(
                    workflow_job_id,
                    delay_seconds=90  # Wait longer for termination cleanup
                )

                logger.info(f"Workflow terminated and VM destroyed: {vm_instance.name}")
                logger.info(f"Report generation queued for terminated workflow: {report_queue_result.id}")

        return {
            "workflow_job_id": workflow_job_id,
            "action": "terminated",
            "terminated_at": datetime.utcnow().isoformat(),
            "report_queue_task_id": getattr(report_queue_result, 'id', None)
        }
    
    except Exception as e:
        logger.error(f"Workflow termination failed: {e}")
        raise
