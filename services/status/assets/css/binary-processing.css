/* Binary Processing Dashboard Styles */

.binary-processing-section {
    padding: 20px;
    background: var(--bg-primary);
    border-radius: 12px;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.last-update {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

/* Processing Stats */
.processing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 4px;
}

/* Binary Grid */
.binary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.binary-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.binary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-color);
}

.binary-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.binary-info {
    flex: 1;
}

.binary-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    word-break: break-word;
}

.binary-type {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 500;
    background: var(--bg-primary);
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
}

.binary-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status-icon {
    font-size: 1.2rem;
}

/* Progress Bar */
.progress-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

.progress-text {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 35px;
    text-align: right;
}

/* Binary Details */
.binary-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.detail-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
    font-family: 'Courier New', monospace;
    background: var(--bg-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* Empty State */
.no-binaries {
    text-align: center;
    padding: 60px 20px;
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.6;
}

.empty-state h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

/* Tab Integration */
.tab-button[data-tab="binary-processing"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.tab-button[data-tab="binary-processing"]:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.tab-button[data-tab="binary-processing"].active {
    background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .binary-grid {
        grid-template-columns: 1fr;
    }
    
    .processing-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .dashboard-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .binary-card-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .binary-status {
        align-self: flex-start;
    }
}

@media (max-width: 480px) {
    .processing-stats {
        grid-template-columns: 1fr;
    }
    
    .binary-processing-section {
        padding: 15px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .binary-card {
        padding: 15px;
    }
}

/* Animation for new cards */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.binary-card {
    animation: slideInUp 0.3s ease-out;
}

/* Pulse animation for active processing */
.binary-card[data-status="processing"] .status-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}
