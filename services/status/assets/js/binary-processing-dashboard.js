// Binary Processing Dashboard for TurdParty Status
// Real-time monitoring of all in-progress binary analysis

class BinaryProcessingDashboard {
    constructor() {
        this.processingBinaries = new Map();
        this.updateInterval = null;
        this.refreshRate = 2000; // 2 seconds
        this.apiBaseUrl = 'http://api.turdparty.localhost/api/v1';
        
        // Processing stages with icons and colors
        this.stages = {
            'pending': { icon: '⏳', color: '#fbbf24', label: 'Pending' },
            'file_uploaded': { icon: '📁', color: '#3b82f6', label: 'File Uploaded' },
            'file_stored': { icon: '💾', color: '#3b82f6', label: 'File Stored' },
            'vm_creating': { icon: '🔧', color: '#f59e0b', label: 'Creating VM' },
            'vm_running': { icon: '🖥️', color: '#10b981', label: 'VM Running' },
            'file_injecting': { icon: '💉', color: '#8b5cf6', label: 'Injecting File' },
            'monitoring': { icon: '👁️', color: '#06b6d4', label: 'Monitoring' },
            'completed': { icon: '✅', color: '#10b981', label: 'Completed' },
            'failed': { icon: '❌', color: '#ef4444', label: 'Failed' }
        };
    }

    async initialize() {
        console.log('Initializing Binary Processing Dashboard...');
        this.createDashboardSection();
        await this.startMonitoring();
    }

    createDashboardSection() {
        // Find the main content area
        const mainContent = document.querySelector('.tab-content-container');
        if (!mainContent) {
            console.error('Main content area not found');
            return;
        }

        // Create binary processing tab
        const tabNav = document.querySelector('.tab-nav');
        if (tabNav) {
            const binaryTab = document.createElement('button');
            binaryTab.className = 'tab-button';
            binaryTab.setAttribute('data-tab', 'binary-processing');
            binaryTab.innerHTML = '🔬 Binary Processing';
            binaryTab.addEventListener('click', () => this.showBinaryTab());
            tabNav.appendChild(binaryTab);
        }

        // Create binary processing content
        const binaryContent = document.createElement('div');
        binaryContent.className = 'tab-content';
        binaryContent.id = 'binary-processing-tab';
        binaryContent.innerHTML = `
            <section class="binary-processing-section">
                <div class="section-header">
                    <h2>🔬 Binary Processing Dashboard</h2>
                    <div class="dashboard-controls">
                        <button id="refresh-binaries" class="btn-primary">🔄 Refresh</button>
                        <span class="last-update">Last updated: <span id="last-update-time">Never</span></span>
                    </div>
                </div>
                
                <div class="processing-stats">
                    <div class="stat-card">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-content">
                            <div class="stat-value" id="active-count">0</div>
                            <div class="stat-label">Active</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <div class="stat-value" id="pending-count">0</div>
                            <div class="stat-label">Pending</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-value" id="completed-count">0</div>
                            <div class="stat-label">Completed</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">❌</div>
                        <div class="stat-content">
                            <div class="stat-value" id="failed-count">0</div>
                            <div class="stat-label">Failed</div>
                        </div>
                    </div>
                </div>

                <div class="binary-grid" id="binary-grid">
                    <!-- Binary processing cards will be populated here -->
                </div>

                <div class="no-binaries" id="no-binaries" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">📭</div>
                        <h3>No Active Binary Processing</h3>
                        <p>All binary analysis workflows are currently idle.</p>
                    </div>
                </div>
            </section>
        `;

        mainContent.appendChild(binaryContent);

        // Add event listeners
        document.getElementById('refresh-binaries')?.addEventListener('click', () => this.refreshData());
    }

    showBinaryTab() {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });

        // Show binary processing tab
        document.getElementById('binary-processing-tab')?.classList.add('active');
        document.querySelector('[data-tab="binary-processing"]')?.classList.add('active');
    }

    async startMonitoring() {
        console.log('Starting binary processing monitoring...');
        await this.refreshData();
        
        this.updateInterval = setInterval(async () => {
            await this.refreshData();
        }, this.refreshRate);
    }

    async refreshData() {
        try {
            // Fetch active workflows
            const workflows = await this.fetchActiveWorkflows();
            
            // Fetch file injection status
            const injections = await this.fetchFileInjections();
            
            // Fetch Celery task status
            const tasks = await this.fetchCeleryTasks();
            
            // Combine and update display
            this.updateBinaryDisplay(workflows, injections, tasks);
            this.updateStats();
            
            // Update last refresh time
            document.getElementById('last-update-time').textContent = new Date().toLocaleTimeString();
            
        } catch (error) {
            console.error('Error refreshing binary processing data:', error);
        }
    }

    async fetchActiveWorkflows() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/workflow/active`);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.warn('Could not fetch workflows:', error);
        }
        return [];
    }

    async fetchFileInjections() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/file-injection?status_filter=in_progress`);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.warn('Could not fetch file injections:', error);
        }
        return [];
    }

    async fetchCeleryTasks() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health/celery/tasks`);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.warn('Could not fetch Celery tasks:', error);
        }
        return { active: [], reserved: [] };
    }

    updateBinaryDisplay(workflows, injections, tasks) {
        const grid = document.getElementById('binary-grid');
        const noBinaries = document.getElementById('no-binaries');
        
        if (!grid) return;

        // Clear existing content
        grid.innerHTML = '';
        
        // Combine all processing items
        const allItems = [
            ...workflows.map(w => ({ ...w, type: 'workflow' })),
            ...injections.map(i => ({ ...i, type: 'injection' })),
            ...this.extractBinaryTasks(tasks)
        ];

        if (allItems.length === 0) {
            grid.style.display = 'none';
            noBinaries.style.display = 'block';
            return;
        }

        grid.style.display = 'grid';
        noBinaries.style.display = 'none';

        // Create cards for each item
        allItems.forEach(item => {
            const card = this.createBinaryCard(item);
            grid.appendChild(card);
        });
    }

    extractBinaryTasks(tasks) {
        const binaryTasks = [];
        
        // Extract relevant tasks from active and reserved
        [...(tasks.active || []), ...(tasks.reserved || [])].forEach(task => {
            if (this.isBinaryRelatedTask(task)) {
                binaryTasks.push({
                    id: task.id,
                    name: this.extractBinaryName(task),
                    type: 'task',
                    status: 'processing',
                    task_name: task.name,
                    started_at: task.time_start || new Date().toISOString()
                });
            }
        });
        
        return binaryTasks;
    }

    isBinaryRelatedTask(task) {
        const binaryTaskPatterns = [
            'get_vm_for_processing',
            'inject_file',
            'execute_command_in_vm',
            'start_vm_monitoring',
            'generate_analysis_report'
        ];
        
        return binaryTaskPatterns.some(pattern => 
            task.name && task.name.includes(pattern)
        );
    }

    extractBinaryName(task) {
        // Try to extract binary name from task args or name
        if (task.args && task.args.length > 0) {
            const firstArg = task.args[0];
            if (typeof firstArg === 'string' && firstArg.includes('binary')) {
                return firstArg;
            }
        }
        
        return task.name || 'Unknown Binary';
    }

    createBinaryCard(item) {
        const card = document.createElement('div');
        card.className = 'binary-card';
        
        const stage = this.stages[item.status] || this.stages['pending'];
        const elapsed = this.calculateElapsed(item.started_at || item.created_at);
        
        card.innerHTML = `
            <div class="binary-card-header">
                <div class="binary-info">
                    <div class="binary-name">${this.getBinaryDisplayName(item)}</div>
                    <div class="binary-type">${item.type}</div>
                </div>
                <div class="binary-status" style="color: ${stage.color}">
                    <span class="status-icon">${stage.icon}</span>
                    <span class="status-text">${stage.label}</span>
                </div>
            </div>
            
            <div class="binary-card-body">
                <div class="progress-info">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${item.progress_percentage || 0}%; background-color: ${stage.color}"></div>
                    </div>
                    <div class="progress-text">${item.progress_percentage || 0}%</div>
                </div>
                
                <div class="binary-details">
                    <div class="detail-item">
                        <span class="detail-label">ID:</span>
                        <span class="detail-value">${item.id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Elapsed:</span>
                        <span class="detail-value">${elapsed}</span>
                    </div>
                    ${item.current_step ? `
                    <div class="detail-item">
                        <span class="detail-label">Step:</span>
                        <span class="detail-value">${item.current_step}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        return card;
    }

    getBinaryDisplayName(item) {
        return item.name || item.filename || item.task_name || 'Unknown Binary';
    }

    calculateElapsed(startTime) {
        if (!startTime) return 'Unknown';
        
        const start = new Date(startTime);
        const now = new Date();
        const elapsed = Math.floor((now - start) / 1000);
        
        if (elapsed < 60) return `${elapsed}s`;
        if (elapsed < 3600) return `${Math.floor(elapsed / 60)}m ${elapsed % 60}s`;
        return `${Math.floor(elapsed / 3600)}h ${Math.floor((elapsed % 3600) / 60)}m`;
    }

    updateStats() {
        const cards = document.querySelectorAll('.binary-card');
        const stats = {
            active: 0,
            pending: 0,
            completed: 0,
            failed: 0
        };

        cards.forEach(card => {
            const statusText = card.querySelector('.status-text')?.textContent?.toLowerCase();
            if (statusText) {
                if (statusText.includes('pending') || statusText.includes('creating')) {
                    stats.pending++;
                } else if (statusText.includes('completed')) {
                    stats.completed++;
                } else if (statusText.includes('failed')) {
                    stats.failed++;
                } else {
                    stats.active++;
                }
            }
        });

        document.getElementById('active-count').textContent = stats.active;
        document.getElementById('pending-count').textContent = stats.pending;
        document.getElementById('completed-count').textContent = stats.completed;
        document.getElementById('failed-count').textContent = stats.failed;
    }

    stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.binaryDashboard = new BinaryProcessingDashboard();
        window.binaryDashboard.initialize();
    }, 1500);
});

// Export for external use
window.BinaryProcessingDashboard = BinaryProcessingDashboard;
