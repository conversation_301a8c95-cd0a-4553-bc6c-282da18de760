<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💩🎉 TurdParty 💩🎉 System Status</title>
    <link rel="stylesheet" href="assets/css/status.css?v=5.0">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        // Initialize Mermaid with dark theme configuration
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            securityLevel: 'loose',
            themeVariables: {
                primaryColor: '#7c4dff',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#651fff',
                lineColor: '#bb86fc',
                secondaryColor: '#2d2d2d',
                tertiaryColor: '#1e1e1e',
                background: '#121212',
                mainBkg: '#1e1e1e',
                secondaryBkg: '#2d2d2d',
                tertiaryBkg: '#333333'
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="status-header">
            <div class="header-content">
                <h1>💩🎉 TurdParty 💩🎉 System Status</h1>
                <p class="subtitle">Binary Analysis Platform - Real-time Service Monitoring</p>
                <div class="overall-status" id="overall-status">
                    <span class="status-indicator operational" id="status-indicator"></span>
                    <span class="status-text" id="status-text">All Systems Operational</span>
                </div>
            </div>
        </header>

        <!-- Tab Navigation -->
        <section class="tab-navigation">
            <div class="tab-container">
                <button class="tab-button active" data-tab="architecture">🏗️ Architecture</button>
                <button class="tab-button" data-tab="services">🚀 Service Status</button>
                <button class="tab-button" data-tab="metrics">📊 Metrics</button>
                <button class="tab-button" data-tab="activity">📈 Activity</button>
            </div>
        </section>

        <!-- Tab Content -->
        <div class="tab-content-container">
            <!-- Architecture Tab -->
            <div class="tab-content active" id="architecture-tab">
                <!-- Mini Service Status Icons -->
                <section class="mini-services-section">
                    <div id="mini-services-grid" class="mini-services-grid">
                        <!-- Mini service cards will be populated by JavaScript -->
                    </div>
                </section>

                <section class="architecture-section">
                    <h2>🏗️ System Architecture</h2>
                    <div class="mermaid-container">
                        <div class="mermaid" id="architecture-diagram">
graph TB
    subgraph "External Interface"
        USER[User/Client]
        TRAEFIK[Traefik Proxy<br/>Authentication & Routing]
    end

    subgraph "API Layer"
        API[FastAPI Service<br/>Port: 8000<br/>File Upload & Management]
        STATUS[Status Dashboard<br/>Port: 8090<br/>System Monitoring]
    end

    subgraph "Storage Layer"
        MINIO[MinIO Object Storage<br/>Port: 9000-9001<br/>File Storage with UUID]
        POSTGRES[PostgreSQL Database<br/>Port: 5432<br/>Metadata & State]
        REDIS[Redis Cache<br/>Port: 6379<br/>Session & Queue Management]
    end

    subgraph "Analysis Layer"
        VAGRANT[Vagrant VMs<br/>Ubuntu/Windows<br/>Isolated Execution]
        DOCKER[Docker Containers<br/>Fallback VMs<br/>Network Isolated]
    end

    subgraph "ELK Stack"
        ELASTICSEARCH[Elasticsearch<br/>Port: 9200<br/>Search & Analytics]
        LOGSTASH[Logstash<br/>Port: 5044, 5001, 8081<br/>Data Processing]
        KIBANA[Kibana<br/>Port: 5601<br/>Visualisation Dashboard]
    end

    USER --> TRAEFIK
    TRAEFIK --> API
    TRAEFIK --> STATUS

    API --> MINIO
    API --> POSTGRES
    API --> REDIS

    STATUS --> API
    STATUS --> MINIO
    STATUS --> POSTGRES
    STATUS --> REDIS

    API --> VAGRANT
    API --> DOCKER

    VAGRANT --> LOGSTASH
    DOCKER --> LOGSTASH
    API --> LOGSTASH

    LOGSTASH --> ELASTICSEARCH
    ELASTICSEARCH --> KIBANA

    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef processing fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef elk fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class USER,TRAEFIK external
    class API,STATUS api
    class MINIO,POSTGRES,REDIS storage
    class VAGRANT,DOCKER processing
    class ELASTICSEARCH,LOGSTASH,KIBANA elk
                    </div>
                </section>
            </div>

            <!-- Services Tab -->
            <div class="tab-content" id="services-tab">
                <section class="services-section">
                    <h2>🚀 Service Status</h2>
                    <div class="services-grid" id="services-grid">
                        <!-- Services will be populated by JavaScript -->
                    </div>
                </section>
            </div>

            <!-- Metrics Tab -->
            <div class="tab-content" id="metrics-tab">
                <!-- Task Metrics Section -->
                <section class="metrics-section">
                    <h2>📊 Task Metrics</h2>
                    <div class="metrics-grid">
                        <div class="metric-card task-metric">
                            <div class="metric-header">
                                <span class="metric-icon">✅</span>
                                <span class="metric-title">Success Rate</span>
                            </div>
                            <div class="metric-value" id="success-rate">0.0%</div>
                            <div class="metric-trend">↗️ +2.3% from yesterday</div>
                        </div>
                        <div class="metric-card task-metric">
                            <div class="metric-header">
                                <span class="metric-icon">📋</span>
                                <span class="metric-title">Total Tasks</span>
                            </div>
                            <div class="metric-value" id="total-tasks">0</div>
                            <div class="metric-trend">📈 Last 24 hours</div>
                        </div>
                        <div class="metric-card task-metric">
                            <div class="metric-header">
                                <span class="metric-icon">❌</span>
                                <span class="metric-title">Failed Tasks</span>
                            </div>
                            <div class="metric-value" id="failed-tasks">0</div>
                            <div class="metric-trend">🔽 -15% improvement</div>
                        </div>
                        <div class="metric-card task-metric">
                            <div class="metric-header">
                                <span class="metric-icon">👷</span>
                                <span class="metric-title">Active Workers</span>
                            </div>
                            <div class="metric-value" id="active-workers">0</div>
                            <div class="metric-trend">⚡ Real-time count</div>
                        </div>
                    </div>
                </section>

                <!-- Performance Metrics Section -->
                <section class="metrics-section">
                    <h2>⚡ Performance Metrics</h2>
                    <div class="metrics-grid">
                        <div class="metric-card performance-metric">
                            <div class="metric-header">
                                <span class="metric-icon">🚀</span>
                                <span class="metric-title">Throughput</span>
                            </div>
                            <div class="metric-value" id="throughput">1.35M ops/s</div>
                            <div class="metric-trend">🔥 Model Operations</div>
                        </div>
                        <div class="metric-card performance-metric">
                            <div class="metric-header">
                                <span class="metric-icon">⏱️</span>
                                <span class="metric-title">VM Boot Time</span>
                            </div>
                            <div class="metric-value" id="vm-boot">14s</div>
                            <div class="metric-trend">🐳 Container Startup</div>
                        </div>
                        <div class="metric-card performance-metric">
                            <div class="metric-header">
                                <span class="metric-icon">📁</span>
                                <span class="metric-title">File Operations</span>
                            </div>
                            <div class="metric-value" id="file-ops">0.7s</div>
                            <div class="metric-trend">⬆️⬇️ Upload/Download</div>
                        </div>
                        <div class="metric-card performance-metric">
                            <div class="metric-header">
                                <span class="metric-icon">🧪</span>
                                <span class="metric-title">Test Success</span>
                            </div>
                            <div class="metric-value" id="test-success">63/63</div>
                            <div class="metric-trend">✨ Tests Passing</div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Activity Tab -->
            <div class="tab-content" id="activity-tab">
                <section class="activity-section">
                    <h2>📈 Recent Activity</h2>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <span class="stat-icon">🔄</span>
                            <span class="stat-value" id="activity-count">0</span>
                            <span class="stat-label">Events Today</span>
                        </div>
                        <div class="activity-stat">
                            <span class="stat-icon">✅</span>
                            <span class="stat-value" id="success-count">0</span>
                            <span class="stat-label">Successful</span>
                        </div>
                        <div class="activity-stat">
                            <span class="stat-icon">⚠️</span>
                            <span class="stat-value" id="warning-count">0</span>
                            <span class="stat-label">Warnings</span>
                        </div>
                        <div class="activity-stat">
                            <span class="stat-icon">❌</span>
                            <span class="stat-value" id="error-count">0</span>
                            <span class="stat-label">Errors</span>
                        </div>
                    </div>
                    <div class="activity-feed" id="activity-feed">
                        <!-- Activity will be populated by JavaScript -->
                    </div>
                </section>
            </div>
        </div>

        <!-- Footer -->
        <footer class="status-footer">
            <div class="footer-content">
                <p>&copy; 2024 💩🎉 TurdParty 💩🎉 Binary Analysis Platform</p>
                <div class="footer-links">
                    <a href="/service">Service View</a>
                    <a href="/docs">Documentation</a>
                    <a href="/api/v1/health">API Health</a>
                    <a href="http://kibana.turdparty.localhost">Kibana Dashboard</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="assets/js/error-logger.js?v=1.0"></script>
    <script src="assets/js/status.js?v=5.0"></script>
    <script src="assets/js/celery-integration.js?v=5.0"></script>
    <script src="assets/js/binary-processing-dashboard.js?v=1.0"></script>
    <link rel="stylesheet" href="assets/css/binary-processing.css?v=1.0">
</body>
</html>
