#!/usr/bin/env python3
"""
Create Simple Workflow Records
Create workflow records using the existing database schema.
"""

import json
import logging
import sys
import time
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_database_connection():
    """Get database connection."""
    return psycopg2.connect(
        host="localhost",
        port=5432,
        database="turdparty",
        user="postgres",
        password="postgres",
        cursor_factory=RealDictCursor
    )


def create_simple_workflow_records() -> List[Dict[str, Any]]:
    """Create simple workflow records for testing."""
    logger.info("🔄 Creating simple workflow records...")
    
    created_workflows = []
    
    with get_database_connection() as conn:
        with conn.cursor() as cursor:
            # Get some existing files
            cursor.execute("""
                SELECT id, filename, file_size, file_hash 
                FROM file_uploads 
                WHERE status IN ('STORED', 'PROCESSING')
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            files = cursor.fetchall()
            logger.info(f"   📊 Found {len(files)} files to create workflows for")
            
            for file_record in files:
                try:
                    file_id = file_record["id"]
                    filename = file_record["filename"]
                    
                    # Check if workflow already exists
                    cursor.execute(
                        "SELECT id FROM workflow_jobs WHERE file_upload_id = %s",
                        (file_id,)
                    )
                    existing_workflow = cursor.fetchone()
                    
                    if existing_workflow:
                        logger.info(f"   📁 Workflow already exists for {filename}")
                        created_workflows.append({
                            "filename": filename,
                            "workflow_id": str(existing_workflow["id"]),
                            "file_id": str(file_id),
                            "status": "existing"
                        })
                        continue
                    
                    # Create new workflow record with correct schema
                    workflow_id = str(uuid.uuid4())
                    now = datetime.now()

                    cursor.execute("""
                        INSERT INTO workflow_jobs (
                            id, name, description, status, current_step,
                            progress_percentage, file_upload_id, vm_config,
                            injection_config, results, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        workflow_id,
                        f"Analysis of {filename}",
                        f"Automated analysis workflow for {filename}",
                        "COMPLETED",  # Use enum value
                        "completed",
                        "100",
                        file_id,
                        json.dumps({"template": "ubuntu/focal64", "memory_mb": 2048, "cpus": 2}),
                        json.dumps({"path": f"/tmp/analysis/{filename}"}),
                        json.dumps({
                            "file_processed": True,
                            "vm_executed": True,
                            "data_collected": True,
                            "analysis_complete": True
                        }),
                        now,
                        now
                    ))
                    
                    created_workflows.append({
                        "filename": filename,
                        "workflow_id": workflow_id,
                        "file_id": str(file_id),
                        "file_hash": file_record["file_hash"],
                        "status": "created"
                    })
                    
                    logger.info(f"   ✅ Created workflow for {filename}: {workflow_id}")
                    
                except Exception as e:
                    logger.error(f"   ❌ Failed to create workflow for {file_record.get('filename', 'unknown')}: {e}")
            
            conn.commit()
    
    logger.info(f"📊 Created {len(created_workflows)} workflow records")
    return created_workflows


def test_direct_report_generation(workflows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Test direct report generation."""
    logger.info("🧪 Testing direct report generation...")
    
    # Add workers path to sys.path
    sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
    
    # Set environment variables
    import os
    os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/turdparty"
    
    test_results = []
    
    try:
        from tasks.report_generation import generate_analysis_report
        
        # Test with first 3 workflows
        for workflow in workflows[:3]:
            try:
                workflow_id = workflow["workflow_id"]
                filename = workflow["filename"]
                
                logger.info(f"   🔄 Testing report generation for {filename}")
                
                # Call the function directly (not as Celery task)
                # We need to call it as a regular function, not a Celery task
                result = generate_analysis_report.__wrapped__(None, workflow_id)
                
                test_results.append({
                    "filename": filename,
                    "workflow_id": workflow_id,
                    "result": result,
                    "success": result.get("success", False)
                })
                
                if result.get("success"):
                    logger.info(f"   ✅ Report generated for {filename}")
                    if result.get("report_url"):
                        logger.info(f"      📄 Report URL: {result['report_url']}")
                else:
                    logger.info(f"   ⚠️ Report generation failed for {filename}: {result.get('error', 'unknown')}")
                
            except Exception as e:
                logger.error(f"   ❌ Direct test failed for {workflow['filename']}: {e}")
                test_results.append({
                    "filename": workflow["filename"],
                    "workflow_id": workflow["workflow_id"],
                    "error": str(e),
                    "success": False
                })
        
        logger.info(f"📊 Tested {len(test_results)} direct report generations")
        return test_results
        
    except Exception as e:
        logger.error(f"❌ Failed to test direct report generation: {e}")
        return []


def queue_reports_via_celery(workflows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Queue reports via Celery."""
    logger.info("📋 Queuing reports via Celery...")
    
    # Add workers path to sys.path
    sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
    
    # Import Celery app with correct configuration
    import os
    os.environ["REDIS_HOST"] = "localhost"  # Use localhost since we're outside container
    os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/turdparty"
    
    try:
        from celery_app import app
        
        queued_reports = []
        
        # Queue reports for first 3 workflows
        for workflow in workflows[:3]:
            try:
                workflow_id = workflow["workflow_id"]
                filename = workflow["filename"]
                
                # Queue the report generation task
                result = app.send_task(
                    "services.workers.tasks.report_generation.generate_analysis_report",
                    args=[workflow_id],
                    queue='reports'
                )
                
                queued_reports.append({
                    "filename": filename,
                    "workflow_id": workflow_id,
                    "task_id": result.id,
                    "file_id": workflow["file_id"]
                })
                
                logger.info(f"   ✅ Queued report for {filename}: {result.id}")
                
            except Exception as e:
                logger.error(f"   ❌ Failed to queue report for {workflow['filename']}: {e}")
        
        logger.info(f"📊 Queued {len(queued_reports)} reports")
        return queued_reports
        
    except Exception as e:
        logger.error(f"❌ Failed to queue reports via Celery: {e}")
        return []


def main():
    """Main execution."""
    logger.info("🚀 💩🎉TurdParty🎉💩 SIMPLE WORKFLOW CREATION")
    logger.info("Creating simple workflow records for report generation testing")
    logger.info("=" * 80)
    
    try:
        # Step 1: Create workflow records
        workflows = create_simple_workflow_records()
        
        if not workflows:
            logger.warning("No workflows created")
            return False
        
        # Step 2: Test direct report generation
        test_results = test_direct_report_generation(workflows)
        
        # Step 3: Try to queue reports via Celery
        queued_reports = queue_reports_via_celery(workflows)
        
        # Step 4: Save results
        results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "workflows_created": len([w for w in workflows if w["status"] == "created"]),
            "workflows_existing": len([w for w in workflows if w["status"] == "existing"]),
            "reports_tested": len(test_results),
            "reports_queued": len(queued_reports),
            "workflows": workflows,
            "test_results": test_results,
            "queued_reports": queued_reports
        }
        
        results_file = f"/tmp/simple-workflow-creation-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n📄 Results saved: {results_file}")
        
        # Summary
        logger.info("\n🎉 SIMPLE WORKFLOW CREATION COMPLETE!")
        logger.info("=" * 60)
        logger.info(f"✅ Workflows created: {len([w for w in workflows if w['status'] == 'created'])}")
        logger.info(f"✅ Workflows existing: {len([w for w in workflows if w['status'] == 'existing'])}")
        logger.info(f"✅ Reports tested: {len(test_results)}")
        logger.info(f"✅ Reports queued: {len(queued_reports)}")
        
        successful_tests = len([r for r in test_results if r.get("success")])
        logger.info(f"📊 Successful report tests: {successful_tests}/{len(test_results)}")
        
        if successful_tests > 0:
            logger.info("\n🎉 REPORT GENERATION IS WORKING!")
            logger.info("Real reports are being generated from workflow data!")
        elif len(test_results) > 0:
            logger.info("\n⚠️ Report generation tested but needs ECS data")
            logger.info("Infrastructure is working, but needs actual VM execution data")
        else:
            logger.info("\n⚠️ Report generation needs attention")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Simple workflow creation failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
