#!/usr/bin/env python3
"""
Test Sphinx Report Generation Directly
Test the SphinxReportGenerator with real workflow data.
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_database_connection():
    """Get database connection."""
    return psycopg2.connect(
        host="localhost",
        port=5432,
        database="turdparty",
        user="postgres",
        password="postgres",
        cursor_factory=RealDictCursor
    )


def get_workflow_data() -> List[Dict[str, Any]]:
    """Get workflow data from database."""
    logger.info("📄 Getting workflow data from database...")
    
    with get_database_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    wj.id as workflow_id,
                    wj.name,
                    wj.status,
                    fu.id as file_id,
                    fu.filename,
                    fu.file_hash,
                    fu.file_size,
                    wj.created_at
                FROM workflow_jobs wj
                JOIN file_uploads fu ON wj.file_upload_id = fu.id
                WHERE wj.status = 'COMPLETED'
                ORDER BY wj.created_at DESC
                LIMIT 5
            """)
            
            workflows = cursor.fetchall()
            
    logger.info(f"   📊 Found {len(workflows)} completed workflows")
    return [dict(workflow) for workflow in workflows]


async def test_sphinx_report_generation():
    """Test Sphinx report generation."""
    logger.info("🔥 TESTING SPHINX REPORT GENERATION")
    logger.info("=" * 80)
    
    try:
        # Get workflow data
        workflows = get_workflow_data()
        
        if not workflows:
            logger.warning("No completed workflows found")
            return False
        
        # Add project root to path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from services.report_generator import SphinxReportGenerator
        
        # Test with first workflow
        workflow = workflows[0]
        file_id = workflow["file_id"]
        filename = workflow["filename"]
        
        logger.info(f"📋 Testing report generation for: {filename}")
        logger.info(f"   🆔 File ID: {file_id}")
        logger.info(f"   📁 Workflow: {workflow['workflow_id']}")
        
        # Initialize report generator
        generator = SphinxReportGenerator()
        
        try:
            # Generate report
            logger.info("🔄 Starting report generation...")
            result = await generator.generate_report_from_uuid(str(file_id))
            
            if result.get("success"):
                logger.info("✅ REPORT GENERATION SUCCESSFUL!")
                logger.info(f"   📄 RST File: {result.get('rst_file')}")
                logger.info(f"   🌐 HTML Path: {result.get('html_path')}")
                logger.info(f"   🔗 Report URL: {result.get('report_url')}")
                
                # Save result
                results_file = f"/tmp/sphinx-report-test-{int(time.time())}.json"
                with open(results_file, 'w') as f:
                    json.dump(result, f, indent=2, default=str)
                
                logger.info(f"   💾 Results saved: {results_file}")
                
                return True
                
            else:
                logger.error("❌ REPORT GENERATION FAILED!")
                logger.error(f"   📝 Error: {result.get('error')}")
                return False
                
        finally:
            await generator.cleanup()
            
    except Exception as e:
        logger.error(f"❌ Sphinx report test failed: {e}")
        return False


async def test_mock_report_generation():
    """Test report generation with mock data."""
    logger.info("\n🧪 TESTING MOCK REPORT GENERATION")
    logger.info("=" * 80)
    
    try:
        # Add project root to path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from services.report_generator import SphinxReportGenerator
        
        # Create mock report generator that doesn't depend on external APIs
        class MockSphinxReportGenerator(SphinxReportGenerator):
            async def _fetch_report_data(self, file_uuid: str):
                """Mock report data."""
                return {
                    "metadata": {
                        "report_id": f"rpt_{file_uuid}",
                        "file_uuid": file_uuid,
                        "generated_at": datetime.utcnow().isoformat(),
                        "report_version": "1.0"
                    },
                    "file_info": {
                        "filename": "test_upload.txt",
                        "file_size": 1024,
                        "file_hash": "abc123"
                    },
                    "security_analysis": {
                        "threat_indicators": {
                            "risk_level": "low"
                        }
                    }
                }
            
            async def _fetch_ecs_data(self, file_uuid: str):
                """Mock ECS data."""
                return [
                    {
                        "@timestamp": "2025-06-13T07:00:00Z",
                        "event": {"action": "file_created"},
                        "file": {"path": "/tmp/test.txt", "size": 1024},
                        "process": {"name": "test.exe", "pid": 1234}
                    },
                    {
                        "@timestamp": "2025-06-13T07:01:00Z",
                        "event": {"action": "process_start"},
                        "process": {"name": "notepad.exe", "pid": 5678, "command_line": "notepad.exe test.txt"}
                    }
                ]
        
        # Test with mock data
        generator = MockSphinxReportGenerator()
        
        try:
            logger.info("🔄 Testing with mock data...")
            result = await generator.generate_report_from_uuid("test-uuid-12345")
            
            if result.get("success"):
                logger.info("✅ MOCK REPORT GENERATION SUCCESSFUL!")
                logger.info(f"   📄 RST File: {result.get('rst_file')}")
                logger.info(f"   🌐 HTML Path: {result.get('html_path')}")
                
                return True
            else:
                logger.error("❌ MOCK REPORT GENERATION FAILED!")
                logger.error(f"   📝 Error: {result.get('error')}")
                return False
                
        finally:
            await generator.cleanup()
            
    except Exception as e:
        logger.error(f"❌ Mock report test failed: {e}")
        return False


async def test_report_infrastructure():
    """Test report generation infrastructure."""
    logger.info("\n🔧 TESTING REPORT INFRASTRUCTURE")
    logger.info("=" * 80)
    
    try:
        # Test 1: Check if Sphinx is available
        logger.info("📋 TEST 1: Checking Sphinx availability")
        try:
            import sphinx
            logger.info(f"   ✅ Sphinx available: version {sphinx.__version__}")
        except ImportError:
            logger.error("   ❌ Sphinx not available")
            return False
        
        # Test 2: Check if Jinja2 is available
        logger.info("📋 TEST 2: Checking Jinja2 availability")
        try:
            import jinja2
            logger.info(f"   ✅ Jinja2 available: version {jinja2.__version__}")
        except ImportError:
            logger.error("   ❌ Jinja2 not available")
            return False
        
        # Test 3: Check if httpx is available
        logger.info("📋 TEST 3: Checking httpx availability")
        try:
            import httpx
            logger.info(f"   ✅ httpx available")
        except ImportError:
            logger.error("   ❌ httpx not available")
            return False
        
        # Test 4: Check if elasticsearch is available
        logger.info("📋 TEST 4: Checking Elasticsearch client availability")
        try:
            from elasticsearch import AsyncElasticsearch
            logger.info(f"   ✅ Elasticsearch client available")
        except ImportError:
            logger.error("   ❌ Elasticsearch client not available")
            return False
        
        # Test 5: Check report directories
        logger.info("📋 TEST 5: Checking report directories")
        try:
            reports_dir = Path("docs/reports")
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            templates_dir = reports_dir / "_templates"
            templates_dir.mkdir(exist_ok=True)
            
            reports_output_dir = reports_dir / "reports"
            reports_output_dir.mkdir(exist_ok=True)
            
            logger.info(f"   ✅ Report directories created: {reports_dir}")
        except Exception as e:
            logger.error(f"   ❌ Failed to create report directories: {e}")
            return False
        
        logger.info("✅ ALL INFRASTRUCTURE TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Infrastructure test failed: {e}")
        return False


async def main():
    """Main test execution."""
    logger.info("🚀 💩🎉TurdParty🎉💩 SPHINX REPORT GENERATION TESTING")
    logger.info("Testing Sphinx report generation with real and mock data")
    logger.info("=" * 100)
    
    # Run tests
    infrastructure_success = await test_report_infrastructure()
    mock_success = await test_mock_report_generation()
    real_success = await test_sphinx_report_generation()
    
    # Summary
    logger.info("\n📊 SPHINX REPORT GENERATION TEST SUMMARY")
    logger.info("=" * 70)
    logger.info(f"Infrastructure Test:    {'✅ PASSED' if infrastructure_success else '❌ FAILED'}")
    logger.info(f"Mock Report Test:       {'✅ PASSED' if mock_success else '❌ FAILED'}")
    logger.info(f"Real Report Test:       {'✅ PASSED' if real_success else '❌ FAILED'}")
    
    overall_success = infrastructure_success and (mock_success or real_success)
    logger.info(f"Overall Result:         {'✅ TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n🎉 SPHINX REPORT GENERATION IS WORKING!")
        logger.info("Reports can be generated from workflow data!")
    else:
        logger.info("\n⚠️ SPHINX REPORT GENERATION NEEDS ATTENTION")
        logger.info("Some components need to be fixed.")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
