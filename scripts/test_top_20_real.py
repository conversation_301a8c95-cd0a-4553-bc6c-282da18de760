#!/usr/bin/env python3
"""
Real Top 20 Test Script for TurdParty.

Tests the top 20 most important components with real implementations,
no mocks. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any
import uuid

import docker
import redis
from elasticsearch import AsyncElasticsearch
from minio import Minio

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.api.src.services.elk_logger import ELKLogger
from services.api.src.services.file_injection_service import FileInjectionService
from services.api.src.models.file_injection import FileInjectionCreate, InjectionStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealTop20Tester:
    """Real implementation tester for top 20 TurdParty components."""

    def __init__(self) -> None:
        """Initialize the real tester."""
        self.results: Dict[str, Any] = {}
        self.start_time = datetime.now(timezone.utc)
        
        # Service clients (will be initialized)
        self.docker_client: docker.DockerClient = None
        self.redis_client: redis.Redis = None
        self.es_client: AsyncElasticsearch = None
        self.minio_client: Minio = None
        self.elk_logger: ELKLogger = None
        self.file_injection_service: FileInjectionService = None

    async def initialize_services(self) -> bool:
        """Initialize all real service connections."""
        logger.info("🔧 Initializing real service connections...")
        
        try:
            # Docker
            self.docker_client = docker.from_env()
            self.docker_client.ping()
            logger.info("✅ Docker connection established")
            
            # Redis
            self.redis_client = redis.Redis(
                host="redis.turdparty.localhost",
                port=6379,
                decode_responses=True,
                socket_timeout=5
            )
            self.redis_client.ping()
            logger.info("✅ Redis connection established")
            
            # Elasticsearch
            self.es_client = AsyncElasticsearch(
                hosts=[{"host": "elasticsearch.turdparty.localhost", "port": 9200}],
                timeout=10
            )
            await self.es_client.info()
            logger.info("✅ Elasticsearch connection established")
            
            # MinIO
            self.minio_client = Minio(
                "minio.turdparty.localhost:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            list(self.minio_client.list_buckets())
            logger.info("✅ MinIO connection established")
            
            # Initialize services
            self.elk_logger = ELKLogger(es_client=self.es_client)
            self.file_injection_service = FileInjectionService(
                elk_logger=self.elk_logger,
                minio_client=self.minio_client,
                redis_client=self.redis_client
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Service initialization failed: {e}")
            return False

    async def test_01_docker_container_lifecycle(self) -> bool:
        """Test 1: Real Docker container lifecycle."""
        logger.info("🧪 Test 1: Docker container lifecycle")
        
        try:
            container_name = f"test-lifecycle-{uuid.uuid4().hex[:8]}"
            
            # Create container
            container = self.docker_client.containers.run(
                "alpine:latest",
                command="sleep 60",
                name=container_name,
                detach=True,
                remove=False
            )
            
            # Verify running
            container.reload()
            assert container.status == "running"
            
            # Stop container
            container.stop(timeout=5)
            container.reload()
            assert container.status == "exited"
            
            # Remove container
            container.remove()
            
            self.results["test_01"] = {"status": "PASS", "details": "Container lifecycle successful"}
            logger.info("✅ Test 1: PASS")
            return True
            
        except Exception as e:
            self.results["test_01"] = {"status": "FAIL", "error": str(e)}
            logger.error(f"❌ Test 1: FAIL - {e}")
            return False

    async def test_02_elasticsearch_indexing(self) -> bool:
        """Test 2: Real Elasticsearch indexing."""
        logger.info("🧪 Test 2: Elasticsearch indexing")
        
        try:
            test_uuid = str(uuid.uuid4())
            
            # Log event
            await self.elk_logger.log_system_event(
                file_uuid=test_uuid,
                event_type="test_event",
                process_name="test_process.exe",
                details={"test": "real_indexing"}
            )
            
            # Wait for indexing
            await asyncio.sleep(2)
            
            # Verify indexed
            index_pattern = f"turdparty-system-events-{datetime.now().strftime('%Y.%m')}"
            search_result = await self.es_client.search(
                index=index_pattern,
                body={"query": {"term": {"file_uuid.keyword": test_uuid}}}
            )
            
            assert search_result["hits"]["total"]["value"] >= 1
            
            self.results["test_02"] = {"status": "PASS", "details": "Elasticsearch indexing successful"}
            logger.info("✅ Test 2: PASS")
            return True
            
        except Exception as e:
            self.results["test_02"] = {"status": "FAIL", "error": str(e)}
            logger.error(f"❌ Test 2: FAIL - {e}")
            return False

    async def test_03_redis_caching(self) -> bool:
        """Test 3: Real Redis caching."""
        logger.info("🧪 Test 3: Redis caching")
        
        try:
            test_key = f"test_cache_{uuid.uuid4().hex[:8]}"
            test_value = "real_cache_test"
            
            # Set value
            self.redis_client.set(test_key, test_value, ex=60)
            
            # Get value
            cached_value = self.redis_client.get(test_key)
            assert cached_value == test_value
            
            # Cleanup
            self.redis_client.delete(test_key)
            
            self.results["test_03"] = {"status": "PASS", "details": "Redis caching successful"}
            logger.info("✅ Test 3: PASS")
            return True
            
        except Exception as e:
            self.results["test_03"] = {"status": "FAIL", "error": str(e)}
            logger.error(f"❌ Test 3: FAIL - {e}")
            return False

    async def test_04_minio_file_operations(self) -> bool:
        """Test 4: Real MinIO file operations."""
        logger.info("🧪 Test 4: MinIO file operations")
        
        try:
            bucket_name = f"test-bucket-{uuid.uuid4().hex[:8]}"
            object_name = "test_file.txt"
            test_content = b"Real MinIO test content"
            
            # Create bucket
            self.minio_client.make_bucket(bucket_name)
            
            # Upload object
            from io import BytesIO
            self.minio_client.put_object(
                bucket_name, object_name, BytesIO(test_content), len(test_content)
            )
            
            # Download object
            response = self.minio_client.get_object(bucket_name, object_name)
            downloaded_content = response.read()
            assert downloaded_content == test_content
            
            # Cleanup
            self.minio_client.remove_object(bucket_name, object_name)
            self.minio_client.remove_bucket(bucket_name)
            
            self.results["test_04"] = {"status": "PASS", "details": "MinIO operations successful"}
            logger.info("✅ Test 4: PASS")
            return True
            
        except Exception as e:
            self.results["test_04"] = {"status": "FAIL", "error": str(e)}
            logger.error(f"❌ Test 4: FAIL - {e}")
            return False

    async def test_05_file_injection_workflow(self) -> bool:
        """Test 5: Real file injection workflow."""
        logger.info("🧪 Test 5: File injection workflow")
        
        try:
            # Create test VM
            container_name = f"test-injection-{uuid.uuid4().hex[:8]}"
            container = self.docker_client.containers.run(
                "alpine:latest",
                command="sleep 120",
                name=container_name,
                detach=True,
                remove=False
            )
            
            try:
                # Create injection
                injection_data = FileInjectionCreate(
                    file_uuid=str(uuid.uuid4()),
                    vm_id=container.id,
                    injection_path="/tmp/test_injection.exe",
                    permissions="0755"
                )
                
                injection_response = await self.file_injection_service.create_injection(injection_data)
                assert injection_response.status == InjectionStatus.QUEUED
                
                self.results["test_05"] = {"status": "PASS", "details": "File injection workflow successful"}
                logger.info("✅ Test 5: PASS")
                return True
                
            finally:
                # Cleanup container
                container.stop(timeout=5)
                container.remove()
            
        except Exception as e:
            self.results["test_05"] = {"status": "FAIL", "error": str(e)}
            logger.error(f"❌ Test 5: FAIL - {e}")
            return False

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all top 20 tests."""
        logger.info("🚀 Starting Real Top 20 Tests")
        
        if not await self.initialize_services():
            return {"error": "Service initialization failed"}
        
        tests = [
            self.test_01_docker_container_lifecycle,
            self.test_02_elasticsearch_indexing,
            self.test_03_redis_caching,
            self.test_04_minio_file_operations,
            self.test_05_file_injection_workflow,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if await test():
                passed += 1
        
        # Calculate results
        end_time = datetime.now(timezone.utc)
        duration = (end_time - self.start_time).total_seconds()
        
        summary = {
            "total_tests": total,
            "passed": passed,
            "failed": total - passed,
            "pass_rate": f"{(passed/total)*100:.1f}%",
            "duration_seconds": duration,
            "start_time": self.start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "results": self.results
        }
        
        return summary

    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.es_client:
            await self.es_client.close()
        if self.redis_client:
            self.redis_client.close()
        if self.docker_client:
            self.docker_client.close()


async def main() -> None:
    """Main entry point."""
    tester = RealTop20Tester()
    
    try:
        results = await tester.run_all_tests()
        
        # Print results
        print("\n" + "="*60)
        print("🎯 REAL TOP 20 TEST RESULTS")
        print("="*60)
        print(f"📊 Tests: {results['passed']}/{results['total_tests']} PASSED ({results['pass_rate']})")
        print(f"⏱️  Duration: {results['duration_seconds']:.2f} seconds")
        print(f"🕐 Started: {results['start_time']}")
        print(f"🕐 Ended: {results['end_time']}")
        
        print("\n📋 Individual Test Results:")
        for test_id, result in results['results'].items():
            status_emoji = "✅" if result['status'] == 'PASS' else "❌"
            print(f"  {status_emoji} {test_id}: {result['status']}")
            if result['status'] == 'FAIL':
                print(f"     Error: {result.get('error', 'Unknown error')}")
        
        # Exit with appropriate code
        if results['passed'] == results['total_tests']:
            print("\n🎉 ALL TESTS PASSED!")
            sys.exit(0)
        else:
            print(f"\n💥 {results['failed']} TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
