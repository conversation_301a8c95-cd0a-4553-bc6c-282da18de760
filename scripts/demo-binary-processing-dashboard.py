#!/usr/bin/env python3
"""
Demo script to populate the Binary Processing Dashboard with sample data.
This creates mock workflow jobs and file injections to demonstrate the dashboard functionality.
"""

import asyncio
import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.api.src.services.file_injection_service import FileInjectionService
from services.api.src.models.file_injection import FileInjectionCreate, InjectionStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BinaryProcessingDashboardDemo:
    """Demo class to populate the binary processing dashboard with sample data."""
    
    def __init__(self):
        self.file_injection_service = FileInjectionService()
        
        # Sample binary data for demonstration
        self.sample_binaries = [
            {
                "filename": "notepad++_installer.exe",
                "target_path": "C:\\temp\\notepad++_installer.exe",
                "status": "in_progress",
                "description": "Notepad++ text editor installation"
            },
            {
                "filename": "7zip_installer.exe", 
                "target_path": "C:\\temp\\7zip_installer.exe",
                "status": "pending",
                "description": "7-Zip compression utility installation"
            },
            {
                "filename": "vlc_media_player.exe",
                "target_path": "C:\\temp\\vlc_media_player.exe", 
                "status": "in_progress",
                "description": "VLC Media Player installation"
            },
            {
                "filename": "firefox_installer.exe",
                "target_path": "C:\\temp\\firefox_installer.exe",
                "status": "pending", 
                "description": "Mozilla Firefox browser installation"
            },
            {
                "filename": "putty_installer.exe",
                "target_path": "C:\\temp\\putty_installer.exe",
                "status": "completed",
                "description": "PuTTY SSH client installation"
            }
        ]
    
    async def create_demo_data(self):
        """Create demo file injections and processing data."""
        logger.info("🎬 Creating demo data for Binary Processing Dashboard")
        
        created_injections = []
        
        for i, binary in enumerate(self.sample_binaries):
            try:
                logger.info(f"📁 Creating injection for {binary['filename']}")
                
                # Create file injection data
                injection_data = FileInjectionCreate(
                    filename=binary["filename"],
                    target_path=binary["target_path"],
                    permissions="0755",
                    description=binary["description"]
                )
                
                # Create mock file content
                file_content = f"Mock binary content for {binary['filename']}".encode('utf-8')
                
                # Create the injection
                injection = await self.file_injection_service.create_injection(
                    injection_data, file_content
                )
                
                # Update status based on demo scenario
                if binary["status"] == "in_progress":
                    # Simulate processing
                    injection_dict = self.file_injection_service._injections[injection.id]
                    injection_dict["status"] = InjectionStatus.IN_PROGRESS
                    injection_dict["message"] = f"Processing {binary['filename']} in VM"
                    injection_dict["updated_at"] = datetime.now(timezone.utc)
                    
                elif binary["status"] == "completed":
                    # Simulate completion
                    injection_dict = self.file_injection_service._injections[injection.id]
                    injection_dict["status"] = InjectionStatus.COMPLETED
                    injection_dict["message"] = f"Successfully processed {binary['filename']}"
                    injection_dict["updated_at"] = datetime.now(timezone.utc)
                
                created_injections.append(injection)
                logger.info(f"   ✅ Created injection {injection.id} for {binary['filename']}")
                
                # Add small delay for realistic timing
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"   ❌ Failed to create injection for {binary['filename']}: {e}")
        
        logger.info(f"🎉 Created {len(created_injections)} demo injections")
        return created_injections
    
    async def simulate_processing_updates(self, duration_minutes: int = 5):
        """Simulate ongoing processing updates for demo purposes."""
        logger.info(f"🔄 Starting processing simulation for {duration_minutes} minutes")
        
        end_time = time.time() + (duration_minutes * 60)
        update_interval = 10  # Update every 10 seconds
        
        while time.time() < end_time:
            try:
                # Update in-progress injections
                for injection_id, injection_data in self.file_injection_service._injections.items():
                    if injection_data["status"] == InjectionStatus.IN_PROGRESS:
                        # Simulate progress updates
                        current_time = datetime.now(timezone.utc)
                        
                        # Cycle through different processing steps
                        steps = [
                            "Allocating VM resources",
                            "Starting Windows VM",
                            "Injecting binary file",
                            "Executing installation",
                            "Monitoring runtime behavior",
                            "Capturing system changes",
                            "Streaming data to ELK"
                        ]
                        
                        # Use time to cycle through steps
                        step_index = int(time.time() / 15) % len(steps)
                        current_step = steps[step_index]
                        
                        injection_data["message"] = current_step
                        injection_data["updated_at"] = current_time
                        injection_data["details"] = {
                            "current_step": current_step,
                            "step_number": step_index + 1,
                            "total_steps": len(steps),
                            "vm_id": f"turdparty-vm-{injection_id[:8]}",
                            "progress_percentage": min(95, (step_index + 1) * 12)
                        }
                
                logger.info("🔄 Updated processing status for active injections")
                await asyncio.sleep(update_interval)
                
            except Exception as e:
                logger.error(f"Error updating processing simulation: {e}")
                await asyncio.sleep(update_interval)
        
        logger.info("✅ Processing simulation completed")
    
    async def cleanup_demo_data(self):
        """Clean up demo data."""
        logger.info("🧹 Cleaning up demo data")
        
        try:
            # Clear all injections
            self.file_injection_service._injections.clear()
            logger.info("   ✅ Cleared all demo injections")
            
        except Exception as e:
            logger.error(f"   ❌ Error cleaning up demo data: {e}")
    
    async def run_demo(self, duration_minutes: int = 5):
        """Run the complete demo."""
        logger.info("🚀 Starting Binary Processing Dashboard Demo")
        logger.info("=" * 60)
        
        try:
            # Create demo data
            await self.create_demo_data()
            
            logger.info("")
            logger.info("📊 Dashboard should now show active binary processing!")
            logger.info("🌐 Open http://localhost:8090 and click the 'Binary Processing' tab")
            logger.info("")
            logger.info(f"⏱️ Demo will run for {duration_minutes} minutes with live updates...")
            
            # Simulate processing updates
            await self.simulate_processing_updates(duration_minutes)
            
            logger.info("")
            logger.info("🎬 Demo completed! You can:")
            logger.info("   1. Keep the data for continued testing")
            logger.info("   2. Run cleanup to remove demo data")
            
        except Exception as e:
            logger.error(f"❌ Demo failed: {e}")
            raise


async def main():
    """Main entry point for the demo."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Binary Processing Dashboard Demo")
    parser.add_argument(
        "--duration", 
        type=int, 
        default=5, 
        help="Demo duration in minutes (default: 5)"
    )
    parser.add_argument(
        "--cleanup", 
        action="store_true", 
        help="Clean up demo data and exit"
    )
    
    args = parser.parse_args()
    
    demo = BinaryProcessingDashboardDemo()
    
    if args.cleanup:
        await demo.cleanup_demo_data()
        logger.info("✅ Demo cleanup completed")
        return
    
    try:
        await demo.run_demo(args.duration)
    except KeyboardInterrupt:
        logger.info("\n🛑 Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
