#!/usr/bin/env python3
"""
Test Report Generation Integration
Verify that report generation is properly queued after workflow completion.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_report_generation_integration():
    """Test the complete report generation integration."""
    logger.info("🧪 TESTING REPORT GENERATION INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Test 1: Import and verify report generation task
        logger.info("📋 TEST 1: Verifying report generation task import")
        from services.workers.tasks.report_generation import queue_report_generation, generate_analysis_report
        logger.info("   ✅ Report generation tasks imported successfully")
        
        # Test 2: Test Celery task registration
        logger.info("📋 TEST 2: Checking Celery task registration")
        from services.workers.celery_app import app
        
        registered_tasks = list(app.tasks.keys())
        report_tasks = [task for task in registered_tasks if 'report_generation' in task]
        
        logger.info(f"   📊 Found {len(report_tasks)} report generation tasks:")
        for task in report_tasks:
            logger.info(f"      - {task}")
        
        if len(report_tasks) >= 2:
            logger.info("   ✅ Report generation tasks properly registered")
        else:
            logger.warning("   ⚠️ Some report generation tasks may not be registered")
        
        # Test 3: Test workflow orchestrator integration
        logger.info("📋 TEST 3: Checking workflow orchestrator integration")
        from services.workers.tasks.workflow_orchestrator import cleanup_workflow, terminate_workflow
        
        # Check if the functions contain report generation calls
        import inspect
        cleanup_source = inspect.getsource(cleanup_workflow)
        terminate_source = inspect.getsource(terminate_workflow)
        
        if "queue_report_generation" in cleanup_source:
            logger.info("   ✅ Report generation integrated in cleanup_workflow")
        else:
            logger.warning("   ⚠️ Report generation NOT found in cleanup_workflow")
            
        if "queue_report_generation" in terminate_source:
            logger.info("   ✅ Report generation integrated in terminate_workflow")
        else:
            logger.warning("   ⚠️ Report generation NOT found in terminate_workflow")
        
        # Test 4: Test report generation with mock data
        logger.info("📋 TEST 4: Testing report generation with mock data")
        
        mock_workflow_info = {
            "workflow_id": f"test-workflow-{int(time.time())}",
            "file_uuid": "test-uuid-12345",
            "filename": "test_binary.exe",
            "status": "completed",
            "file_size": 1024000,
            "file_hash": "abcd1234",
            "vm_id": "test-vm-001"
        }
        
        # Queue a test report generation (this will actually queue in Celery)
        logger.info("   🎯 Queuing test report generation...")
        try:
            result = queue_report_generation.delay(
                mock_workflow_info["workflow_id"],
                delay_seconds=5  # Short delay for testing
            )
            logger.info(f"   ✅ Report generation queued successfully: {result.id}")
            
            # Wait a moment and check task status
            await asyncio.sleep(2)
            task_status = result.status
            logger.info(f"   📊 Task status: {task_status}")
            
        except Exception as e:
            logger.error(f"   ❌ Failed to queue report generation: {e}")
        
        # Test 5: Check Elasticsearch connectivity for report generation
        logger.info("📋 TEST 5: Checking Elasticsearch connectivity")
        try:
            from elasticsearch import Elasticsearch
            
            es_client = Elasticsearch(
                hosts=["http://localhost:9200"],
                timeout=10
            )
            
            # Test connection
            cluster_info = es_client.info()
            logger.info(f"   ✅ Elasticsearch connected: {cluster_info['cluster_name']}")
            
            # Check for existing indices
            indices = es_client.cat.indices(format="json")
            turdparty_indices = [idx for idx in indices if 'turdparty' in idx['index']]
            logger.info(f"   📊 Found {len(turdparty_indices)} TurdParty indices")
            
        except Exception as e:
            logger.error(f"   ❌ Elasticsearch connection failed: {e}")
        
        # Test 6: Check report generator availability
        logger.info("📋 TEST 6: Checking report generator availability")
        try:
            from services.report_generator import SphinxReportGenerator
            
            generator = SphinxReportGenerator()
            logger.info("   ✅ SphinxReportGenerator imported successfully")
            
            # Check if reports directory exists
            reports_dir = Path("docs/reports")
            if reports_dir.exists():
                logger.info(f"   ✅ Reports directory exists: {reports_dir}")
            else:
                logger.warning(f"   ⚠️ Reports directory not found: {reports_dir}")
            
        except Exception as e:
            logger.error(f"   ❌ Report generator import failed: {e}")
        
        # Test 7: Verify queue configuration
        logger.info("📋 TEST 7: Checking queue configuration")
        
        celery_config = app.conf
        task_routes = celery_config.get('task_routes', {})
        
        report_queue_config = task_routes.get('tasks.report_generation.*')
        if report_queue_config:
            logger.info(f"   ✅ Report generation queue configured: {report_queue_config}")
        else:
            logger.warning("   ⚠️ Report generation queue not configured")
        
        logger.info("\n🎉 REPORT GENERATION INTEGRATION TEST COMPLETE")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Report generation integration test failed: {e}")
        return False


async def test_real_binary_report_queue():
    """Test queuing reports for the real binaries we processed."""
    logger.info("\n🔥 TESTING REAL BINARY REPORT QUEUE")
    logger.info("=" * 60)
    
    try:
        # Check for recent real binary processing results
        results_files = list(Path("/tmp").glob("real-binary-test-*.json"))
        
        if not results_files:
            logger.warning("   ⚠️ No recent real binary test results found")
            return False
        
        # Get the most recent results file
        latest_results = max(results_files, key=lambda p: p.stat().st_mtime)
        logger.info(f"   📄 Using results from: {latest_results}")
        
        with open(latest_results, 'r') as f:
            results_data = json.load(f)
        
        # Extract successful processing results
        processing_results = results_data.get("processing_results", {})
        upload_results = results_data.get("upload_results", {})
        
        successful_binaries = [
            binary_name for binary_name, result in processing_results.items()
            if result.get("success", False)
        ]
        
        logger.info(f"   📊 Found {len(successful_binaries)} successfully processed binaries")
        
        # Queue report generation for each successful binary
        from services.workers.tasks.report_generation import queue_report_generation
        
        queued_reports = []
        for binary_name in successful_binaries[:3]:  # Test with first 3 binaries
            try:
                file_uuid = upload_results[binary_name]["file_uuid"]
                
                # Create mock workflow ID
                workflow_id = f"real-binary-{binary_name}-{int(time.time())}"
                
                # Queue the report
                result = queue_report_generation.delay(
                    workflow_id,
                    delay_seconds=10
                )
                
                queued_reports.append({
                    "binary_name": binary_name,
                    "file_uuid": file_uuid,
                    "workflow_id": workflow_id,
                    "task_id": result.id
                })
                
                logger.info(f"   ✅ Queued report for {binary_name}: {result.id}")
                
            except Exception as e:
                logger.error(f"   ❌ Failed to queue report for {binary_name}: {e}")
        
        logger.info(f"\n📊 SUMMARY: {len(queued_reports)} reports queued successfully")
        
        # Save the queued report information
        queue_info_file = f"/tmp/queued-reports-{int(time.time())}.json"
        with open(queue_info_file, 'w') as f:
            json.dump(queued_reports, f, indent=2)
        
        logger.info(f"📄 Queued report info saved: {queue_info_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real binary report queue test failed: {e}")
        return False


async def main():
    """Main test execution."""
    logger.info("🚀 REPORT GENERATION INTEGRATION TESTING")
    logger.info("Testing report generation queuing after workflow completion")
    logger.info("=" * 80)
    
    # Run integration tests
    integration_success = await test_report_generation_integration()
    
    # Run real binary report queue test
    real_binary_success = await test_real_binary_report_queue()
    
    # Summary
    logger.info("\n📊 TEST SUMMARY")
    logger.info("=" * 40)
    logger.info(f"Integration Test: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    logger.info(f"Real Binary Test: {'✅ PASSED' if real_binary_success else '❌ FAILED'}")
    
    overall_success = integration_success and real_binary_success
    logger.info(f"Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
