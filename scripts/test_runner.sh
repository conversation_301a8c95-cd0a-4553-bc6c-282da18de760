#!/bin/bash
# Modern test runner for TurdParty using industry-standard tools
# Supports multiple test types and environments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORTS_DIR="${PROJECT_ROOT}/reports"
COVERAGE_THRESHOLD=80
PARALLEL_WORKERS=4

# Create reports directory
mkdir -p "${REPORTS_DIR}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} $1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run unit tests (real implementations only)
run_unit_tests() {
    print_header "Running Unit Tests (Real Implementations)"

    python -m pytest tests/unit/ \
        --cov=services.api.src \
        --cov-report=html:"${REPORTS_DIR}/coverage-html" \
        --cov-report=term-missing \
        --cov-report=json:"${REPORTS_DIR}/coverage.json" \
        --cov-fail-under="${COVERAGE_THRESHOLD}" \
        --junitxml="${REPORTS_DIR}/junit-unit.xml" \
        --html="${REPORTS_DIR}/unit-tests.html" \
        --self-contained-html \
        --asyncio-mode=auto \
        -v \
        "$@"

    print_success "Unit tests completed"
}

# Function to run property-based tests
run_property_tests() {
    print_header "Running Property-Based Tests (Hypothesis)"
    
    python -m pytest tests/property/ \
        --hypothesis-show-statistics \
        --junitxml="${REPORTS_DIR}/junit-property.xml" \
        --html="${REPORTS_DIR}/property-tests.html" \
        --self-contained-html \
        -v \
        "$@"
    
    print_success "Property-based tests completed"
}

# Function to run performance benchmarks
run_performance_tests() {
    print_header "Running Performance Benchmarks"
    
    python -m pytest tests/performance/ \
        --benchmark-only \
        --benchmark-json="${REPORTS_DIR}/benchmark-results.json" \
        --benchmark-html="${REPORTS_DIR}/benchmark-results.html" \
        --junitxml="${REPORTS_DIR}/junit-performance.xml" \
        -v \
        "$@"
    
    print_success "Performance benchmarks completed"
}

# Function to run security tests
run_security_tests() {
    print_header "Running Security Tests"
    
    # Bandit security scanning
    print_status "Running Bandit security scan..."
    bandit -r api/ services/ \
        -f json \
        -o "${REPORTS_DIR}/bandit-report.json" \
        -c pyproject.toml
    
    bandit -r api/ services/ \
        -f txt \
        -c pyproject.toml
    
    # Safety dependency scanning
    print_status "Running Safety dependency scan..."
    safety check \
        --json \
        --output "${REPORTS_DIR}/safety-report.json" || true
    
    safety check || true
    
    # Security-focused pytest tests
    print_status "Running security test suite..."
    python -m pytest tests/security/ \
        --junitxml="${REPORTS_DIR}/junit-security.xml" \
        --html="${REPORTS_DIR}/security-tests.html" \
        --self-contained-html \
        -v \
        "$@"
    
    print_success "Security tests completed"
}

# Function to run real integration tests
run_integration_tests() {
    print_header "Running Real Integration Tests (No Mocks)"

    # Check if services are available
    print_status "Checking service availability..."

    python -m pytest tests/integration/ \
        -m "integration" \
        --junitxml="${REPORTS_DIR}/junit-integration.xml" \
        --html="${REPORTS_DIR}/integration-tests.html" \
        --self-contained-html \
        --asyncio-mode=auto \
        --tb=short \
        -v \
        "$@"

    print_success "Real integration tests completed"
}

# Function to run load tests
run_load_tests() {
    print_header "Running Load Tests (Locust)"
    
    if ! command_exists locust; then
        print_error "Locust not found. Install with: pip install locust"
        return 1
    fi
    
    print_status "Starting load test..."
    locust -f tests/load/locustfile.py \
        --headless \
        -u 10 \
        -r 2 \
        -t 30s \
        --host=http://localhost:8000 \
        --html="${REPORTS_DIR}/load-test-report.html" \
        --csv="${REPORTS_DIR}/load-test" \
        || true
    
    print_success "Load tests completed"
}

# Function to run linting and formatting
run_linting() {
    print_header "Running Code Quality Checks"
    
    # Ruff linting
    print_status "Running Ruff linting..."
    ruff check api/ services/ tests/ --output-format=json > "${REPORTS_DIR}/ruff-report.json" || true
    ruff check api/ services/ tests/
    
    # Ruff formatting check
    print_status "Checking code formatting..."
    ruff format --check api/ services/ tests/
    
    # MyPy type checking
    print_status "Running MyPy type checking..."
    mypy api/ services/ \
        --html-report "${REPORTS_DIR}/mypy-html" \
        --json-report "${REPORTS_DIR}/mypy-report.json" || true
    
    print_success "Code quality checks completed"
}

# Function to run all tests
run_all_tests() {
    print_header "Running Complete Test Suite"
    
    # Create timestamp for this run
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    RUN_DIR="${REPORTS_DIR}/run_${TIMESTAMP}"
    mkdir -p "${RUN_DIR}"
    
    # Redirect reports to timestamped directory
    REPORTS_DIR="${RUN_DIR}"
    
    # Run all test types
    run_linting "$@"
    run_unit_tests "$@"
    run_property_tests "$@"
    run_security_tests "$@"
    
    # Only run these if services are available
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        run_integration_tests "$@"
        run_load_tests "$@"
    else
        print_warning "Services not running, skipping integration and load tests"
    fi
    
    # Performance tests (can run without services)
    run_performance_tests "$@"
    
    # Generate summary report
    generate_summary_report "${RUN_DIR}"
    
    print_success "Complete test suite finished. Reports in: ${RUN_DIR}"
}

# Function to generate summary report
generate_summary_report() {
    local report_dir="$1"
    local summary_file="${report_dir}/test-summary.html"
    
    print_status "Generating summary report..."
    
    cat > "${summary_file}" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>TurdParty Test Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .links { margin: 10px 0; }
        .links a { margin-right: 15px; text-decoration: none; color: #007bff; }
        .links a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 TurdParty Test Summary</h1>
        <p>Generated: $(date)</p>
        <p>Test Run: $(basename "${report_dir}")</p>
    </div>
    
    <div class="section success">
        <h2>✅ Test Reports</h2>
        <div class="links">
            <a href="unit-tests.html">Unit Tests</a>
            <a href="property-tests.html">Property Tests</a>
            <a href="security-tests.html">Security Tests</a>
            <a href="integration-tests.html">Integration Tests</a>
            <a href="coverage-html/index.html">Coverage Report</a>
            <a href="benchmark-results.html">Performance Benchmarks</a>
            <a href="load-test-report.html">Load Test Report</a>
            <a href="mypy-html/index.html">Type Checking</a>
        </div>
    </div>
    
    <div class="section">
        <h2>📊 Raw Data</h2>
        <div class="links">
            <a href="coverage.json">Coverage JSON</a>
            <a href="benchmark-results.json">Benchmark JSON</a>
            <a href="bandit-report.json">Security Scan JSON</a>
            <a href="safety-report.json">Dependency Scan JSON</a>
            <a href="ruff-report.json">Linting JSON</a>
            <a href="mypy-report.json">Type Check JSON</a>
        </div>
    </div>
    
    <div class="section">
        <h2>🔧 Commands Used</h2>
        <pre>
Unit Tests: pytest tests/unit/ --cov=api --cov=services
Property Tests: pytest tests/property/ --hypothesis-show-statistics  
Performance: pytest tests/performance/ --benchmark-only
Security: bandit + safety + pytest tests/security/
Load Tests: locust -f tests/load/locustfile.py
        </pre>
    </div>
</body>
</html>
EOF
    
    print_success "Summary report generated: ${summary_file}"
}

# Function to show usage
show_usage() {
    echo "TurdParty Modern Test Runner"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  unit         Run unit tests with coverage"
    echo "  property     Run property-based tests (Hypothesis)"
    echo "  performance  Run performance benchmarks"
    echo "  security     Run security tests and scans"
    echo "  integration  Run integration tests"
    echo "  load         Run load tests (Locust)"
    echo "  lint         Run linting and formatting checks"
    echo "  all          Run complete test suite"
    echo "  help         Show this help message"
    echo ""
    echo "Options:"
    echo "  -v, --verbose    Verbose output"
    echo "  -x, --exitfirst  Stop on first failure"
    echo "  -k EXPRESSION    Only run tests matching expression"
    echo "  --parallel N     Run tests in parallel (default: ${PARALLEL_WORKERS})"
    echo ""
    echo "Examples:"
    echo "  $0 unit -v                    # Run unit tests verbosely"
    echo "  $0 all --parallel 8           # Run all tests with 8 workers"
    echo "  $0 unit -k test_file_injection # Run specific test pattern"
}

# Main execution
main() {
    cd "${PROJECT_ROOT}"
    
    case "${1:-help}" in
        "unit")
            shift
            run_unit_tests "$@"
            ;;
        "property")
            shift
            run_property_tests "$@"
            ;;
        "performance")
            shift
            run_performance_tests "$@"
            ;;
        "security")
            shift
            run_security_tests "$@"
            ;;
        "integration")
            shift
            run_integration_tests "$@"
            ;;
        "load")
            shift
            run_load_tests "$@"
            ;;
        "lint")
            shift
            run_linting "$@"
            ;;
        "all")
            shift
            run_all_tests "$@"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
