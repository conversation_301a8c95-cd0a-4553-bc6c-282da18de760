#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Service Configuration Generator

This script generates environment-specific configuration files from the
central config/service-urls.json file. It creates configuration files
for different services and environments.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any


def load_service_config() -> Dict[str, Any]:
    """Load the central service configuration"""
    config_path = Path(__file__).parent.parent / "config" / "service-urls.json"
    
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Service URLs config not found at {config_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in service URLs config: {e}")
        sys.exit(1)


def generate_javascript_config(config: Dict[str, Any], environment: str) -> str:
    """Generate JavaScript configuration"""
    env_config = config['environments'][environment]
    
    js_config = f"""// Auto-generated service configuration for {environment}
// Generated from config/service-urls.json - DO NOT EDIT MANUALLY

const SERVICE_CONFIG = {{
    environment: '{environment}',
    domain: '{env_config['domain']}',
    protocol: '{env_config['protocol']}',
    
    services: {{"""
    
    for service_name, service_config in env_config['services'].items():
        js_config += f"""
        {service_name}: {{
            subdomain: {json.dumps(service_config['subdomain'])},
            port: {json.dumps(service_config['port'])},
            path: '{service_config['path']}',
            health_endpoint: '{service_config['health_endpoint']}'
        }},"""
    
    js_config += """
    },
    
    api_endpoints: {"""
    
    for category, endpoints in config['api_endpoints'].items():
        js_config += f"""
        {category}: {{"""
        for endpoint_name, endpoint_path in endpoints.items():
            js_config += f"""
            {endpoint_name}: '{endpoint_path}',"""
        js_config += """
        },"""
    
    js_config += """
    },
    
    minio_buckets: {"""
    
    for bucket_type, bucket_name in config['minio_buckets'].items():
        js_config += f"""
        {bucket_type}: '{bucket_name}',"""
    
    js_config += """
    },
    
    elasticsearch_indices: {"""
    
    for index_type, index_pattern in config['elasticsearch_indices'].items():
        js_config += f"""
        {index_type}: '{index_pattern}',"""
    
    js_config += """
    }
};

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SERVICE_CONFIG;
}
if (typeof window !== 'undefined') {
    window.SERVICE_CONFIG = SERVICE_CONFIG;
}
export default SERVICE_CONFIG;
"""
    
    return js_config


def generate_python_config(config: Dict[str, Any], environment: str) -> str:
    """Generate Python configuration"""
    env_config = config['environments'][environment]
    
    python_config = f'''"""
Auto-generated service configuration for {environment}
Generated from config/service-urls.json - DO NOT EDIT MANUALLY
"""

SERVICE_CONFIG = {json.dumps(config, indent=4)}

ENVIRONMENT = '{environment}'
ENV_CONFIG = SERVICE_CONFIG['environments'][ENVIRONMENT]

# Convenience functions
def get_service_url(service_name: str, include_health: bool = False) -> str:
    """Get service URL for current environment"""
    from utils.service_urls import get_service_url_manager
    return get_service_url_manager().get_service_url(service_name, include_health)

def get_api_endpoint(endpoint: str, params: dict = None) -> str:
    """Get API endpoint URL for current environment"""
    from utils.service_urls import get_service_url_manager
    return get_service_url_manager().get_api_endpoint(endpoint, params or {{}})
'''
    
    return python_config


def generate_env_file(config: Dict[str, Any], environment: str) -> str:
    """Generate .env file"""
    env_config = config['environments'][environment]
    
    env_content = f"""# Auto-generated environment configuration for {environment}
# Generated from config/service-urls.json - DO NOT EDIT MANUALLY

TURDPARTY_ENV={environment}
ENVIRONMENT={environment}
DOMAIN={env_config['domain']}
PROTOCOL={env_config['protocol']}

# Service URLs
"""
    
    for service_name, service_config in env_config['services'].items():
        if service_config['subdomain']:
            url = f"{env_config['protocol']}://{service_config['subdomain']}.{env_config['domain']}"
        else:
            url = f"{env_config['protocol']}://{env_config['domain']}"
        
        if service_config['port']:
            url += f":{service_config['port']}"
        
        if service_config['path']:
            url += service_config['path']
        
        env_content += f"{service_name.upper()}_URL={url}\n"
    
    env_content += f"""
# MinIO Buckets
"""
    for bucket_type, bucket_name in config['minio_buckets'].items():
        env_content += f"MINIO_BUCKET_{bucket_type.upper()}={bucket_name}\n"
    
    env_content += f"""
# Elasticsearch Indices
"""
    for index_type, index_pattern in config['elasticsearch_indices'].items():
        env_content += f"ELASTICSEARCH_INDEX_{index_type.upper()}={index_pattern}\n"
    
    return env_content


def main():
    """Main function"""
    print("🔧 Generating service configuration files...")
    
    # Load central configuration
    config = load_service_config()
    
    # Create output directory
    output_dir = Path(__file__).parent.parent / "generated-config"
    output_dir.mkdir(exist_ok=True)
    
    # Generate for each environment
    for environment in config['environments'].keys():
        print(f"📝 Generating configuration for {environment}...")
        
        # Create environment directory
        env_dir = output_dir / environment
        env_dir.mkdir(exist_ok=True)
        
        # Generate JavaScript config
        js_config = generate_javascript_config(config, environment)
        with open(env_dir / "service-config.js", 'w') as f:
            f.write(js_config)
        
        # Generate Python config
        python_config = generate_python_config(config, environment)
        with open(env_dir / "service_config.py", 'w') as f:
            f.write(python_config)
        
        # Generate .env file
        env_file = generate_env_file(config, environment)
        with open(env_dir / ".env", 'w') as f:
            f.write(env_file)
        
        print(f"   ✅ Generated files for {environment}")
    
    print(f"""
🎉 Configuration generation complete!

Generated files in: {output_dir}

📁 Structure:
{output_dir}/
├── development/
│   ├── service-config.js
│   ├── service_config.py
│   └── .env
├── staging/
│   ├── service-config.js
│   ├── service_config.py
│   └── .env
├── production/
│   ├── service-config.js
│   ├── service_config.py
│   └── .env
└── local/
    ├── service-config.js
    ├── service_config.py
    └── .env

💡 Usage:
- Import service-config.js in frontend applications
- Import service_config.py in Python services
- Source .env files for environment variables
""")


if __name__ == "__main__":
    main()
