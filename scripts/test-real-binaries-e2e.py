#!/usr/bin/env python3
"""
TOP 20 WINDOWS BINARIES - PRODUCTION PROCESSING
Downloads and processes 20 ACTUAL Windows binaries through the complete TurdParty workflow.
PRODUCTION RUN - NOT TESTING - ACTUAL BINARY ANALYSIS PIPELINE.
Comprehensive logging to verify every step of the sequence.
"""

import asyncio
import hashlib
import json
import logging
import os
import sys
import tempfile
import time
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List
import requests

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import real service implementations
from services.api.src.services.elk_logger import ELKLogger
from services.api.src.services.file_injection_service import FileInjectionService
from services.api.src.models.file_injection import FileInjectionCreate

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'/tmp/real-binary-test-{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RealBinaryTester:
    """Test real Windows binaries through complete TurdParty workflow."""
    
    def __init__(self):
        self.elk_logger = None
        self.file_injection_service = None
        self.minio_client = None
        self.redis_client = None
        self.results = {}
        
        # TOP 20 REAL WINDOWS BINARIES FOR PRODUCTION PROCESSING
        self.real_binaries = {
            "notepadpp": {
                "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.6.2/npp.8.6.2.portable.x64.zip",
                "filename": "npp.8.6.2.portable.x64.zip",
                "expected_size_mb": 6,
                "description": "Notepad++ Portable - Text Editor"
            },
            "7zip": {
                "url": "https://www.7-zip.org/a/7z2301-x64.exe",
                "filename": "7z2301-x64.exe",
                "expected_size_mb": 2,
                "description": "7-Zip File Archiver"
            },
            "putty": {
                "url": "https://the.earth.li/~sgtatham/putty/latest/w64/putty.exe",
                "filename": "putty.exe",
                "expected_size_mb": 2,
                "description": "PuTTY SSH Client"
            },
            "vlc": {
                "url": "https://get.videolan.org/vlc/3.0.20/win64/vlc-3.0.20-win64.exe",
                "filename": "vlc-3.0.20-win64.exe",
                "expected_size_mb": 45,
                "description": "VLC Media Player"
            },
            "firefox": {
                "url": "https://download.mozilla.org/?product=firefox-stub&os=win&lang=en-US",
                "filename": "firefox-installer.exe",
                "expected_size_mb": 1,
                "description": "Mozilla Firefox Browser Installer"
            },
            "chrome": {
                "url": "https://dl.google.com/chrome/install/ChromeStandaloneSetup64.exe",
                "filename": "ChromeStandaloneSetup64.exe",
                "expected_size_mb": 2,
                "description": "Google Chrome Browser"
            },
            "vscode": {
                "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64",
                "filename": "VSCodeSetup-x64.exe",
                "expected_size_mb": 100,
                "description": "Visual Studio Code"
            },
            "git": {
                "url": "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe",
                "filename": "Git-2.43.0-64-bit.exe",
                "expected_size_mb": 50,
                "description": "Git for Windows"
            },
            "nodejs": {
                "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
                "filename": "node-v20.10.0-x64.msi",
                "expected_size_mb": 30,
                "description": "Node.js Runtime"
            },
            "python": {
                "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
                "filename": "python-3.12.1-amd64.exe",
                "expected_size_mb": 25,
                "description": "Python Programming Language"
            },
            "wireshark": {
                "url": "https://2.na.dl.wireshark.org/win64/Wireshark-4.2.0-x64.exe",
                "filename": "Wireshark-4.2.0-x64.exe",
                "expected_size_mb": 60,
                "description": "Wireshark Network Analyzer"
            },
            "winrar": {
                "url": "https://www.rarlab.com/rar/winrar-x64-623.exe",
                "filename": "winrar-x64-623.exe",
                "expected_size_mb": 3,
                "description": "WinRAR Archive Manager"
            },
            "teamviewer": {
                "url": "https://download.teamviewer.com/download/TeamViewer_Setup_x64.exe",
                "filename": "TeamViewer_Setup_x64.exe",
                "expected_size_mb": 25,
                "description": "TeamViewer Remote Access"
            },
            "discord": {
                "url": "https://discord.com/api/downloads/distributions/app/installers/latest?channel=stable&platform=win&arch=x64",
                "filename": "DiscordSetup.exe",
                "expected_size_mb": 100,
                "description": "Discord Communication Platform"
            },
            "zoom": {
                "url": "https://zoom.us/client/5.16.10.668/ZoomInstaller.exe",
                "filename": "ZoomInstaller.exe",
                "expected_size_mb": 5,
                "description": "Zoom Video Conferencing"
            },
            "slack": {
                "url": "https://downloads.slack-edge.com/releases/windows/4.36.140/prod/x64/SlackSetup.exe",
                "filename": "SlackSetup.exe",
                "expected_size_mb": 120,
                "description": "Slack Team Communication"
            },
            "steam": {
                "url": "https://cdn.akamai.steamstatic.com/client/installer/SteamSetup.exe",
                "filename": "SteamSetup.exe",
                "expected_size_mb": 2,
                "description": "Steam Gaming Platform"
            },
            "spotify": {
                "url": "https://download.scdn.co/SpotifySetup.exe",
                "filename": "SpotifySetup.exe",
                "expected_size_mb": 1,
                "description": "Spotify Music Streaming"
            },
            "obs": {
                "url": "https://github.com/obsproject/obs-studio/releases/download/30.0.2/OBS-Studio-30.0.2-Full-Installer-x64.exe",
                "filename": "OBS-Studio-30.0.2-Full-Installer-x64.exe",
                "expected_size_mb": 120,
                "description": "OBS Studio Broadcasting Software"
            },
            "audacity": {
                "url": "https://github.com/audacity/audacity/releases/download/Audacity-3.4.2/audacity-win-3.4.2-x64.exe",
                "filename": "audacity-win-3.4.2-x64.exe",
                "expected_size_mb": 30,
                "description": "Audacity Audio Editor"
            }
        }
        
        # Create download directory
        self.download_dir = Path("/tmp/turdparty-real-binaries")
        self.download_dir.mkdir(exist_ok=True)
        
    async def initialize_real_services(self) -> bool:
        """Initialize all real service connections with comprehensive logging."""
        logger.info("🔧 INITIALIZING REAL SERVICES")
        logger.info("=" * 60)
        
        try:
            # Set environment variables for services
            os.environ["ELASTICSEARCH_HOST"] = "localhost"
            os.environ["ELASTICSEARCH_PORT"] = "9200"
            os.environ["LOGSTASH_HOST"] = "localhost"
            os.environ["LOGSTASH_PORT"] = "5000"
            
            # Initialize ELK logger
            logger.info("📊 Initializing ELK Logger...")
            self.elk_logger = ELKLogger()
            
            # Test Elasticsearch connection
            await self.elk_logger.es_client.info()
            logger.info("   ✅ Elasticsearch connection verified")
            
            # Initialize Redis client
            logger.info("🔄 Initializing Redis client...")
            import redis
            self.redis_client = redis.Redis(
                host="localhost",
                port=6379,
                decode_responses=True,
                socket_timeout=5
            )
            self.redis_client.ping()
            logger.info("   ✅ Redis connection verified")
            
            # Initialize MinIO client
            logger.info("📦 Initializing MinIO client...")
            from minio import Minio
            self.minio_client = Minio(
                "localhost:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            list(self.minio_client.list_buckets())
            logger.info("   ✅ MinIO connection verified")
            
            # Initialize file injection service
            logger.info("💉 Initializing File Injection Service...")
            self.file_injection_service = FileInjectionService()
            logger.info("   ✅ File Injection Service initialized")
            
            logger.info("🎉 ALL SERVICES INITIALIZED SUCCESSFULLY")
            return True
            
        except Exception as e:
            logger.error(f"❌ Service initialization failed: {e}")
            return False
    
    async def download_real_binary(self, binary_name: str, binary_info: Dict[str, Any]) -> Dict[str, Any]:
        """Download a real binary with comprehensive validation and logging."""
        logger.info(f"📥 DOWNLOADING REAL BINARY: {binary_name}")
        logger.info(f"   URL: {binary_info['url']}")
        logger.info(f"   Expected size: ~{binary_info['expected_size_mb']}MB")
        
        file_path = self.download_dir / binary_info['filename']
        
        try:
            # Check if already downloaded
            if file_path.exists():
                logger.info(f"   📁 File already exists: {file_path}")
                file_size = file_path.stat().st_size
                logger.info(f"   📏 Existing file size: {file_size:,} bytes")
            else:
                # Download the binary
                logger.info(f"   🌐 Downloading from: {binary_info['url']}")
                response = requests.get(binary_info['url'], stream=True, timeout=60)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                logger.info(f"   📏 Download size: {total_size:,} bytes ({total_size/1024/1024:.1f}MB)")
                
                downloaded = 0
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            if downloaded % (1024 * 1024) == 0:  # Log every MB
                                logger.info(f"   📊 Downloaded: {downloaded:,} bytes ({downloaded/total_size*100:.1f}%)")
                
                file_size = file_path.stat().st_size
                logger.info(f"   ✅ Download complete: {file_size:,} bytes")
            
            # Calculate file hash
            logger.info("   🔐 Calculating file hash...")
            file_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    file_hash.update(chunk)
            
            calculated_hash = file_hash.hexdigest()
            logger.info(f"   🔐 SHA256: {calculated_hash}")
            
            # Validate file size
            size_mb = file_size / 1024 / 1024
            expected_size = binary_info['expected_size_mb']
            if size_mb > expected_size * 2:  # Allow 2x expected size
                logger.warning(f"   ⚠️  File size ({size_mb:.1f}MB) larger than expected ({expected_size}MB)")
            
            # Log to ELK
            await self.elk_logger.log_system_event(
                event_type="binary_downloaded",
                message=f"Real binary downloaded: {binary_name}",
                details={
                    "binary_name": binary_name,
                    "filename": binary_info['filename'],
                    "file_path": str(file_path),
                    "file_size": file_size,
                    "file_hash": calculated_hash,
                    "download_url": binary_info['url'],
                    "description": binary_info['description']
                }
            )
            
            logger.info(f"   🎉 {binary_name} download successful")
            
            return {
                "success": True,
                "file_path": str(file_path),
                "file_size": file_size,
                "file_hash": calculated_hash,
                "binary_info": binary_info
            }
            
        except Exception as e:
            logger.error(f"   ❌ {binary_name} download failed: {e}")
            
            # Log failure to ELK
            await self.elk_logger.log_system_event(
                event_type="binary_download_failed",
                message=f"Real binary download failed: {binary_name}",
                level="ERROR",
                details={
                    "binary_name": binary_name,
                    "error": str(e),
                    "download_url": binary_info['url']
                }
            )
            
            return {
                "success": False,
                "error": str(e),
                "binary_info": binary_info
            }
    
    async def upload_to_minio(self, binary_name: str, file_path: str, file_hash: str) -> Dict[str, Any]:
        """Upload real binary to MinIO with comprehensive logging."""
        logger.info(f"📦 UPLOADING TO MINIO: {binary_name}")
        
        try:
            # Create bucket if it doesn't exist
            bucket_name = "turdparty-binaries"
            if not self.minio_client.bucket_exists(bucket_name):
                logger.info(f"   🪣 Creating bucket: {bucket_name}")
                self.minio_client.make_bucket(bucket_name)
            
            # Generate object name with UUID
            file_uuid = str(uuid.uuid4())
            object_name = f"real-binaries/{file_uuid}/{Path(file_path).name}"
            
            logger.info(f"   📤 Uploading to: {bucket_name}/{object_name}")
            logger.info(f"   🆔 File UUID: {file_uuid}")
            
            # Upload file
            file_size = Path(file_path).stat().st_size
            self.minio_client.fput_object(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=file_path,
                metadata={
                    "file-uuid": file_uuid,
                    "binary-name": binary_name,
                    "file-hash": file_hash,
                    "upload-timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
            
            logger.info(f"   ✅ Upload successful: {file_size:,} bytes")
            
            # Log to ELK
            await self.elk_logger.log_system_event(
                event_type="binary_uploaded_minio",
                message=f"Real binary uploaded to MinIO: {binary_name}",
                details={
                    "binary_name": binary_name,
                    "file_uuid": file_uuid,
                    "bucket_name": bucket_name,
                    "object_name": object_name,
                    "file_size": file_size,
                    "file_hash": file_hash
                }
            )
            
            return {
                "success": True,
                "file_uuid": file_uuid,
                "bucket_name": bucket_name,
                "object_name": object_name,
                "file_size": file_size
            }
            
        except Exception as e:
            logger.error(f"   ❌ MinIO upload failed: {e}")
            
            # Log failure to ELK
            await self.elk_logger.log_system_event(
                event_type="binary_upload_failed",
                message=f"Real binary MinIO upload failed: {binary_name}",
                level="ERROR",
                details={
                    "binary_name": binary_name,
                    "error": str(e),
                    "file_path": file_path
                }
            )
            
            return {
                "success": False,
                "error": str(e)
            }

    async def create_real_file_injection(self, binary_name: str, file_path: str, file_uuid: str) -> Dict[str, Any]:
        """Create real file injection with comprehensive logging."""
        logger.info(f"💉 CREATING FILE INJECTION: {binary_name}")

        try:
            # Read actual file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            logger.info(f"   📄 File content size: {len(file_content):,} bytes")

            # Create injection data
            injection_data = FileInjectionCreate(
                filename=Path(file_path).name,
                target_path=f"/app/analysis/{binary_name}/{Path(file_path).name}",
                permissions="0755",
                description=f"Real binary injection: {binary_name} (UUID: {file_uuid})"
            )

            logger.info(f"   🎯 Target path: {injection_data.target_path}")
            logger.info(f"   🔐 Permissions: {injection_data.permissions}")

            # Create injection using real service
            injection_response = await self.file_injection_service.create_injection(
                injection_data, file_content
            )

            logger.info(f"   🆔 Injection ID: {injection_response.id}")
            logger.info(f"   📊 Status: {injection_response.status.value}")
            logger.info(f"   🔐 File hash: {injection_response.file_hash}")

            # Log to ELK
            await self.elk_logger.log_file_injection_event(
                injection_id=injection_response.id,
                event_type="injection_created",
                filename=injection_response.filename,
                target_path=injection_response.target_path,
                status=injection_response.status.value,
                details={
                    "binary_name": binary_name,
                    "file_uuid": file_uuid,
                    "file_size": injection_response.file_size,
                    "file_hash": injection_response.file_hash,
                    "permissions": injection_response.permissions
                }
            )

            logger.info(f"   ✅ File injection created successfully")

            return {
                "success": True,
                "injection_id": injection_response.id,
                "status": injection_response.status.value,
                "file_size": injection_response.file_size,
                "file_hash": injection_response.file_hash
            }

        except Exception as e:
            logger.error(f"   ❌ File injection failed: {e}")

            # Log failure to ELK
            await self.elk_logger.log_file_injection_event(
                injection_id="failed",
                event_type="injection_failed",
                filename=Path(file_path).name,
                error_message=str(e),
                details={
                    "binary_name": binary_name,
                    "file_uuid": file_uuid,
                    "error": str(e)
                }
            )

            return {
                "success": False,
                "error": str(e)
            }

    async def process_injection(self, binary_name: str, injection_id: str) -> Dict[str, Any]:
        """Process file injection with comprehensive logging."""
        logger.info(f"⚙️ PROCESSING INJECTION: {binary_name}")
        logger.info(f"   🆔 Injection ID: {injection_id}")

        try:
            # Process the injection
            status_response = await self.file_injection_service.process_injection(injection_id)

            logger.info(f"   📊 Final status: {status_response.status.value}")
            logger.info(f"   📝 Message: {status_response.message}")
            logger.info(f"   📈 Progress: {status_response.progress}%")

            # Log to ELK
            await self.elk_logger.log_file_injection_event(
                injection_id=injection_id,
                event_type="injection_processed",
                status=status_response.status.value,
                details={
                    "binary_name": binary_name,
                    "progress": status_response.progress,
                    "message": status_response.message,
                    "processing_details": status_response.details
                }
            )

            logger.info(f"   ✅ Injection processing completed")

            return {
                "success": True,
                "status": status_response.status.value,
                "progress": status_response.progress,
                "message": status_response.message,
                "details": status_response.details
            }

        except Exception as e:
            logger.error(f"   ❌ Injection processing failed: {e}")

            # Log failure to ELK
            await self.elk_logger.log_file_injection_event(
                injection_id=injection_id,
                event_type="injection_processing_failed",
                error_message=str(e),
                details={
                    "binary_name": binary_name,
                    "error": str(e)
                }
            )

            return {
                "success": False,
                "error": str(e)
            }

    async def run_complete_real_binary_test(self) -> Dict[str, Any]:
        """Run complete real binary testing workflow."""
        logger.info("🚀 STARTING TOP 20 BINARIES PRODUCTION PROCESSING")
        logger.info("=" * 80)

        start_time = time.time()

        # Step 0: Initialize services
        logger.info("\n🔧 STEP 0: SERVICE INITIALIZATION")
        if not await self.initialize_real_services():
            return {"error": "Service initialization failed"}

        # Step 1: Download real binaries
        logger.info("\n📥 STEP 1: DOWNLOADING REAL BINARIES")
        download_results = {}
        for binary_name, binary_info in self.real_binaries.items():
            result = await self.download_real_binary(binary_name, binary_info)
            download_results[binary_name] = result

            if not result["success"]:
                logger.error(f"❌ Skipping {binary_name} due to download failure")
                continue

        # Step 2: Upload to MinIO
        logger.info("\n📦 STEP 2: UPLOADING TO MINIO")
        upload_results = {}
        for binary_name, download_result in download_results.items():
            if not download_result["success"]:
                continue

            result = await self.upload_to_minio(
                binary_name,
                download_result["file_path"],
                download_result["file_hash"]
            )
            upload_results[binary_name] = result

        # Step 3: Create file injections
        logger.info("\n💉 STEP 3: CREATING FILE INJECTIONS")
        injection_results = {}
        for binary_name, download_result in download_results.items():
            if not download_result["success"]:
                continue

            upload_result = upload_results.get(binary_name, {})
            if not upload_result.get("success"):
                continue

            result = await self.create_real_file_injection(
                binary_name,
                download_result["file_path"],
                upload_result["file_uuid"]
            )
            injection_results[binary_name] = result

        # Step 4: Process injections
        logger.info("\n⚙️ STEP 4: PROCESSING INJECTIONS")
        processing_results = {}
        for binary_name, injection_result in injection_results.items():
            if not injection_result["success"]:
                continue

            result = await self.process_injection(
                binary_name,
                injection_result["injection_id"]
            )
            processing_results[binary_name] = result

        # Step 5: Generate comprehensive summary
        total_time = time.time() - start_time
        await self.generate_comprehensive_summary(
            download_results,
            upload_results,
            injection_results,
            processing_results,
            total_time
        )

        return {
            "download_results": download_results,
            "upload_results": upload_results,
            "injection_results": injection_results,
            "processing_results": processing_results,
            "total_time": total_time
        }

    async def generate_comprehensive_summary(
        self,
        download_results: Dict[str, Any],
        upload_results: Dict[str, Any],
        injection_results: Dict[str, Any],
        processing_results: Dict[str, Any],
        total_time: float
    ):
        """Generate comprehensive test summary with detailed logging."""
        logger.info("\n📊 COMPREHENSIVE TEST SUMMARY")
        logger.info("=" * 80)

        # Calculate success rates
        total_binaries = len(self.real_binaries)
        successful_downloads = sum(1 for r in download_results.values() if r.get("success"))
        successful_uploads = sum(1 for r in upload_results.values() if r.get("success"))
        successful_injections = sum(1 for r in injection_results.values() if r.get("success"))
        successful_processing = sum(1 for r in processing_results.values() if r.get("success"))

        logger.info(f"⏱️ TOTAL EXECUTION TIME: {total_time:.1f} seconds")
        logger.info(f"📊 BINARIES TESTED: {total_binaries}")
        logger.info("")
        logger.info(f"📥 DOWNLOADS: {successful_downloads}/{total_binaries} ({successful_downloads/total_binaries*100:.1f}%)")
        logger.info(f"📦 UPLOADS: {successful_uploads}/{total_binaries} ({successful_uploads/total_binaries*100:.1f}%)")
        logger.info(f"💉 INJECTIONS: {successful_injections}/{total_binaries} ({successful_injections/total_binaries*100:.1f}%)")
        logger.info(f"⚙️ PROCESSING: {successful_processing}/{total_binaries} ({successful_processing/total_binaries*100:.1f}%)")

        # Detailed per-binary results
        logger.info("\n📋 DETAILED RESULTS:")
        for binary_name in self.real_binaries.keys():
            download_ok = download_results.get(binary_name, {}).get("success", False)
            upload_ok = upload_results.get(binary_name, {}).get("success", False)
            injection_ok = injection_results.get(binary_name, {}).get("success", False)
            processing_ok = processing_results.get(binary_name, {}).get("success", False)

            status_icons = [
                "✅" if download_ok else "❌",
                "✅" if upload_ok else "❌",
                "✅" if injection_ok else "❌",
                "✅" if processing_ok else "❌"
            ]

            logger.info(f"   {binary_name}: {' '.join(status_icons)} (D/U/I/P)")

        # Calculate overall success rate
        overall_success = successful_processing / total_binaries * 100
        logger.info(f"\n🎯 OVERALL SUCCESS RATE: {overall_success:.1f}%")

        if overall_success >= 90:
            logger.info("   🎉 EXCELLENT: Real binary workflow performing exceptionally!")
        elif overall_success >= 70:
            logger.info("   ✅ GOOD: Real binary workflow performing well")
        else:
            logger.info("   ⚠️ NEEDS ATTENTION: Real binary workflow requires optimization")

        # Log summary to ELK
        await self.elk_logger.log_system_event(
            event_type="real_binary_test_completed",
            message="Real binary end-to-end test completed",
            details={
                "total_binaries": total_binaries,
                "successful_downloads": successful_downloads,
                "successful_uploads": successful_uploads,
                "successful_injections": successful_injections,
                "successful_processing": successful_processing,
                "overall_success_rate": overall_success,
                "total_time": total_time,
                "binaries_tested": list(self.real_binaries.keys())
            }
        )

        # Save detailed results
        results_file = f"/tmp/real-binary-test-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump({
                "download_results": download_results,
                "upload_results": upload_results,
                "injection_results": injection_results,
                "processing_results": processing_results,
                "summary": {
                    "total_binaries": total_binaries,
                    "successful_downloads": successful_downloads,
                    "successful_uploads": successful_uploads,
                    "successful_injections": successful_injections,
                    "successful_processing": successful_processing,
                    "overall_success_rate": overall_success,
                    "total_time": total_time
                }
            }, f, indent=2, default=str)

        logger.info(f"\n📄 Detailed results saved: {results_file}")
        logger.info("🎉 TOP 20 BINARIES PRODUCTION PROCESSING COMPLETE!")


async def main():
    """Main entry point for real binary testing."""
    logger.info("🚀 TOP 20 WINDOWS BINARIES - PRODUCTION PROCESSING")
    logger.info("Processing 20 actual Windows binaries through complete TurdParty workflow")
    logger.info("=" * 80)

    tester = RealBinaryTester()
    results = await tester.run_complete_real_binary_test()

    if "error" in results:
        logger.error(f"❌ Test failed: {results['error']}")
        return False

    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        logger.info("✅ Real binary testing completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Real binary testing failed!")
        sys.exit(1)
