#!/usr/bin/env python3
"""
Test Service Connections
Verify API and Redis connections are working before running full workflow.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
import aiohttp
import redis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_api_connection():
    """Test API connection and health."""
    logger.info("🔍 TESTING API CONNECTION")
    logger.info("=" * 50)
    
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Test health endpoint
            async with session.get("http://localhost:8000/health/") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"   ✅ API Health: {health_data.get('status', 'unknown')}")
                    logger.info(f"   📅 Timestamp: {health_data.get('timestamp', 'unknown')}")
                    return True
                else:
                    logger.error(f"   ❌ API Health check failed: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"   ❌ API connection failed: {e}")
        return False


def test_redis_connection():
    """Test Redis connection."""
    logger.info("\n🔍 TESTING REDIS CONNECTION")
    logger.info("=" * 50)
    
    try:
        # Test Redis connection
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # Test ping
        if r.ping():
            logger.info("   ✅ Redis ping successful")
            
            # Test set/get
            test_key = f"test_key_{int(time.time())}"
            test_value = "test_value"
            
            r.set(test_key, test_value, ex=60)  # Expire in 60 seconds
            retrieved_value = r.get(test_key)
            
            if retrieved_value == test_value:
                logger.info("   ✅ Redis set/get successful")
                r.delete(test_key)  # Cleanup
                return True
            else:
                logger.error(f"   ❌ Redis set/get failed: expected '{test_value}', got '{retrieved_value}'")
                return False
        else:
            logger.error("   ❌ Redis ping failed")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ Redis connection failed: {e}")
        return False


async def test_api_endpoints():
    """Test key API endpoints."""
    logger.info("\n🔍 TESTING API ENDPOINTS")
    logger.info("=" * 50)
    
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            
            # Test API docs
            async with session.get("http://localhost:8000/docs") as response:
                if response.status == 200:
                    logger.info("   ✅ API Docs accessible")
                else:
                    logger.warning(f"   ⚠️ API Docs failed: HTTP {response.status}")
            
            # Test API v1 root (should return method not allowed or similar)
            async with session.get("http://localhost:8000/api/v1/") as response:
                if response.status in [200, 404, 405]:  # Any of these is fine
                    logger.info("   ✅ API v1 endpoint accessible")
                else:
                    logger.warning(f"   ⚠️ API v1 endpoint issue: HTTP {response.status}")
            
            return True
            
    except Exception as e:
        logger.error(f"   ❌ API endpoint test failed: {e}")
        return False


def test_celery_connection():
    """Test Celery connection to Redis."""
    logger.info("\n🔍 TESTING CELERY CONNECTION")
    logger.info("=" * 50)
    
    try:
        # Add workers path to sys.path
        sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
        
        # Import Celery app with correct Redis configuration
        import os
        os.environ["REDIS_HOST"] = "localhost"  # Override to use localhost
        
        from celery_app import app
        
        # Test broker connection
        try:
            # Get broker connection
            with app.connection() as conn:
                conn.ensure_connection(max_retries=3)
            logger.info("   ✅ Celery broker connection successful")
            
            # Check registered tasks
            registered_tasks = list(app.tasks.keys())
            report_tasks = [task for task in registered_tasks if 'report_generation' in task]
            
            logger.info(f"   📊 Total registered tasks: {len(registered_tasks)}")
            logger.info(f"   📊 Report generation tasks: {len(report_tasks)}")
            
            if report_tasks:
                logger.info("   ✅ Report generation tasks found:")
                for task in report_tasks:
                    logger.info(f"      - {task}")
            else:
                logger.warning("   ⚠️ No report generation tasks found")
            
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Celery broker connection failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ Celery import failed: {e}")
        return False


async def test_file_upload_endpoint():
    """Test file upload endpoint with a small test file."""
    logger.info("\n🔍 TESTING FILE UPLOAD ENDPOINT")
    logger.info("=" * 50)
    
    try:
        # Create a small test file
        test_file_content = b"This is a test file for TurdParty upload testing."
        test_filename = "test_upload.txt"
        
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            
            # Prepare multipart form data
            data = aiohttp.FormData()
            data.add_field('description', 'Test file upload')
            data.add_field('file', test_file_content, filename=test_filename)
            
            # Upload file
            async with session.post("http://localhost:8000/api/v1/files/upload", data=data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.info("   ✅ File upload successful")
                    logger.info(f"   🆔 File ID: {result.get('file_id', 'unknown')}")
                    logger.info(f"   📏 File Size: {result.get('file_size', 'unknown')} bytes")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"   ❌ File upload failed: HTTP {response.status}")
                    logger.error(f"   📝 Error: {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"   ❌ File upload test failed: {e}")
        return False


async def main():
    """Main test execution."""
    logger.info("🚀 💩🎉TurdParty🎉💩 SERVICE CONNECTION TESTING")
    logger.info("Testing all service connections before running VM workflow")
    logger.info("=" * 80)
    
    # Run all tests
    api_success = await test_api_connection()
    redis_success = test_redis_connection()
    endpoints_success = await test_api_endpoints()
    celery_success = test_celery_connection()
    upload_success = await test_file_upload_endpoint()
    
    # Summary
    logger.info("\n📊 CONNECTION TEST SUMMARY")
    logger.info("=" * 50)
    logger.info(f"API Connection:     {'✅ PASSED' if api_success else '❌ FAILED'}")
    logger.info(f"Redis Connection:   {'✅ PASSED' if redis_success else '❌ FAILED'}")
    logger.info(f"API Endpoints:      {'✅ PASSED' if endpoints_success else '❌ FAILED'}")
    logger.info(f"Celery Connection:  {'✅ PASSED' if celery_success else '❌ FAILED'}")
    logger.info(f"File Upload:        {'✅ PASSED' if upload_success else '❌ FAILED'}")
    
    overall_success = all([api_success, redis_success, endpoints_success, celery_success, upload_success])
    logger.info(f"Overall Result:     {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n🎉 ALL SERVICES ARE READY!")
        logger.info("You can now run the VM workflow with confidence.")
    else:
        logger.info("\n⚠️ SOME SERVICES NEED ATTENTION")
        logger.info("Fix the failing connections before running the VM workflow.")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
