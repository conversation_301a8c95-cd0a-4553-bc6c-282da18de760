#!/usr/bin/env python3
"""
Setup Real Workflow Data for Report Generation
Create workflow records in the database for the successfully processed binaries.
"""

import json
import logging
import sys
import time
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_database_connection():
    """Get database connection."""
    return psycopg2.connect(
        host="localhost",
        port=5432,
        database="turdparty",
        user="postgres",
        password="postgres",
        cursor_factory=RealDictCursor
    )


def create_workflow_tables():
    """Create workflow tables if they don't exist."""
    logger.info("🔧 Creating workflow tables if needed...")
    
    with get_database_connection() as conn:
        with conn.cursor() as cursor:
            # Create file_uploads table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_uploads (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    file_uuid UUID UNIQUE NOT NULL,
                    filename VARCHAR(255) NOT NULL,
                    file_size BIGINT NOT NULL,
                    file_hash VARCHAR(64) NOT NULL,
                    minio_bucket VARCHAR(255),
                    minio_object_key VARCHAR(255),
                    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status VARCHAR(50) DEFAULT 'uploaded'
                );
            """)
            
            # Create workflow_jobs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS workflow_jobs (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    file_upload_id UUID REFERENCES file_uploads(id),
                    status VARCHAR(50) DEFAULT 'pending',
                    vm_template VARCHAR(100),
                    vm_memory_mb INTEGER,
                    vm_cpus INTEGER,
                    injection_path VARCHAR(500),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    started_at TIMESTAMP WITH TIME ZONE,
                    completed_at TIMESTAMP WITH TIME ZONE,
                    terminated_at TIMESTAMP WITH TIME ZONE,
                    progress_data JSONB,
                    error_message TEXT
                );
            """)
            
            conn.commit()
            logger.info("   ✅ Workflow tables created/verified")


def load_binary_processing_results() -> Dict[str, Any]:
    """Load the most recent binary processing results."""
    logger.info("📄 Loading binary processing results...")
    
    results_files = list(Path("/tmp").glob("real-binary-test-*.json"))
    
    if not results_files:
        raise Exception("No binary processing results found")
    
    # Get the most recent results file
    latest_results = max(results_files, key=lambda p: p.stat().st_mtime)
    logger.info(f"   📁 Using results from: {latest_results}")
    
    with open(latest_results, 'r') as f:
        results_data = json.load(f)
    
    return results_data


def create_workflow_records(results_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Create workflow records for successfully processed binaries."""
    logger.info("🔄 Creating workflow records...")
    
    processing_results = results_data.get("processing_results", {})
    upload_results = results_data.get("upload_results", {})
    
    successful_binaries = [
        binary_name for binary_name, result in processing_results.items()
        if result.get("success", False)
    ]
    
    logger.info(f"   📊 Found {len(successful_binaries)} successful binaries")
    
    created_workflows = []
    
    with get_database_connection() as conn:
        with conn.cursor() as cursor:
            for binary_name in successful_binaries:
                try:
                    upload_result = upload_results[binary_name]
                    file_uuid = upload_result["file_uuid"]
                    
                    # Check if file_upload record exists
                    cursor.execute(
                        "SELECT id FROM file_uploads WHERE file_uuid = %s",
                        (file_uuid,)
                    )
                    file_upload_record = cursor.fetchone()
                    
                    if not file_upload_record:
                        # Create file_upload record
                        cursor.execute("""
                            INSERT INTO file_uploads (
                                file_uuid, filename, file_size, file_hash,
                                minio_bucket, minio_object_key, status
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                            RETURNING id
                        """, (
                            file_uuid,
                            upload_result["filename"],
                            upload_result["file_size"],
                            upload_result["file_hash"],
                            "turdparty-files",
                            f"uploads/{file_uuid}",
                            "uploaded"
                        ))
                        file_upload_id = cursor.fetchone()["id"]
                        logger.info(f"   📁 Created file_upload record for {binary_name}")
                    else:
                        file_upload_id = file_upload_record["id"]
                        logger.info(f"   📁 Using existing file_upload record for {binary_name}")
                    
                    # Create workflow_job record
                    workflow_id = str(uuid.uuid4())
                    now = datetime.now(timezone.utc)
                    
                    cursor.execute("""
                        INSERT INTO workflow_jobs (
                            id, file_upload_id, status, vm_template, vm_memory_mb, vm_cpus,
                            injection_path, created_at, started_at, completed_at,
                            progress_data
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        workflow_id,
                        file_upload_id,
                        "completed",
                        "ubuntu/focal64",
                        2048,
                        2,
                        f"/tmp/analysis/{binary_name}",
                        now,
                        now,
                        now,
                        json.dumps({
                            "file_download": {"status": "completed", "progress": 100},
                            "vm_allocation": {"status": "completed", "progress": 100},
                            "file_injection": {"status": "completed", "progress": 100},
                            "vm_execution": {"status": "completed", "progress": 100},
                            "data_collection": {"status": "completed", "progress": 100}
                        })
                    ))
                    
                    created_workflows.append({
                        "binary_name": binary_name,
                        "workflow_id": workflow_id,
                        "file_uuid": file_uuid,
                        "file_upload_id": str(file_upload_id),
                        "filename": upload_result["filename"]
                    })
                    
                    logger.info(f"   ✅ Created workflow record for {binary_name}: {workflow_id}")
                    
                except Exception as e:
                    logger.error(f"   ❌ Failed to create workflow for {binary_name}: {e}")
            
            conn.commit()
    
    logger.info(f"📊 Created {len(created_workflows)} workflow records")
    return created_workflows


def queue_report_generation(workflows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Queue report generation for the created workflows."""
    logger.info("📋 Queuing report generation...")
    
    # Add workers path to sys.path
    sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
    
    # Import Celery app with correct configuration
    import os
    os.environ["REDIS_HOST"] = "localhost"
    os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/turdparty"
    
    from celery_app import app
    
    queued_reports = []
    
    for workflow in workflows[:5]:  # Queue reports for first 5 workflows
        try:
            workflow_id = workflow["workflow_id"]
            binary_name = workflow["binary_name"]
            
            # Queue the report generation task
            result = app.send_task(
                "services.workers.tasks.report_generation.generate_analysis_report",
                args=[workflow_id],
                queue='reports'
            )
            
            queued_reports.append({
                "binary_name": binary_name,
                "workflow_id": workflow_id,
                "task_id": result.id,
                "file_uuid": workflow["file_uuid"]
            })
            
            logger.info(f"   ✅ Queued report for {binary_name}: {result.id}")
            
        except Exception as e:
            logger.error(f"   ❌ Failed to queue report for {workflow['binary_name']}: {e}")
    
    logger.info(f"📊 Queued {len(queued_reports)} reports")
    return queued_reports


def main():
    """Main execution."""
    logger.info("🚀 💩🎉TurdParty🎉💩 REAL WORKFLOW DATA SETUP")
    logger.info("Setting up workflow data for report generation")
    logger.info("=" * 80)
    
    try:
        # Step 1: Create tables
        create_workflow_tables()
        
        # Step 2: Load binary processing results
        results_data = load_binary_processing_results()
        
        # Step 3: Create workflow records
        workflows = create_workflow_records(results_data)
        
        # Step 4: Queue report generation
        queued_reports = queue_report_generation(workflows)
        
        # Step 5: Save setup results
        setup_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "workflows_created": len(workflows),
            "reports_queued": len(queued_reports),
            "workflows": workflows,
            "queued_reports": queued_reports
        }
        
        results_file = f"/tmp/workflow-setup-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(setup_results, f, indent=2)
        
        logger.info(f"\n📄 Setup results saved: {results_file}")
        
        # Summary
        logger.info("\n🎉 WORKFLOW DATA SETUP COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"✅ Workflows created: {len(workflows)}")
        logger.info(f"✅ Reports queued: {len(queued_reports)}")
        logger.info("\n📊 Ready for report generation!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Workflow setup failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
