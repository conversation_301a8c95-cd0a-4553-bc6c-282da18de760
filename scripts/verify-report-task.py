#!/usr/bin/env python3
"""
Verify Report Generation Task Import
Check if the report generation task can be imported without errors.
"""

import sys
from pathlib import Path

# Add project paths
sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))

print("🔍 VERIFYING REPORT GENERATION TASK IMPORT")
print("=" * 60)

try:
    print("📋 Step 1: Testing basic imports...")
    
    # Test basic imports first
    import os
    import time
    from datetime import datetime, timezone
    from typing import Dict, Any, Optional
    print("   ✅ Basic imports successful")
    
    # Test Celery imports
    from celery import shared_task
    from celery.utils.log import get_task_logger
    print("   ✅ Celery imports successful")
    
    # Test SQLAlchemy imports
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
    print("   ✅ SQLAlchemy imports successful")
    
    print("\n📋 Step 2: Testing task module imports...")
    
    # Test ELK integration import
    try:
        from tasks.elk_integration import stream_workflow_event
        print("   ✅ ELK integration import successful")
    except Exception as e:
        print(f"   ⚠️ ELK integration import failed: {e}")
    
    print("\n📋 Step 3: Testing report generation task import...")
    
    # Try to import the report generation task
    try:
        from tasks.report_generation import queue_report_generation, generate_analysis_report
        print("   ✅ Report generation tasks imported successfully")
        
        # Check if they are callable
        if callable(queue_report_generation) and callable(generate_analysis_report):
            print("   ✅ Report generation tasks are callable")
        else:
            print("   ❌ Report generation tasks are not callable")
            
    except Exception as e:
        print(f"   ❌ Report generation task import failed: {e}")
        print(f"   📝 Error details: {type(e).__name__}: {e}")
        
        # Try to identify the specific issue
        if "No module named" in str(e):
            print("   💡 This appears to be a module import issue")
        elif "database" in str(e):
            print("   💡 This appears to be a database import issue")
        elif "elk_integration" in str(e):
            print("   💡 This appears to be an ELK integration import issue")
    
    print("\n📋 Step 4: Testing Celery app registration...")
    
    try:
        from celery_app import app
        
        # Check if tasks are registered
        registered_tasks = list(app.tasks.keys())
        report_tasks = [task for task in registered_tasks if 'report_generation' in task]
        
        print(f"   📊 Total registered tasks: {len(registered_tasks)}")
        print(f"   📊 Report generation tasks: {len(report_tasks)}")
        
        if report_tasks:
            print("   ✅ Report generation tasks found in registry:")
            for task in report_tasks:
                print(f"      - {task}")
        else:
            print("   ❌ No report generation tasks found in registry")
            
            # Show some example registered tasks
            print("   📝 Example registered tasks:")
            for task in registered_tasks[:5]:
                print(f"      - {task}")
                
    except Exception as e:
        print(f"   ❌ Celery app import failed: {e}")
    
    print("\n🎯 VERIFICATION COMPLETE")
    
except Exception as e:
    print(f"❌ VERIFICATION FAILED: {e}")
    import traceback
    traceback.print_exc()
