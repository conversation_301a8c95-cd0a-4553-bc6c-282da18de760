#!/usr/bin/env python3
"""
Run 20 Windows Binaries Through Complete VM Workflow
Uses the existing TurdParty API to upload files and start VM workflows.
This is the PRODUCTION VM execution pipeline - not just file processing.
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List
import aiohttp
import aiofiles

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'/tmp/vm-workflow-{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class VMWorkflowRunner:
    """Run binaries through complete VM workflow using TurdParty API."""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000/api/v1"
        self.session = None
        self.results = {}
        
        # Use the same 20 binaries from our previous test
        self.binaries = {
            "notepadpp": {
                "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.6.2/npp.8.6.2.portable.x64.zip",
                "filename": "npp.8.6.2.portable.x64.zip",
                "description": "Notepad++ Portable - Text Editor"
            },
            "7zip": {
                "url": "https://www.7-zip.org/a/7z2301-x64.exe",
                "filename": "7z2301-x64.exe", 
                "description": "7-Zip File Archiver"
            },
            "putty": {
                "url": "https://the.earth.li/~sgtatham/putty/latest/w64/putty.exe",
                "filename": "putty.exe",
                "description": "PuTTY SSH Client"
            },
            "vlc": {
                "url": "https://get.videolan.org/vlc/3.0.20/win64/vlc-3.0.20-win64.exe",
                "filename": "vlc-3.0.20-win64.exe",
                "description": "VLC Media Player"
            },
            "firefox": {
                "url": "https://download.mozilla.org/?product=firefox-stub&os=win&lang=en-US",
                "filename": "firefox-installer.exe",
                "description": "Mozilla Firefox Browser Installer"
            },
            "chrome": {
                "url": "https://dl.google.com/chrome/install/ChromeStandaloneSetup64.exe",
                "filename": "ChromeStandaloneSetup64.exe",
                "description": "Google Chrome Browser"
            },
            "vscode": {
                "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64",
                "filename": "VSCodeSetup-x64.exe",
                "description": "Visual Studio Code"
            },
            "git": {
                "url": "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe",
                "filename": "Git-2.43.0-64-bit.exe",
                "description": "Git for Windows"
            },
            "nodejs": {
                "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
                "filename": "node-v20.10.0-x64.msi",
                "description": "Node.js Runtime"
            },
            "python": {
                "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
                "filename": "python-3.12.1-amd64.exe",
                "description": "Python Programming Language"
            },
            "wireshark": {
                "url": "https://2.na.dl.wireshark.org/win64/Wireshark-4.2.0-x64.exe",
                "filename": "Wireshark-4.2.0-x64.exe",
                "description": "Wireshark Network Analyzer"
            },
            "winrar": {
                "url": "https://www.rarlab.com/rar/winrar-x64-623.exe",
                "filename": "winrar-x64-623.exe",
                "description": "WinRAR Archive Manager"
            },
            "teamviewer": {
                "url": "https://download.teamviewer.com/download/TeamViewer_Setup_x64.exe",
                "filename": "TeamViewer_Setup_x64.exe",
                "description": "TeamViewer Remote Access"
            },
            "discord": {
                "url": "https://discord.com/api/downloads/distributions/app/installers/latest?channel=stable&platform=win&arch=x64",
                "filename": "DiscordSetup.exe",
                "description": "Discord Communication Platform"
            },
            "zoom": {
                "url": "https://zoom.us/client/5.16.10.668/ZoomInstaller.exe",
                "filename": "ZoomInstaller.exe",
                "description": "Zoom Video Conferencing"
            },
            "slack": {
                "url": "https://downloads.slack-edge.com/releases/windows/4.36.140/prod/x64/SlackSetup.exe",
                "filename": "SlackSetup.exe",
                "description": "Slack Team Communication"
            },
            "steam": {
                "url": "https://cdn.akamai.steamstatic.com/client/installer/SteamSetup.exe",
                "filename": "SteamSetup.exe",
                "description": "Steam Gaming Platform"
            },
            "spotify": {
                "url": "https://download.scdn.co/SpotifySetup.exe",
                "filename": "SpotifySetup.exe",
                "description": "Spotify Music Streaming"
            },
            "obs": {
                "url": "https://github.com/obsproject/obs-studio/releases/download/30.0.2/OBS-Studio-30.0.2-Full-Installer-x64.exe",
                "filename": "OBS-Studio-30.0.2-Full-Installer-x64.exe",
                "description": "OBS Studio Broadcasting Software"
            },
            "audacity": {
                "url": "https://github.com/audacity/audacity/releases/download/Audacity-3.4.2/audacity-win-3.4.2-x64.exe",
                "filename": "audacity-win-3.4.2-x64.exe",
                "description": "Audacity Audio Editor"
            }
        }
        
        # Create download directory
        self.download_dir = Path("/tmp/turdparty-vm-binaries")
        self.download_dir.mkdir(exist_ok=True)
        
    async def initialize_session(self):
        """Initialize HTTP session."""
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
        self.session = aiohttp.ClientSession(timeout=timeout)
        
    async def cleanup_session(self):
        """Cleanup HTTP session."""
        if self.session:
            await self.session.close()
    
    async def check_api_health(self) -> bool:
        """Check if the TurdParty API is available."""
        logger.info("🔍 Checking TurdParty API health...")
        
        try:
            async with self.session.get(f"{self.api_base_url}/../health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"   ✅ API is healthy: {health_data.get('status', 'unknown')}")
                    return True
                else:
                    logger.error(f"   ❌ API health check failed: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"   ❌ API health check failed: {e}")
            return False
    
    async def download_binary(self, binary_name: str, binary_info: Dict[str, Any]) -> Dict[str, Any]:
        """Download a binary file."""
        logger.info(f"📥 DOWNLOADING: {binary_name}")
        logger.info(f"   🌐 URL: {binary_info['url']}")
        
        file_path = self.download_dir / binary_info['filename']
        
        try:
            # Check if already downloaded
            if file_path.exists():
                file_size = file_path.stat().st_size
                logger.info(f"   📁 File already exists: {file_size:,} bytes")
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "file_size": file_size,
                    "binary_info": binary_info
                }
            
            # Download the binary
            logger.info(f"   🌐 Downloading...")
            async with self.session.get(binary_info['url']) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {response.reason}")
                
                total_size = int(response.headers.get('content-length', 0))
                logger.info(f"   📏 Size: {total_size:,} bytes ({total_size/1024/1024:.1f}MB)")
                
                downloaded = 0
                async with aiofiles.open(file_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                        downloaded += len(chunk)
                        
                        # Log progress every 10MB
                        if downloaded % (10 * 1024 * 1024) == 0:
                            progress = downloaded / total_size * 100 if total_size > 0 else 0
                            logger.info(f"   📊 Progress: {downloaded:,} bytes ({progress:.1f}%)")
                
                file_size = file_path.stat().st_size
                logger.info(f"   ✅ Download complete: {file_size:,} bytes")
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "file_size": file_size,
                    "binary_info": binary_info
                }
                
        except Exception as e:
            logger.error(f"   ❌ Download failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "binary_info": binary_info
            }
    
    async def upload_file_to_api(self, binary_name: str, file_path: str, description: str) -> Dict[str, Any]:
        """Upload file to TurdParty API."""
        logger.info(f"📤 UPLOADING TO API: {binary_name}")
        logger.info(f"   📁 File: {file_path}")
        
        try:
            # Prepare multipart form data
            data = aiohttp.FormData()
            data.add_field('description', description)
            
            # Add file
            async with aiofiles.open(file_path, 'rb') as f:
                file_content = await f.read()
                data.add_field('file', file_content, filename=Path(file_path).name)
            
            # Upload to API
            async with self.session.post(f"{self.api_base_url}/files/upload", data=data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.info(f"   ✅ Upload successful")
                    logger.info(f"   🆔 File ID: {result['file_id']}")
                    logger.info(f"   🔐 Hash: {result['file_hash'][:16]}...")
                    
                    return {
                        "success": True,
                        "file_id": result["file_id"],
                        "file_hash": result["file_hash"],
                        "file_size": result["file_size"],
                        "status": result["status"],
                        "api_response": result
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"   ❌ Upload failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def start_vm_workflow(self, binary_name: str, file_id: str, description: str) -> Dict[str, Any]:
        """Start VM workflow for uploaded file."""
        logger.info(f"🚀 STARTING VM WORKFLOW: {binary_name}")
        logger.info(f"   🆔 File ID: {file_id}")

        try:
            # Prepare workflow data
            data = aiohttp.FormData()
            data.add_field('file_id', file_id)
            data.add_field('vm_template', 'ubuntu/focal64')  # Use Ubuntu for Windows binary analysis
            data.add_field('vm_memory_mb', '2048')  # 2GB RAM
            data.add_field('vm_cpus', '2')  # 2 CPU cores
            data.add_field('injection_path', f'/tmp/analysis/{binary_name}')
            data.add_field('description', f'VM workflow for {description}')

            # Start workflow via API
            async with self.session.post(f"{self.api_base_url}/workflow/start", data=data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.info(f"   ✅ Workflow started successfully")
                    logger.info(f"   🔧 Workflow ID: {result['workflow_job_id']}")
                    logger.info(f"   📊 Status: {result['status']}")

                    return {
                        "success": True,
                        "workflow_job_id": result["workflow_job_id"],
                        "status": result["status"],
                        "api_response": result
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")

        except Exception as e:
            logger.error(f"   ❌ Workflow start failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def monitor_workflow_status(self, binary_name: str, workflow_job_id: str, max_wait_minutes: int = 35) -> Dict[str, Any]:
        """Monitor workflow status until completion or timeout."""
        logger.info(f"👁️ MONITORING WORKFLOW: {binary_name}")
        logger.info(f"   🔧 Workflow ID: {workflow_job_id}")
        logger.info(f"   ⏰ Max wait: {max_wait_minutes} minutes")

        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        check_interval = 30  # Check every 30 seconds

        try:
            while True:
                elapsed = time.time() - start_time

                if elapsed > max_wait_seconds:
                    logger.warning(f"   ⏰ Timeout reached ({max_wait_minutes} minutes)")
                    return {
                        "success": False,
                        "status": "timeout",
                        "elapsed_minutes": elapsed / 60,
                        "final_status": "unknown"
                    }

                # Check workflow status
                try:
                    async with self.session.get(f"{self.api_base_url}/workflow/{workflow_job_id}") as response:
                        if response.status == 200:
                            result = await response.json()
                            status = result.get("status", "unknown")
                            progress = result.get("progress", 0)
                            current_step = result.get("current_step", "unknown")

                            logger.info(f"   📊 Status: {status} | Progress: {progress}% | Step: {current_step}")

                            # Check if workflow is complete
                            if status in ["completed", "failed", "terminated"]:
                                logger.info(f"   🏁 Workflow finished: {status}")

                                return {
                                    "success": status == "completed",
                                    "status": status,
                                    "progress": progress,
                                    "current_step": current_step,
                                    "elapsed_minutes": elapsed / 60,
                                    "final_status": status,
                                    "api_response": result
                                }
                        else:
                            logger.warning(f"   ⚠️ Status check failed: HTTP {response.status}")

                except Exception as e:
                    logger.warning(f"   ⚠️ Status check error: {e}")

                # Wait before next check
                await asyncio.sleep(check_interval)

        except Exception as e:
            logger.error(f"   ❌ Monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "elapsed_minutes": (time.time() - start_time) / 60
            }

    async def process_single_binary(self, binary_name: str, binary_info: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single binary through the complete VM workflow."""
        logger.info(f"\n{'='*80}")
        logger.info(f"🔥 PROCESSING BINARY: {binary_name.upper()}")
        logger.info(f"📝 Description: {binary_info['description']}")
        logger.info(f"{'='*80}")

        start_time = time.time()
        result = {
            "binary_name": binary_name,
            "binary_info": binary_info,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "steps": {}
        }

        try:
            # Step 1: Download binary
            logger.info(f"\n📥 STEP 1: DOWNLOADING BINARY")
            download_result = await self.download_binary(binary_name, binary_info)
            result["steps"]["download"] = download_result

            if not download_result["success"]:
                logger.error(f"❌ Download failed, skipping {binary_name}")
                return result

            # Step 2: Upload to API
            logger.info(f"\n📤 STEP 2: UPLOADING TO TURDPARTY API")
            upload_result = await self.upload_file_to_api(
                binary_name,
                download_result["file_path"],
                binary_info["description"]
            )
            result["steps"]["upload"] = upload_result

            if not upload_result["success"]:
                logger.error(f"❌ Upload failed, skipping {binary_name}")
                return result

            # Step 3: Start VM workflow
            logger.info(f"\n🚀 STEP 3: STARTING VM WORKFLOW")
            workflow_result = await self.start_vm_workflow(
                binary_name,
                upload_result["file_id"],
                binary_info["description"]
            )
            result["steps"]["workflow_start"] = workflow_result

            if not workflow_result["success"]:
                logger.error(f"❌ Workflow start failed, skipping {binary_name}")
                return result

            # Step 4: Monitor workflow
            logger.info(f"\n👁️ STEP 4: MONITORING VM WORKFLOW (30+ minutes)")
            monitor_result = await self.monitor_workflow_status(
                binary_name,
                workflow_result["workflow_job_id"],
                max_wait_minutes=35  # Wait up to 35 minutes
            )
            result["steps"]["workflow_monitor"] = monitor_result

            # Calculate total time
            total_time = time.time() - start_time
            result["total_time_minutes"] = total_time / 60
            result["end_time"] = datetime.now(timezone.utc).isoformat()

            # Determine overall success
            result["success"] = all([
                download_result["success"],
                upload_result["success"],
                workflow_result["success"],
                monitor_result["success"]
            ])

            if result["success"]:
                logger.info(f"✅ {binary_name} COMPLETED SUCCESSFULLY in {total_time/60:.1f} minutes")
            else:
                logger.error(f"❌ {binary_name} FAILED after {total_time/60:.1f} minutes")

            return result

        except Exception as e:
            logger.error(f"❌ Processing failed for {binary_name}: {e}")
            result["error"] = str(e)
            result["success"] = False
            return result

    async def run_all_binaries_vm_workflow(self) -> Dict[str, Any]:
        """Run all 20 binaries through the complete VM workflow."""
        logger.info("🚀 STARTING 20 BINARIES VM WORKFLOW EXECUTION")
        logger.info("This will run each binary through the complete TurdParty pipeline:")
        logger.info("  📥 Download → 📤 Upload → 🚀 VM Creation → 💉 Injection → 👁️ Monitor → 📊 Report")
        logger.info("=" * 100)

        start_time = time.time()
        results = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "binaries_processed": {},
            "summary": {}
        }

        try:
            # Initialize session
            await self.initialize_session()

            # Check API health
            if not await self.check_api_health():
                raise Exception("TurdParty API is not available")

            # Process each binary
            successful_count = 0
            failed_count = 0

            for i, (binary_name, binary_info) in enumerate(self.binaries.items(), 1):
                logger.info(f"\n🎯 PROCESSING BINARY {i}/20: {binary_name}")

                try:
                    result = await self.process_single_binary(binary_name, binary_info)
                    results["binaries_processed"][binary_name] = result

                    if result["success"]:
                        successful_count += 1
                        logger.info(f"✅ Binary {i}/20 COMPLETED: {binary_name}")
                    else:
                        failed_count += 1
                        logger.error(f"❌ Binary {i}/20 FAILED: {binary_name}")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ Binary {i}/20 EXCEPTION: {binary_name} - {e}")
                    results["binaries_processed"][binary_name] = {
                        "binary_name": binary_name,
                        "success": False,
                        "error": str(e)
                    }

                # Log progress
                logger.info(f"📊 PROGRESS: {i}/20 processed | ✅ {successful_count} successful | ❌ {failed_count} failed")

                # Small delay between binaries to avoid overwhelming the system
                if i < 20:
                    logger.info("⏳ Waiting 30 seconds before next binary...")
                    await asyncio.sleep(30)

            # Calculate final statistics
            total_time = time.time() - start_time
            results["end_time"] = datetime.now(timezone.utc).isoformat()
            results["total_time_hours"] = total_time / 3600
            results["summary"] = {
                "total_binaries": 20,
                "successful": successful_count,
                "failed": failed_count,
                "success_rate": (successful_count / 20) * 100,
                "total_time_hours": total_time / 3600,
                "average_time_per_binary_minutes": (total_time / 60) / 20
            }

            # Log final summary
            logger.info(f"\n{'='*100}")
            logger.info("🎉 20 BINARIES VM WORKFLOW EXECUTION COMPLETE!")
            logger.info(f"{'='*100}")
            logger.info(f"📊 FINAL STATISTICS:")
            logger.info(f"   🎯 Total Binaries: 20")
            logger.info(f"   ✅ Successful: {successful_count}")
            logger.info(f"   ❌ Failed: {failed_count}")
            logger.info(f"   📈 Success Rate: {(successful_count/20)*100:.1f}%")
            logger.info(f"   ⏰ Total Time: {total_time/3600:.1f} hours")
            logger.info(f"   ⏱️ Average per Binary: {(total_time/60)/20:.1f} minutes")

            # Save results
            results_file = f"/tmp/vm-workflow-results-{int(time.time())}.json"
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"📄 Detailed results saved: {results_file}")

            return results

        except Exception as e:
            logger.error(f"❌ VM workflow execution failed: {e}")
            results["error"] = str(e)
            results["success"] = False
            return results

        finally:
            await self.cleanup_session()


async def main():
    """Main entry point for VM workflow execution."""
    logger.info("🚀 💩🎉TurdParty🎉💩 - 20 BINARIES VM WORKFLOW EXECUTION")
    logger.info("Running 20 Windows binaries through complete VM analysis pipeline")
    logger.info("This includes REAL VM creation, file injection, and 30-minute monitoring")
    logger.info("=" * 100)

    runner = VMWorkflowRunner()
    results = await runner.run_all_binaries_vm_workflow()

    if results.get("error"):
        logger.error(f"❌ Execution failed: {results['error']}")
        return False

    success_rate = results.get("summary", {}).get("success_rate", 0)
    if success_rate >= 80:
        logger.info("🎉 EXCELLENT! 80%+ success rate achieved!")
        return True
    elif success_rate >= 60:
        logger.info("✅ GOOD! 60%+ success rate achieved!")
        return True
    else:
        logger.warning("⚠️ LOW SUCCESS RATE - Check system configuration")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        logger.info("✅ VM workflow execution completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ VM workflow execution failed!")
        sys.exit(1)
