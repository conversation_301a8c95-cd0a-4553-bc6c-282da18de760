#!/usr/bin/env python3
"""
Parallel 20 Binary VM Workflow Test
Test the complete pipeline with parallel execution using Celery workers.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

import aiohttp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ParallelWorkflowTester:
    """Parallel workflow tester using Celery workers."""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000/api/v1"
        self.session = None
        self.results = {}
        
        # Full test binaries for comprehensive testing
        self.test_binaries = [
            {
                "name": "notepadpp",
                "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.6.2/npp.8.6.2.Installer.x64.exe",
                "expected_size_mb": 4
            },
            {
                "name": "7zip", 
                "url": "https://www.7-zip.org/a/7z2301-x64.exe",
                "expected_size_mb": 1
            },
            {
                "name": "vlc",
                "url": "https://get.videolan.org/vlc/3.0.20/win64/vlc-3.0.20-win64.exe",
                "expected_size_mb": 40
            },
            {
                "name": "firefox",
                "url": "https://download.mozilla.org/?product=firefox-latest&os=win64&lang=en-US",
                "expected_size_mb": 55
            },
            {
                "name": "chrome",
                "url": "https://dl.google.com/chrome/install/latest/chrome_installer.exe",
                "expected_size_mb": 1
            },
            {
                "name": "vscode",
                "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64",
                "expected_size_mb": 90
            },
            {
                "name": "git",
                "url": "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe",
                "expected_size_mb": 50
            },
            {
                "name": "nodejs",
                "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
                "expected_size_mb": 30
            },
            {
                "name": "python",
                "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
                "expected_size_mb": 25
            },
            {
                "name": "wireshark",
                "url": "https://2.na.dl.wireshark.org/win64/Wireshark-4.2.0-x64.exe",
                "expected_size_mb": 60
            }
        ]
    
    async def __aenter__(self):
        """Async context manager entry."""
        timeout = aiohttp.ClientTimeout(total=600)  # 10 minute timeout
        connector = aiohttp.TCPConnector(limit=20, limit_per_host=10)  # Allow more concurrent connections
        self.session = aiohttp.ClientSession(timeout=timeout, connector=connector)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def verify_api_health(self) -> bool:
        """Verify API is healthy."""
        try:
            async with self.session.get("http://localhost:8000/health/") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"✅ API Health: {health_data.get('status')}")
                    return True
                else:
                    logger.error(f"❌ API health check failed: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ API health check error: {e}")
            return False
    
    async def upload_binary(self, binary_info: Dict[str, Any]) -> Dict[str, Any]:
        """Upload a binary and return upload result."""
        name = binary_info["name"]
        url = binary_info["url"]
        
        logger.info(f"📤 Uploading {name}...")
        
        try:
            # Download binary
            logger.info(f"   🔄 Downloading from {url}")
            async with self.session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"Download failed: HTTP {response.status}")
                
                binary_data = await response.read()
                file_size = len(binary_data)
                
                logger.info(f"   📏 Downloaded {file_size / 1024 / 1024:.1f} MB")
            
            # Upload to API
            logger.info(f"   📤 Uploading to TurdParty API...")
            
            data = aiohttp.FormData()
            data.add_field('description', f'Parallel test of {name}')
            data.add_field('file', binary_data, filename=f"{name}.exe")
            
            async with self.session.post(f"{self.api_base_url}/files/upload", data=data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    
                    logger.info(f"   ✅ Upload successful: {result.get('file_id')}")
                    
                    return {
                        "success": True,
                        "name": name,
                        "file_id": result.get("file_id"),
                        "file_size": file_size,
                        "upload_time": time.time()
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Upload failed: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"   ❌ Upload failed for {name}: {e}")
            return {
                "success": False,
                "name": name,
                "error": str(e)
            }
    
    async def trigger_vm_workflow(self, file_id: str, binary_name: str) -> Dict[str, Any]:
        """Trigger VM workflow for uploaded file."""
        logger.info(f"🚀 Triggering VM workflow for {binary_name}...")
        
        try:
            # Use Form data as expected by the API
            form_data = aiohttp.FormData()
            form_data.add_field('file_id', file_id)
            form_data.add_field('vm_template', 'ubuntu/focal64')
            form_data.add_field('vm_memory_mb', '2048')
            form_data.add_field('vm_cpus', '2')
            form_data.add_field('injection_path', f'/tmp/analysis/{binary_name}')
            form_data.add_field('description', f'Parallel analysis of {binary_name}')
            
            async with self.session.post(
                f"{self.api_base_url}/workflow/start",
                data=form_data
            ) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    workflow_id = result.get("workflow_job_id")
                    
                    logger.info(f"   ✅ Workflow started: {workflow_id}")
                    
                    return {
                        "success": True,
                        "name": binary_name,
                        "workflow_id": workflow_id,
                        "file_id": file_id,
                        "start_time": time.time()
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Workflow start failed: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"   ❌ Workflow trigger failed for {binary_name}: {e}")
            return {
                "success": False,
                "name": binary_name,
                "error": str(e)
            }
    
    async def monitor_single_workflow(self, workflow_info: Dict[str, Any], max_wait_minutes: int = 15) -> Dict[str, Any]:
        """Monitor a single workflow progress."""
        workflow_id = workflow_info["workflow_id"]
        binary_name = workflow_info["name"]
        
        logger.info(f"👀 Monitoring {binary_name} workflow: {workflow_id}")
        
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        key_milestones = {
            "vm_allocated": False,
            "file_injected": False,
            "execution_started": False,
            "files_exfiltrated": False,
            "completed": False
        }
        
        last_step = ""
        
        try:
            while time.time() - start_time < max_wait_seconds:
                async with self.session.get(f"{self.api_base_url}/workflow/{workflow_id}") as response:
                    if response.status == 200:
                        status = await response.json()
                        current_step = status.get("current_step", "")
                        progress = status.get("progress", 0)
                        workflow_status = status.get("status", "")
                        
                        # Log progress changes
                        if current_step != last_step:
                            logger.info(f"   🔄 {binary_name}: {current_step} ({progress}%)")
                            last_step = current_step
                        
                        # Check milestones
                        if "vm" in current_step.lower() and ("creat" in current_step.lower() or "alloc" in current_step.lower()):
                            key_milestones["vm_allocated"] = True
                        
                        if "inject" in current_step.lower():
                            key_milestones["file_injected"] = True
                        
                        if "execut" in current_step.lower() or "running" in current_step.lower():
                            key_milestones["execution_started"] = True
                        
                        if "exfiltrat" in current_step.lower() or "collect" in current_step.lower():
                            key_milestones["files_exfiltrated"] = True
                        
                        if workflow_status in ["COMPLETED", "SUCCESS"]:
                            key_milestones["completed"] = True
                            logger.info(f"   ✅ {binary_name} workflow completed!")
                            break
                        
                        if workflow_status in ["FAILED", "ERROR"]:
                            error_msg = status.get('error_message', 'Unknown error')
                            raise Exception(f"Workflow failed: {error_msg}")
                        
                        # If we have file exfiltration, that's our success criteria
                        if key_milestones["files_exfiltrated"]:
                            logger.info(f"   ✅ {binary_name} files exfiltrated - success!")
                            break
                    
                    else:
                        logger.warning(f"   ⚠️ {binary_name} status check failed: HTTP {response.status}")
                
                await asyncio.sleep(5)  # Check every 5 seconds
            
            return {
                "success": key_milestones["files_exfiltrated"] or key_milestones["completed"],
                "name": binary_name,
                "workflow_id": workflow_id,
                "milestones": key_milestones,
                "duration": time.time() - start_time,
                "final_step": last_step
            }
            
        except Exception as e:
            logger.error(f"   ❌ {binary_name} workflow monitoring failed: {e}")
            return {
                "success": False,
                "name": binary_name,
                "workflow_id": workflow_id,
                "error": str(e),
                "milestones": key_milestones,
                "duration": time.time() - start_time
            }
    
    async def run_parallel_test(self) -> Dict[str, Any]:
        """Run the complete parallel test."""
        logger.info("🚀 💩🎉TurdParty🎉💩 PARALLEL 20 BINARY TEST")
        logger.info("Testing complete pipeline with parallel Celery workers")
        logger.info("=" * 80)
        
        # Verify API health first
        if not await self.verify_api_health():
            return {"success": False, "error": "API health check failed"}
        
        results = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "phase": "upload",
            "binaries_tested": 0,
            "successful_uploads": 0,
            "successful_workflows": 0,
            "successful_completions": 0,
            "upload_results": [],
            "workflow_results": [],
            "monitoring_results": []
        }
        
        # PHASE 1: Parallel Upload
        logger.info("\n🔥 PHASE 1: PARALLEL UPLOAD")
        logger.info("=" * 60)
        
        upload_tasks = [
            self.upload_binary(binary_info) 
            for binary_info in self.test_binaries
        ]
        
        upload_results = await asyncio.gather(*upload_tasks, return_exceptions=True)
        
        successful_uploads = []
        for result in upload_results:
            if isinstance(result, dict) and result.get("success"):
                successful_uploads.append(result)
                results["successful_uploads"] += 1
                logger.info(f"✅ Upload success: {result['name']}")
            else:
                logger.error(f"❌ Upload failed: {result}")
        
        results["upload_results"] = upload_results
        results["binaries_tested"] = len(self.test_binaries)
        
        if not successful_uploads:
            logger.error("❌ No successful uploads - cannot proceed")
            return results
        
        # PHASE 2: Parallel Workflow Trigger
        logger.info(f"\n🔥 PHASE 2: PARALLEL WORKFLOW TRIGGER ({len(successful_uploads)} binaries)")
        logger.info("=" * 60)
        
        workflow_tasks = [
            self.trigger_vm_workflow(upload["file_id"], upload["name"])
            for upload in successful_uploads
        ]
        
        workflow_results = await asyncio.gather(*workflow_tasks, return_exceptions=True)
        
        successful_workflows = []
        for result in workflow_results:
            if isinstance(result, dict) and result.get("success"):
                successful_workflows.append(result)
                results["successful_workflows"] += 1
                logger.info(f"✅ Workflow started: {result['name']}")
            else:
                logger.error(f"❌ Workflow failed: {result}")
        
        results["workflow_results"] = workflow_results
        
        if not successful_workflows:
            logger.error("❌ No successful workflows - cannot proceed")
            return results
        
        # PHASE 3: Parallel Monitoring
        logger.info(f"\n🔥 PHASE 3: PARALLEL MONITORING ({len(successful_workflows)} workflows)")
        logger.info("=" * 60)
        
        monitoring_tasks = [
            self.monitor_single_workflow(workflow_info)
            for workflow_info in successful_workflows
        ]
        
        monitoring_results = await asyncio.gather(*monitoring_tasks, return_exceptions=True)
        
        successful_completions = 0
        for result in monitoring_results:
            if isinstance(result, dict) and result.get("success"):
                successful_completions += 1
                logger.info(f"✅ Workflow completed: {result['name']}")
            else:
                logger.error(f"❌ Workflow monitoring failed: {result}")
        
        results["monitoring_results"] = monitoring_results
        results["successful_completions"] = successful_completions
        results["end_time"] = datetime.now(timezone.utc).isoformat()
        results["success"] = successful_completions > 0
        
        return results


async def main():
    """Main execution."""
    async with ParallelWorkflowTester() as tester:
        results = await tester.run_parallel_test()
        
        # Save results
        results_file = f"/tmp/parallel-20-binary-test-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Summary
        logger.info("\n🎉 PARALLEL TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"✅ Binaries Tested: {results['binaries_tested']}")
        logger.info(f"✅ Successful Uploads: {results['successful_uploads']}")
        logger.info(f"✅ Successful Workflows: {results['successful_workflows']}")
        logger.info(f"✅ Successful Completions: {results['successful_completions']}")
        logger.info(f"📄 Results saved: {results_file}")
        
        # Detailed results
        if results.get("monitoring_results"):
            logger.info("\n📊 DETAILED RESULTS:")
            for result in results["monitoring_results"]:
                if isinstance(result, dict):
                    name = result.get("name", "unknown")
                    success = "✅" if result.get("success") else "❌"
                    duration = result.get("duration", 0)
                    logger.info(f"   {success} {name}: {duration:.1f}s")
        
        if results["success"]:
            logger.info("\n🎉 PARALLEL TEST PASSED!")
            logger.info("Complete pipeline is working with parallel execution!")
        else:
            logger.info("\n⚠️ PARALLEL TEST NEEDS ATTENTION")
            logger.info("Some workflows need fixing.")
        
        return results["success"]


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
