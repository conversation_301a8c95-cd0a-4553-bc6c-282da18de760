#!/usr/bin/env python3
"""
Simple Report Generation Queue Test
Test queuing report generation directly using Celery.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_simple_report_queue():
    """Test simple report generation queuing."""
    logger.info("🧪 TESTING SIMPLE REPORT GENERATION QUEUE")
    logger.info("=" * 60)
    
    try:
        # Test 1: Import Celery app
        logger.info("📋 TEST 1: Importing Celery app")
        sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
        
        from celery_app import app
        logger.info("   ✅ Celery app imported successfully")
        
        # Test 2: Check if report generation tasks are registered
        logger.info("📋 TEST 2: Checking task registration")
        
        registered_tasks = list(app.tasks.keys())
        report_tasks = [task for task in registered_tasks if 'report_generation' in task]
        
        logger.info(f"   📊 Found {len(report_tasks)} report generation tasks:")
        for task in report_tasks:
            logger.info(f"      - {task}")
        
        if len(report_tasks) >= 2:
            logger.info("   ✅ Report generation tasks properly registered")
        else:
            logger.warning("   ⚠️ Some report generation tasks may not be registered")
        
        # Test 3: Test direct task queuing
        logger.info("📋 TEST 3: Testing direct task queuing")
        
        # Create mock workflow data
        mock_workflow_id = f"test-workflow-{int(time.time())}"
        
        # Try to queue a report generation task directly
        try:
            # Use the registered task name
            queue_task_name = "services.workers.tasks.report_generation.queue_report_generation"
            
            if queue_task_name in registered_tasks:
                # Queue the task
                result = app.send_task(
                    queue_task_name,
                    args=[mock_workflow_id, 5],  # workflow_id, delay_seconds
                    queue='reports'
                )
                
                logger.info(f"   ✅ Report generation queued successfully: {result.id}")
                
                # Wait a moment and check task status
                await asyncio.sleep(2)
                task_status = result.status
                logger.info(f"   📊 Task status: {task_status}")
                
                return True
                
            else:
                logger.error(f"   ❌ Task not registered: {queue_task_name}")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ Failed to queue report generation: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Simple report queue test failed: {e}")
        return False


async def test_real_binary_report_queue():
    """Test queuing reports for the real binaries we processed."""
    logger.info("\n🔥 TESTING REAL BINARY REPORT QUEUE")
    logger.info("=" * 60)
    
    try:
        # Check for recent real binary processing results
        results_files = list(Path("/tmp").glob("real-binary-test-*.json"))
        
        if not results_files:
            logger.warning("   ⚠️ No recent real binary test results found")
            return False
        
        # Get the most recent results file
        latest_results = max(results_files, key=lambda p: p.stat().st_mtime)
        logger.info(f"   📄 Using results from: {latest_results}")
        
        with open(latest_results, 'r') as f:
            results_data = json.load(f)
        
        # Extract successful processing results
        processing_results = results_data.get("processing_results", {})
        upload_results = results_data.get("upload_results", {})
        
        successful_binaries = [
            binary_name for binary_name, result in processing_results.items()
            if result.get("success", False)
        ]
        
        logger.info(f"   📊 Found {len(successful_binaries)} successfully processed binaries")
        
        # Import Celery app
        sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
        from celery_app import app
        
        # Queue report generation for each successful binary
        queued_reports = []
        for binary_name in successful_binaries[:3]:  # Test with first 3 binaries
            try:
                file_uuid = upload_results[binary_name]["file_uuid"]
                
                # Create mock workflow ID
                workflow_id = f"real-binary-{binary_name}-{int(time.time())}"
                
                # Queue the report using direct task sending
                result = app.send_task(
                    "services.workers.tasks.report_generation.queue_report_generation",
                    args=[workflow_id, 10],  # workflow_id, delay_seconds
                    queue='reports'
                )
                
                queued_reports.append({
                    "binary_name": binary_name,
                    "file_uuid": file_uuid,
                    "workflow_id": workflow_id,
                    "task_id": result.id
                })
                
                logger.info(f"   ✅ Queued report for {binary_name}: {result.id}")
                
            except Exception as e:
                logger.error(f"   ❌ Failed to queue report for {binary_name}: {e}")
        
        logger.info(f"\n📊 SUMMARY: {len(queued_reports)} reports queued successfully")
        
        # Save the queued report information
        queue_info_file = f"/tmp/queued-reports-{int(time.time())}.json"
        with open(queue_info_file, 'w') as f:
            json.dump(queued_reports, f, indent=2)
        
        logger.info(f"📄 Queued report info saved: {queue_info_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real binary report queue test failed: {e}")
        return False


async def test_celery_worker_status():
    """Test if Celery workers are running and can handle report tasks."""
    logger.info("\n🔧 TESTING CELERY WORKER STATUS")
    logger.info("=" * 60)
    
    try:
        # Import Celery app
        sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "workers"))
        from celery_app import app
        
        # Check active workers
        logger.info("📋 Checking active workers...")
        
        try:
            # Get worker stats
            inspect = app.control.inspect()
            stats = inspect.stats()
            
            if stats:
                logger.info(f"   ✅ Found {len(stats)} active workers:")
                for worker_name, worker_stats in stats.items():
                    logger.info(f"      - {worker_name}: {worker_stats.get('pool', {}).get('max-concurrency', 'unknown')} processes")
            else:
                logger.warning("   ⚠️ No active workers found")
                return False
            
            # Check queues
            active_queues = inspect.active_queues()
            if active_queues:
                logger.info("   📊 Active queues:")
                for worker_name, queues in active_queues.items():
                    queue_names = [q['name'] for q in queues]
                    logger.info(f"      - {worker_name}: {', '.join(queue_names)}")
                    
                    # Check if reports queue is available
                    if 'reports' in queue_names:
                        logger.info("   ✅ Reports queue is active")
                    else:
                        logger.warning("   ⚠️ Reports queue not found")
            
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Failed to check worker status: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Celery worker status test failed: {e}")
        return False


async def main():
    """Main test execution."""
    logger.info("🚀 SIMPLE REPORT GENERATION TESTING")
    logger.info("Testing report generation queuing without complex imports")
    logger.info("=" * 80)
    
    # Run tests
    simple_queue_success = await test_simple_report_queue()
    worker_status_success = await test_celery_worker_status()
    real_binary_success = await test_real_binary_report_queue()
    
    # Summary
    logger.info("\n📊 TEST SUMMARY")
    logger.info("=" * 40)
    logger.info(f"Simple Queue Test: {'✅ PASSED' if simple_queue_success else '❌ FAILED'}")
    logger.info(f"Worker Status Test: {'✅ PASSED' if worker_status_success else '❌ FAILED'}")
    logger.info(f"Real Binary Test: {'✅ PASSED' if real_binary_success else '❌ FAILED'}")
    
    overall_success = simple_queue_success and worker_status_success and real_binary_success
    logger.info(f"Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n🎉 REPORT GENERATION QUEUING IS WORKING!")
        logger.info("Reports will be automatically generated after workflow completion.")
    else:
        logger.info("\n⚠️ REPORT GENERATION NEEDS ATTENTION")
        logger.info("Check Celery workers and task registration.")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
