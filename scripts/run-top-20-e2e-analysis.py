#!/usr/bin/env python3
"""
Top 20 Windows Binaries End-to-End Analysis Runner
Comprehensive test runner for executing binary analysis on the top 20 Windows binaries.
"""

import asyncio
import json
import time
import sys
from datetime import datetime
from pathlib import Path
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Top20E2EAnalysisRunner:
    """Comprehensive end-to-end analysis runner for top 20 Windows binaries."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.results = {}
        self.total_binaries = 20
        self.services_status = {}
        
    def check_prerequisites(self):
        """Check if all required services are running."""
        logger.info("🔍 Checking prerequisites...")
        
        required_services = [
            "http://api.turdparty.localhost/health",
            "http://elasticsearch.turdparty.localhost/_cluster/health",
            "http://kibana.turdparty.localhost/api/status",
            "http://minio.turdparty.localhost/minio/health/live"
        ]
        
        for service_url in required_services:
            try:
                import requests
                response = requests.get(service_url, timeout=10)
                service_name = service_url.split("//")[1].split(".")[0]
                
                if response.status_code == 200:
                    self.services_status[service_name] = "✅ Running"
                    logger.info(f"   ✅ {service_name}: Running")
                else:
                    self.services_status[service_name] = f"❌ Error ({response.status_code})"
                    logger.error(f"   ❌ {service_name}: Error ({response.status_code})")
                    
            except Exception as e:
                service_name = service_url.split("//")[1].split(".")[0]
                self.services_status[service_name] = f"❌ Unreachable ({str(e)})"
                logger.error(f"   ❌ {service_name}: Unreachable ({str(e)})")
        
        # Check if all services are running
        failed_services = [name for name, status in self.services_status.items() if "❌" in status]
        if failed_services:
            logger.error(f"❌ Failed services: {', '.join(failed_services)}")
            return False
        
        logger.info("✅ All prerequisites met!")
        return True
    
    def run_ecs_data_generation(self):
        """Run ECS data generation for all top 20 binaries."""
        logger.info("📊 Step 1: Running ECS data generation...")
        
        try:
            cmd = ["python", "scripts/test-top-20-binaries.py"]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ ECS data generation completed successfully")
                self.results['ecs_generation'] = {
                    'success': True,
                    'output': result.stdout,
                    'duration': time.time() - time.time()  # Will be updated properly
                }
                return True
            else:
                logger.error(f"❌ ECS data generation failed: {result.stderr}")
                self.results['ecs_generation'] = {
                    'success': False,
                    'error': result.stderr,
                    'output': result.stdout
                }
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ ECS data generation timed out after 30 minutes")
            self.results['ecs_generation'] = {
                'success': False,
                'error': 'Timeout after 30 minutes'
            }
            return False
        except Exception as e:
            logger.error(f"❌ ECS data generation failed with exception: {e}")
            self.results['ecs_generation'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def run_real_binary_tests(self):
        """Run real binary download and execution tests."""
        logger.info("🔄 Step 2: Running real binary tests...")
        
        try:
            cmd = ["python", "scripts/test-windows-dev-binaries.py"]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 60 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ Real binary tests completed successfully")
                self.results['real_binary_tests'] = {
                    'success': True,
                    'output': result.stdout,
                    'duration': time.time() - time.time()  # Will be updated properly
                }
                return True
            else:
                logger.error(f"❌ Real binary tests failed: {result.stderr}")
                self.results['real_binary_tests'] = {
                    'success': False,
                    'error': result.stderr,
                    'output': result.stdout
                }
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Real binary tests timed out after 60 minutes")
            self.results['real_binary_tests'] = {
                'success': False,
                'error': 'Timeout after 60 minutes'
            }
            return False
        except Exception as e:
            logger.error(f"❌ Real binary tests failed with exception: {e}")
            self.results['real_binary_tests'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def generate_comprehensive_reports(self):
        """Generate comprehensive reports for all analyzed binaries."""
        logger.info("📋 Step 3: Generating comprehensive reports...")
        
        try:
            # Wait for data indexing
            logger.info("⏳ Waiting for Elasticsearch indexing...")
            time.sleep(30)
            
            cmd = ["python", "scripts/generate-sphinx-reports.py"]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1200  # 20 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ Report generation completed successfully")
                self.results['report_generation'] = {
                    'success': True,
                    'output': result.stdout,
                    'duration': time.time() - time.time()  # Will be updated properly
                }
                return True
            else:
                logger.error(f"❌ Report generation failed: {result.stderr}")
                self.results['report_generation'] = {
                    'success': False,
                    'error': result.stderr,
                    'output': result.stdout
                }
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Report generation timed out after 20 minutes")
            self.results['report_generation'] = {
                'success': False,
                'error': 'Timeout after 20 minutes'
            }
            return False
        except Exception as e:
            logger.error(f"❌ Report generation failed with exception: {e}")
            self.results['report_generation'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def run_performance_validation(self):
        """Run performance validation tests."""
        logger.info("⚡ Step 4: Running performance validation...")
        
        try:
            cmd = ["python", "tests/performance/run_benchmarks.py"]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ Performance validation completed successfully")
                self.results['performance_validation'] = {
                    'success': True,
                    'output': result.stdout,
                    'duration': time.time() - time.time()  # Will be updated properly
                }
                return True
            else:
                logger.error(f"❌ Performance validation failed: {result.stderr}")
                self.results['performance_validation'] = {
                    'success': False,
                    'error': result.stderr,
                    'output': result.stdout
                }
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Performance validation timed out after 10 minutes")
            self.results['performance_validation'] = {
                'success': False,
                'error': 'Timeout after 10 minutes'
            }
            return False
        except Exception as e:
            logger.error(f"❌ Performance validation failed with exception: {e}")
            self.results['performance_validation'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def generate_final_summary(self):
        """Generate final comprehensive summary."""
        total_time = (datetime.now() - self.start_time).total_seconds()
        
        logger.info(f"\n{'='*80}")
        logger.info("📊 TOP 20 WINDOWS BINARIES E2E ANALYSIS SUMMARY")
        logger.info(f"{'='*80}")
        
        logger.info(f"\n⏱️ TOTAL EXECUTION TIME: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        
        # Step Results
        logger.info(f"\n📋 STEP RESULTS:")
        successful_steps = 0
        total_steps = 4
        
        for step_name, result in self.results.items():
            if result.get('success'):
                successful_steps += 1
                logger.info(f"   ✅ {step_name.replace('_', ' ').title()}: Success")
            else:
                logger.info(f"   ❌ {step_name.replace('_', ' ').title()}: Failed")
        
        # Success Rate
        success_rate = (successful_steps / total_steps) * 100
        logger.info(f"\n🎯 SUCCESS RATE: {success_rate:.1f}% ({successful_steps}/{total_steps} steps)")
        
        if success_rate >= 90:
            logger.info("   🎉 EXCELLENT: E2E analysis pipeline performing exceptionally well!")
        elif success_rate >= 70:
            logger.info("   ✅ GOOD: E2E analysis pipeline performing well with minor issues")
        else:
            logger.info("   ⚠️ NEEDS ATTENTION: E2E analysis pipeline requires optimization")
        
        # Access URLs
        logger.info(f"\n🌐 ACCESS URLS:")
        logger.info(f"   📊 Reports Platform: http://reports.turdparty.localhost")
        logger.info(f"   🔍 Kibana Dashboards: http://kibana.turdparty.localhost/app/dashboards")
        logger.info(f"   📋 API Documentation: http://api.turdparty.localhost/docs")
        logger.info(f"   📈 Status Dashboard: http://status.turdparty.localhost")
        
        # Save results
        results_file = f"/tmp/top-20-e2e-analysis-{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump({
                'test_metadata': {
                    'start_time': self.start_time.isoformat(),
                    'end_time': datetime.now().isoformat(),
                    'total_duration_seconds': total_time,
                    'total_binaries': self.total_binaries,
                    'services_status': self.services_status
                },
                'step_results': self.results,
                'summary': {
                    'successful_steps': successful_steps,
                    'total_steps': total_steps,
                    'success_rate': success_rate
                }
            }, f, indent=2, default=str)
        
        logger.info(f"\n📄 Results saved: {results_file}")
        logger.info(f"\n🎉 Top 20 Windows binaries E2E analysis complete!")
        
        return success_rate >= 70  # Return True if success rate is acceptable

def main():
    """Main entry point."""
    logger.info("🚀 Starting Top 20 Windows Binaries E2E Analysis")
    logger.info(f"📅 Started at: {datetime.now().isoformat()}")
    
    runner = Top20E2EAnalysisRunner()
    
    # Step 0: Check prerequisites
    if not runner.check_prerequisites():
        logger.error("❌ Prerequisites not met. Exiting.")
        sys.exit(1)
    
    # Step 1: Run ECS data generation
    if not runner.run_ecs_data_generation():
        logger.error("❌ ECS data generation failed. Continuing with other steps...")
    
    # Step 2: Run real binary tests
    if not runner.run_real_binary_tests():
        logger.error("❌ Real binary tests failed. Continuing with other steps...")
    
    # Step 3: Generate comprehensive reports
    if not runner.generate_comprehensive_reports():
        logger.error("❌ Report generation failed. Continuing with other steps...")
    
    # Step 4: Run performance validation
    if not runner.run_performance_validation():
        logger.error("❌ Performance validation failed. Continuing with final summary...")
    
    # Generate final summary
    success = runner.generate_final_summary()
    
    if success:
        logger.info("✅ E2E analysis completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ E2E analysis completed with issues!")
        sys.exit(1)

if __name__ == "__main__":
    main()
