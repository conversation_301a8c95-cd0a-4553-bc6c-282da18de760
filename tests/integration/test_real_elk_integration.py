"""
Real ELK integration tests for TurdParty.

Tests actual Elasticsearch integration without mocks.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
from datetime import datetime, timezone
import uuid
import os

import pytest
from elasticsearch import AsyncElasticsearch

from services.api.src.services.elk_logger import E<PERSON><PERSON><PERSON>ogger
from services.api.src.models.file_injection import FileInjectionCreate, InjectionStatus

# Import real fixtures
pytest_plugins = ["tests.integration.conftest_real"]


@pytest.mark.integration
@pytest.mark.asyncio
class TestRealELKIntegration:
    """Real ELK integration tests using actual Elasticsearch."""

    async def test_real_elasticsearch_connection(
        self, 
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test real Elasticsearch connection."""
        # Test basic connection
        info = await real_elasticsearch_client.info()
        assert "cluster_name" in info
        assert "version" in info
        
        # Test cluster health
        health = await real_elasticsearch_client.cluster.health()
        assert health["status"] in ["green", "yellow", "red"]

    async def test_real_file_injection_logging(
        self,
        real_elk_logger: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test real file injection event logging to Elasticsearch."""
        # Create test injection data
        injection_data = FileInjectionCreate(
            file_uuid=str(uuid.uuid4()),
            vm_id=f"test-vm-{uuid.uuid4().hex[:8]}",
            injection_path="/tmp/test_file.exe",
            permissions="0755"
        )
        
        # Log the injection event
        await real_elk_logger.log_file_injection_event(
            injection_id=f"inj-{uuid.uuid4().hex[:8]}",
            file_uuid=injection_data.file_uuid,
            vm_id=injection_data.vm_id,
            status=InjectionStatus.QUEUED,
            details={"test": "real_integration"}
        )
        
        # Wait for indexing
        await asyncio.sleep(2)
        
        # Verify the event was logged
        index_pattern = f"turdparty-file-injections-{datetime.now().strftime('%Y.%m')}"
        
        # Search for our logged event
        search_result = await real_elasticsearch_client.search(
            index=index_pattern,
            body={
                "query": {
                    "term": {
                        "file_uuid.keyword": injection_data.file_uuid
                    }
                }
            }
        )
        
        assert search_result["hits"]["total"]["value"] >= 1
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        
        assert source["file_uuid"] == injection_data.file_uuid
        assert source["vm_id"] == injection_data.vm_id
        assert source["status"] == InjectionStatus.QUEUED.value
        assert source["details"]["test"] == "real_integration"

    async def test_real_installation_base_logging(
        self,
        real_elk_logger: ELKLogger,
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test real installation base event logging."""
        test_uuid = str(uuid.uuid4())
        
        # Log installation base event
        await real_elk_logger.log_installation_base(
            file_uuid=test_uuid,
            event_type="file_created",
            file_path="/test/path/file.exe",
            details={
                "size": 1024,
                "permissions": "0755",
                "test_marker": "real_integration"
            }
        )
        
        # Wait for indexing
        await asyncio.sleep(2)
        
        # Verify the event was logged
        index_pattern = f"turdparty-installation-base-{datetime.now().strftime('%Y.%m')}"
        
        search_result = await real_elasticsearch_client.search(
            index=index_pattern,
            body={
                "query": {
                    "term": {
                        "file_uuid.keyword": test_uuid
                    }
                }
            }
        )
        
        assert search_result["hits"]["total"]["value"] >= 1
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        
        assert source["file_uuid"] == test_uuid
        assert source["event_type"] == "file_created"
        assert source["file_path"] == "/test/path/file.exe"
        assert source["details"]["test_marker"] == "real_integration"

    async def test_real_system_event_logging(
        self,
        real_elk_logger: ELKLogger,
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test real system event logging."""
        test_uuid = str(uuid.uuid4())
        
        # Log system event
        await real_elk_logger.log_system_event(
            file_uuid=test_uuid,
            event_type="process_started",
            process_name="test_process.exe",
            details={
                "pid": 1234,
                "command_line": "test_process.exe --arg1 --arg2",
                "test_marker": "real_integration"
            }
        )
        
        # Wait for indexing
        await asyncio.sleep(2)
        
        # Verify the event was logged
        index_pattern = f"turdparty-system-events-{datetime.now().strftime('%Y.%m')}"
        
        search_result = await real_elasticsearch_client.search(
            index=index_pattern,
            body={
                "query": {
                    "term": {
                        "file_uuid.keyword": test_uuid
                    }
                }
            }
        )
        
        assert search_result["hits"]["total"]["value"] >= 1
        hit = search_result["hits"]["hits"][0]
        source = hit["_source"]
        
        assert source["file_uuid"] == test_uuid
        assert source["event_type"] == "process_started"
        assert source["process_name"] == "test_process.exe"
        assert source["details"]["test_marker"] == "real_integration"

    async def test_real_bulk_logging_performance(
        self,
        real_elk_logger: ELKLogger,
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test real bulk logging performance."""
        test_uuid = str(uuid.uuid4())
        num_events = 50
        
        # Log multiple events quickly
        start_time = datetime.now()
        
        tasks = []
        for i in range(num_events):
            task = real_elk_logger.log_system_event(
                file_uuid=test_uuid,
                event_type="bulk_test_event",
                process_name=f"bulk_process_{i}.exe",
                details={
                    "sequence": i,
                    "test_marker": "bulk_performance"
                }
            )
            tasks.append(task)
        
        # Wait for all events to be logged
        await asyncio.gather(*tasks)
        end_time = datetime.now()
        
        # Calculate performance
        duration = (end_time - start_time).total_seconds()
        events_per_second = num_events / duration
        
        # Should be able to log at least 10 events per second
        assert events_per_second >= 10, f"Performance too slow: {events_per_second:.2f} events/sec"
        
        # Wait for indexing
        await asyncio.sleep(3)
        
        # Verify all events were logged
        index_pattern = f"turdparty-system-events-{datetime.now().strftime('%Y.%m')}"
        
        search_result = await real_elasticsearch_client.search(
            index=index_pattern,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"file_uuid.keyword": test_uuid}},
                            {"term": {"details.test_marker.keyword": "bulk_performance"}}
                        ]
                    }
                },
                "size": num_events
            }
        )
        
        assert search_result["hits"]["total"]["value"] == num_events

    async def test_real_index_creation_and_mapping(
        self,
        real_elasticsearch_client: AsyncElasticsearch
    ) -> None:
        """Test that indices are created with proper mappings."""
        current_month = datetime.now().strftime('%Y.%m')
        expected_indices = [
            f"turdparty-file-injections-{current_month}",
            f"turdparty-installation-base-{current_month}",
            f"turdparty-system-events-{current_month}"
        ]
        
        for index_name in expected_indices:
            # Check if index exists
            exists = await real_elasticsearch_client.indices.exists(index=index_name)
            
            if exists:
                # Get mapping
                mapping = await real_elasticsearch_client.indices.get_mapping(index=index_name)
                
                # Verify basic structure
                assert index_name in mapping
                properties = mapping[index_name]["mappings"].get("properties", {})
                
                # Common fields should exist
                assert "timestamp" in properties
                assert "file_uuid" in properties
                assert "event_type" in properties or "status" in properties

    async def test_real_error_handling(
        self,
        real_elk_logger: ELKLogger
    ) -> None:
        """Test real error handling in ELK logging."""
        # Test with invalid data that should be handled gracefully
        try:
            await real_elk_logger.log_system_event(
                file_uuid="invalid-uuid-format",  # Invalid UUID format
                event_type="test_error_handling",
                process_name="error_test.exe",
                details={"test": "error_handling"}
            )
            # Should not raise an exception
        except Exception as e:
            pytest.fail(f"ELK logger should handle errors gracefully: {e}")
