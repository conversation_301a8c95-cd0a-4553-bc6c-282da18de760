"""
Real integration test configuration for TurdParty.

This module provides real service implementations for integration testing,
replacing mocks with actual service connections. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
import os
import time
from pathlib import Path
from typing import AsyncGenerator, Generator
import uuid

import pytest
import docker
import redis
from elasticsearch import AsyncElasticsearch
from minio import Minio

from services.api.src.services.elk_logger import ELKLogger
from services.api.src.services.file_injection_service import FileInjectionService


@pytest.fixture(scope="session")
def real_docker_client() -> Generator[docker.DockerClient, None, None]:
    """Provide a real Docker client for integration testing."""
    try:
        client = docker.from_env()
        # Test connection
        client.ping()
        yield client
    except Exception as e:
        pytest.skip(f"Docker not available: {e}")
    finally:
        if 'client' in locals():
            client.close()


@pytest.fixture(scope="session")
def real_redis_client() -> Generator[redis.Redis, None, None]:
    """Provide a real Redis client for integration testing."""
    redis_host = os.getenv("REDIS_HOST", "redis.turdparty.localhost")
    redis_port = int(os.getenv("REDIS_PORT", "6379"))
    
    try:
        client = redis.Redis(
            host=redis_host,
            port=redis_port,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        # Test connection
        client.ping()
        yield client
    except Exception as e:
        pytest.skip(f"Redis not available at {redis_host}:{redis_port}: {e}")
    finally:
        if 'client' in locals():
            client.close()


@pytest.fixture(scope="session")
async def real_elasticsearch_client() -> AsyncGenerator[AsyncElasticsearch, None]:
    """Provide a real Elasticsearch client for integration testing."""
    es_host = os.getenv("ELASTICSEARCH_HOST", "elasticsearch.turdparty.localhost")
    es_port = int(os.getenv("ELASTICSEARCH_PORT", "9200"))
    
    try:
        client = AsyncElasticsearch(
            hosts=[{"host": es_host, "port": es_port}],
            timeout=10,
            max_retries=3,
            retry_on_timeout=True
        )
        # Test connection
        await client.info()
        yield client
    except Exception as e:
        pytest.skip(f"Elasticsearch not available at {es_host}:{es_port}: {e}")
    finally:
        if 'client' in locals():
            await client.close()


@pytest.fixture(scope="session")
def real_minio_client() -> Generator[Minio, None, None]:
    """Provide a real MinIO client for integration testing."""
    minio_host = os.getenv("MINIO_HOST", "minio.turdparty.localhost")
    minio_port = int(os.getenv("MINIO_PORT", "9000"))
    minio_access_key = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    minio_secret_key = os.getenv("MINIO_SECRET_KEY", "minioadmin")
    
    try:
        client = Minio(
            f"{minio_host}:{minio_port}",
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=False
        )
        # Test connection by listing buckets
        list(client.list_buckets())
        yield client
    except Exception as e:
        pytest.skip(f"MinIO not available at {minio_host}:{minio_port}: {e}")


@pytest.fixture
async def real_elk_logger(real_elasticsearch_client: AsyncElasticsearch) -> AsyncGenerator[ELKLogger, None]:
    """Provide a real ELK logger for integration testing."""
    logger = ELKLogger(es_client=real_elasticsearch_client)
    yield logger


@pytest.fixture
async def real_file_injection_service(
    real_elk_logger: ELKLogger,
    real_minio_client: Minio,
    real_redis_client: redis.Redis
) -> AsyncGenerator[FileInjectionService, None]:
    """Provide a real file injection service for integration testing."""
    service = FileInjectionService(
        elk_logger=real_elk_logger,
        minio_client=real_minio_client,
        redis_client=real_redis_client
    )
    yield service


@pytest.fixture
def real_test_bucket(real_minio_client: Minio) -> Generator[str, None, None]:
    """Create a real test bucket in MinIO."""
    bucket_name = f"test-bucket-{uuid.uuid4().hex[:8]}"
    
    try:
        # Create bucket
        real_minio_client.make_bucket(bucket_name)
        yield bucket_name
    finally:
        # Cleanup: remove all objects and bucket
        try:
            objects = real_minio_client.list_objects(bucket_name, recursive=True)
            for obj in objects:
                real_minio_client.remove_object(bucket_name, obj.object_name)
            real_minio_client.remove_bucket(bucket_name)
        except Exception:
            pass  # Best effort cleanup


@pytest.fixture
def real_test_file(tmp_path: Path) -> Path:
    """Create a real test file for upload testing."""
    test_file = tmp_path / "test_malware.exe"
    test_content = b"MZ\x90\x00" + b"A" * 1000  # Fake PE header + content
    test_file.write_bytes(test_content)
    return test_file


@pytest.fixture
async def real_test_vm(real_docker_client: docker.DockerClient) -> AsyncGenerator[str, None]:
    """Create a real test VM using Docker for integration testing."""
    container_name = f"test-vm-{uuid.uuid4().hex[:8]}"
    
    try:
        # Create a simple test container
        container = real_docker_client.containers.run(
            "alpine:latest",
            command="sleep 300",  # Keep alive for 5 minutes
            name=container_name,
            detach=True,
            remove=False,
            labels={"test": "true", "turdparty": "integration-test"}
        )
        
        # Wait for container to be ready
        for _ in range(30):  # 30 second timeout
            container.reload()
            if container.status == "running":
                break
            await asyncio.sleep(1)
        else:
            raise RuntimeError("Container failed to start")
        
        yield container.id
        
    finally:
        # Cleanup
        try:
            container = real_docker_client.containers.get(container_name)
            container.stop(timeout=5)
            container.remove()
        except Exception:
            pass  # Best effort cleanup


@pytest.fixture
def real_test_environment() -> dict[str, str]:
    """Provide real environment configuration for integration tests."""
    return {
        "TURDPARTY_ENV": "integration_test",
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "ELASTICSEARCH_HOST": os.getenv("ELASTICSEARCH_HOST", "elasticsearch.turdparty.localhost"),
        "ELASTICSEARCH_PORT": os.getenv("ELASTICSEARCH_PORT", "9200"),
        "REDIS_HOST": os.getenv("REDIS_HOST", "redis.turdparty.localhost"),
        "REDIS_PORT": os.getenv("REDIS_PORT", "6379"),
        "MINIO_HOST": os.getenv("MINIO_HOST", "minio.turdparty.localhost"),
        "MINIO_PORT": os.getenv("MINIO_PORT", "9000"),
        "MINIO_ACCESS_KEY": os.getenv("MINIO_ACCESS_KEY", "minioadmin"),
        "MINIO_SECRET_KEY": os.getenv("MINIO_SECRET_KEY", "minioadmin"),
    }


@pytest.fixture(autouse=True)
def setup_real_test_environment(real_test_environment: dict[str, str], monkeypatch) -> None:
    """Set up real environment variables for integration tests."""
    for key, value in real_test_environment.items():
        monkeypatch.setenv(key, value)


@pytest.fixture
async def wait_for_services() -> None:
    """Wait for all services to be ready before running tests."""
    services = [
        ("Elasticsearch", "elasticsearch.turdparty.localhost", 9200),
        ("Redis", "redis.turdparty.localhost", 6379),
        ("MinIO", "minio.turdparty.localhost", 9000),
    ]
    
    for service_name, host, port in services:
        for attempt in range(30):  # 30 second timeout per service
            try:
                if service_name == "Elasticsearch":
                    es = AsyncElasticsearch(hosts=[{"host": host, "port": port}])
                    await es.info()
                    await es.close()
                elif service_name == "Redis":
                    r = redis.Redis(host=host, port=port, socket_timeout=2)
                    r.ping()
                    r.close()
                elif service_name == "MinIO":
                    m = Minio(f"{host}:{port}", access_key="minioadmin", secret_key="minioadmin", secure=False)
                    list(m.list_buckets())
                break
            except Exception:
                if attempt == 29:  # Last attempt
                    pytest.skip(f"{service_name} not available at {host}:{port}")
                await asyncio.sleep(1)


@pytest.fixture
def cleanup_test_data() -> Generator[None, None, None]:
    """Cleanup test data after tests complete."""
    test_indices = []
    test_keys = []
    test_buckets = []
    
    yield
    
    # Cleanup Elasticsearch indices
    # Cleanup Redis keys  
    # Cleanup MinIO buckets
    # This will be implemented based on what gets created during tests
