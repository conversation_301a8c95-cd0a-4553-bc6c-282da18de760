"""
Real file injection integration tests for TurdParty.

Tests actual file injection service with real MinIO, Redis, and Docker.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
from pathlib import Path
import uuid

import pytest
import docker
import redis
from minio import Minio

from services.api.src.services.file_injection_service import FileInjectionService
from services.api.src.models.file_injection import FileInjectionCreate, InjectionStatus


@pytest.mark.integration
@pytest.mark.asyncio
class TestRealFileInjectionIntegration:
    """Real file injection integration tests using actual services."""

    async def test_real_file_upload_to_minio(
        self,
        real_minio_client: Minio,
        real_test_bucket: str,
        real_test_file: Path
    ) -> None:
        """Test real file upload to MinIO."""
        object_name = f"test-files/{uuid.uuid4().hex}.exe"
        
        # Upload file
        real_minio_client.fput_object(
            bucket_name=real_test_bucket,
            object_name=object_name,
            file_path=str(real_test_file)
        )
        
        # Verify file exists
        try:
            stat = real_minio_client.stat_object(real_test_bucket, object_name)
            assert stat.size == real_test_file.stat().st_size
        except Exception as e:
            pytest.fail(f"File not found in MinIO: {e}")
        
        # Download and verify content
        response = real_minio_client.get_object(real_test_bucket, object_name)
        downloaded_content = response.read()
        original_content = real_test_file.read_bytes()
        
        assert downloaded_content == original_content

    async def test_real_injection_workflow(
        self,
        real_file_injection_service: FileInjectionService,
        real_test_vm: str,
        real_test_bucket: str,
        real_test_file: Path,
        real_minio_client: Minio
    ) -> None:
        """Test complete real file injection workflow."""
        # Upload test file to MinIO
        file_uuid = str(uuid.uuid4())
        object_name = f"uploads/{file_uuid}.exe"
        
        real_minio_client.fput_object(
            bucket_name=real_test_bucket,
            object_name=object_name,
            file_path=str(real_test_file)
        )
        
        # Create injection request
        injection_data = FileInjectionCreate(
            file_uuid=file_uuid,
            vm_id=real_test_vm,
            injection_path="/tmp/test_malware.exe",
            permissions="0755"
        )
        
        # Create injection
        injection_response = await real_file_injection_service.create_injection(injection_data)
        
        assert injection_response.file_uuid == file_uuid
        assert injection_response.vm_id == real_test_vm
        assert injection_response.status == InjectionStatus.QUEUED
        assert injection_response.injection_id is not None
        
        # Process the injection
        process_result = await real_file_injection_service.process_injection(
            injection_response.injection_id
        )
        
        assert process_result["success"] is True
        assert process_result["injection_id"] == injection_response.injection_id

    async def test_real_vm_file_injection(
        self,
        real_docker_client: docker.DockerClient,
        real_test_vm: str,
        real_test_file: Path
    ) -> None:
        """Test real file injection into Docker container."""
        container = real_docker_client.containers.get(real_test_vm)
        
        # Create a tar archive of the test file
        import tarfile
        import io
        
        tar_buffer = io.BytesIO()
        with tarfile.open(fileobj=tar_buffer, mode='w') as tar:
            tarinfo = tarfile.TarInfo(name="injected_file.exe")
            tarinfo.size = real_test_file.stat().st_size
            tar.addfile(tarinfo, real_test_file.open('rb'))
        
        tar_buffer.seek(0)
        
        # Inject file into container
        success = container.put_archive("/tmp", tar_buffer)
        assert success is True
        
        # Verify file exists in container
        exec_result = container.exec_run("ls -la /tmp/injected_file.exe")
        assert exec_result.exit_code == 0
        assert b"injected_file.exe" in exec_result.output
        
        # Verify file content
        exec_result = container.exec_run("cat /tmp/injected_file.exe")
        assert exec_result.exit_code == 0
        assert exec_result.output == real_test_file.read_bytes()

    async def test_real_redis_caching(
        self,
        real_redis_client: redis.Redis,
        real_file_injection_service: FileInjectionService
    ) -> None:
        """Test real Redis caching for injection status."""
        injection_id = f"test-injection-{uuid.uuid4().hex[:8]}"
        test_status = InjectionStatus.IN_PROGRESS
        
        # Set status in Redis
        cache_key = f"injection_status:{injection_id}"
        real_redis_client.set(cache_key, test_status.value, ex=300)  # 5 minute expiry
        
        # Verify status is cached
        cached_status = real_redis_client.get(cache_key)
        assert cached_status == test_status.value
        
        # Test expiry
        ttl = real_redis_client.ttl(cache_key)
        assert 0 < ttl <= 300

    async def test_real_concurrent_injections(
        self,
        real_file_injection_service: FileInjectionService,
        real_test_bucket: str,
        real_test_file: Path,
        real_minio_client: Minio,
        real_docker_client: docker.DockerClient
    ) -> None:
        """Test real concurrent file injections."""
        num_injections = 5
        injection_tasks = []
        
        # Create multiple test VMs
        test_vms = []
        for i in range(num_injections):
            container_name = f"concurrent-test-vm-{i}-{uuid.uuid4().hex[:8]}"
            container = real_docker_client.containers.run(
                "alpine:latest",
                command="sleep 300",
                name=container_name,
                detach=True,
                remove=False,
                labels={"test": "concurrent", "turdparty": "integration-test"}
            )
            test_vms.append(container.id)
        
        try:
            # Upload test files and create injections
            for i, vm_id in enumerate(test_vms):
                file_uuid = str(uuid.uuid4())
                object_name = f"concurrent/{file_uuid}.exe"
                
                # Upload file
                real_minio_client.fput_object(
                    bucket_name=real_test_bucket,
                    object_name=object_name,
                    file_path=str(real_test_file)
                )
                
                # Create injection
                injection_data = FileInjectionCreate(
                    file_uuid=file_uuid,
                    vm_id=vm_id,
                    injection_path=f"/tmp/concurrent_test_{i}.exe",
                    permissions="0755"
                )
                
                task = real_file_injection_service.create_injection(injection_data)
                injection_tasks.append(task)
            
            # Execute all injections concurrently
            injection_responses = await asyncio.gather(*injection_tasks)
            
            # Verify all injections were created
            assert len(injection_responses) == num_injections
            for response in injection_responses:
                assert response.status == InjectionStatus.QUEUED
                assert response.injection_id is not None
        
        finally:
            # Cleanup test VMs
            for vm_id in test_vms:
                try:
                    container = real_docker_client.containers.get(vm_id)
                    container.stop(timeout=5)
                    container.remove()
                except Exception:
                    pass  # Best effort cleanup

    async def test_real_error_scenarios(
        self,
        real_file_injection_service: FileInjectionService,
        real_test_bucket: str
    ) -> None:
        """Test real error scenarios in file injection."""
        # Test injection with non-existent file
        injection_data = FileInjectionCreate(
            file_uuid=str(uuid.uuid4()),  # Non-existent file
            vm_id="non-existent-vm",
            injection_path="/tmp/non_existent.exe",
            permissions="0755"
        )
        
        injection_response = await real_file_injection_service.create_injection(injection_data)
        assert injection_response.status == InjectionStatus.QUEUED
        
        # Process should fail gracefully
        process_result = await real_file_injection_service.process_injection(
            injection_response.injection_id
        )
        
        assert process_result["success"] is False
        assert "error" in process_result

    async def test_real_injection_status_tracking(
        self,
        real_file_injection_service: FileInjectionService,
        real_redis_client: redis.Redis
    ) -> None:
        """Test real injection status tracking through Redis."""
        injection_id = f"status-test-{uuid.uuid4().hex[:8]}"
        
        # Test status progression
        statuses = [
            InjectionStatus.QUEUED,
            InjectionStatus.IN_PROGRESS,
            InjectionStatus.COMPLETED
        ]
        
        for status in statuses:
            # Update status
            cache_key = f"injection_status:{injection_id}"
            real_redis_client.set(cache_key, status.value, ex=300)
            
            # Verify status
            cached_status = real_redis_client.get(cache_key)
            assert cached_status == status.value
            
            # Small delay to simulate processing time
            await asyncio.sleep(0.1)

    async def test_real_performance_metrics(
        self,
        real_file_injection_service: FileInjectionService,
        real_test_bucket: str,
        real_test_file: Path,
        real_minio_client: Minio
    ) -> None:
        """Test real performance metrics for file injection."""
        import time
        
        # Upload test file
        file_uuid = str(uuid.uuid4())
        object_name = f"performance/{file_uuid}.exe"
        
        upload_start = time.time()
        real_minio_client.fput_object(
            bucket_name=real_test_bucket,
            object_name=object_name,
            file_path=str(real_test_file)
        )
        upload_time = time.time() - upload_start
        
        # Create injection
        injection_data = FileInjectionCreate(
            file_uuid=file_uuid,
            vm_id="performance-test-vm",
            injection_path="/tmp/performance_test.exe",
            permissions="0755"
        )
        
        creation_start = time.time()
        injection_response = await real_file_injection_service.create_injection(injection_data)
        creation_time = time.time() - creation_start
        
        # Performance assertions
        assert upload_time < 5.0, f"File upload too slow: {upload_time:.2f}s"
        assert creation_time < 1.0, f"Injection creation too slow: {creation_time:.2f}s"
        assert injection_response.injection_id is not None
