"""
Integration tests for file injection API endpoints.

Tests the complete file injection workflow ensuring PEP8, PEP257, and PEP484 compliance.
"""

from pathlib import Path
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient
from httpx import AsyncClient
import pytest

from services.api.src.main import app as get_application


class TestFileInjectionAPIIntegration:
    """Integration test suite for file injection API."""

    @pytest.fixture
    def app(self):
        """Create FastAPI application for testing."""
        return get_application()

    @pytest.fixture
    def client(self, app) -> TestClient:
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    async def async_client(self, app) -> AsyncClient:
        """Create async test client."""
        async with Async<PERSON>lient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    def sample_file(self, temp_dir: Path) -> Path:
        """Create a sample file for testing."""
        file_path = temp_dir / "test_script.sh"
        file_path.write_text("#!/bin/bash\necho 'Hello World'\n")
        return file_path

    @pytest.fixture
    def mock_elk_logger(self):
        """Mock ELK logger for integration tests."""
        with patch("api.v1.routes.file_injection.ELKLogger") as mock_class:
            mock_instance = AsyncMock()
            mock_class.return_value = mock_instance
            yield mock_instance

    def test_health_endpoint(self, client: TestClient) -> None:
        """Test the health check endpoint."""
        # Act
        response = client.get("/health")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "turdparty-api"

    def test_create_file_injection_success(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test successful file injection creation via API."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test_script.sh", f, "application/x-sh")}
            data = {
                "target_path": "/app/scripts/test_script.sh",
                "permissions": "0755",
                "description": "Test script injection",
            }

            # Act
            response = client.post("/api/v1/file_injection/", files=files, data=data)

        # Assert
        assert response.status_code == 201
        result = response.json()
        assert "id" in result
        assert result["filename"] == "test_script.sh"
        assert result["target_path"] == "/app/scripts/test_script.sh"
        assert result["permissions"] == "0755"
        assert result["status"] == "pending"
        assert result["description"] == "Test script injection"

    def test_create_file_injection_missing_file(self, client: TestClient) -> None:
        """Test file injection creation without file."""
        # Arrange
        data = {
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        }

        # Act
        response = client.post("/api/v1/file_injection/", data=data)

        # Assert
        assert response.status_code == 422  # Validation error

    def test_get_file_injection_by_id(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test retrieving file injection by ID."""
        # Arrange - Create an injection first
        with sample_file.open("rb") as f:
            files = {"file": ("test_script.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}
            create_response = client.post("/api/v1/file_injection/", files=files, data=data)

        injection_id = create_response.json()["id"]

        # Act
        response = client.get(f"/api/v1/file_injection/{injection_id}")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["id"] == injection_id
        assert result["filename"] == "test_script.sh"

    def test_get_file_injection_not_found(self, client: TestClient) -> None:
        """Test retrieving non-existent file injection."""
        # Act
        response = client.get("/api/v1/file_injection/nonexistent-id")

        # Assert
        assert response.status_code == 404

    def test_get_all_file_injections(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test retrieving all file injections."""
        # Arrange - Create multiple injections
        for i in range(3):
            with sample_file.open("rb") as f:
                files = {"file": (f"test_{i}.sh", f, "application/x-sh")}
                data = {"target_path": f"/app/test_{i}.sh", "permissions": "0755"}
                client.post("/api/v1/file_injection/", files=files, data=data)

        # Act
        response = client.get("/api/v1/file_injection/")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert len(result) == 3

    def test_get_all_file_injections_with_pagination(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test retrieving file injections with pagination."""
        # Arrange - Create multiple injections
        for i in range(5):
            with sample_file.open("rb") as f:
                files = {"file": (f"test_{i}.sh", f, "application/x-sh")}
                data = {"target_path": f"/app/test_{i}.sh", "permissions": "0755"}
                client.post("/api/v1/file_injection/", files=files, data=data)

        # Act
        response = client.get("/api/v1/file_injection/?skip=2&limit=2")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert len(result) == 2

    def test_get_all_file_injections_with_status_filter(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test retrieving file injections with status filter."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}
            client.post("/api/v1/file_injection/", files=files, data=data)

        # Act
        response = client.get("/api/v1/file_injection/?status_filter=pending")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert len(result) == 1
        assert result[0]["status"] == "pending"

    def test_get_injection_status(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test getting injection status."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}
            create_response = client.post("/api/v1/file_injection/", files=files, data=data)

        injection_id = create_response.json()["id"]

        # Act
        response = client.get(f"/api/v1/file_injection/{injection_id}/status")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["id"] == injection_id
        assert result["status"] == "pending"
        assert result["progress"] == 0

    def test_process_injection_success(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test successful injection processing."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}
            create_response = client.post("/api/v1/file_injection/", files=files, data=data)

        injection_id = create_response.json()["id"]

        # Act
        response = client.post(f"/api/v1/file_injection/{injection_id}/process")

        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["id"] == injection_id
        assert result["status"] == "completed"
        assert result["progress"] == 100

    def test_process_injection_not_found(self, client: TestClient) -> None:
        """Test processing non-existent injection."""
        # Act
        response = client.post("/api/v1/file_injection/nonexistent-id/process")

        # Assert
        assert response.status_code == 500  # Service error for non-existent ID

    def test_delete_injection_success(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test successful injection deletion."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}
            create_response = client.post("/api/v1/file_injection/", files=files, data=data)

        injection_id = create_response.json()["id"]

        # Act
        response = client.delete(f"/api/v1/file_injection/{injection_id}")

        # Assert
        assert response.status_code == 204

        # Verify deletion
        get_response = client.get(f"/api/v1/file_injection/{injection_id}")
        assert get_response.status_code == 404

    def test_delete_injection_not_found(self, client: TestClient) -> None:
        """Test deleting non-existent injection."""
        # Act
        response = client.delete("/api/v1/file_injection/nonexistent-id")

        # Assert
        assert response.status_code == 500  # Service error for non-existent ID

    def test_elk_logging_integration(
        self,
        client: TestClient,
        sample_file: Path,
        mock_elk_logger: AsyncMock,
    ) -> None:
        """Test that ELK logging is called during injection operations."""
        # Arrange
        with sample_file.open("rb") as f:
            files = {"file": ("test.sh", f, "application/x-sh")}
            data = {"target_path": "/app/test.sh", "permissions": "0755"}

            # Act - Create injection
            create_response = client.post("/api/v1/file_injection/", files=files, data=data)

        injection_id = create_response.json()["id"]

        # Process injection
        client.post(f"/api/v1/file_injection/{injection_id}/process")

        # Delete injection
        client.delete(f"/api/v1/file_injection/{injection_id}")

        # Assert ELK logging was called
        assert mock_elk_logger.log_file_injection_event.call_count >= 3  # Create, process, delete
        assert mock_elk_logger.log_installation_base.call_count >= 1  # During processing
