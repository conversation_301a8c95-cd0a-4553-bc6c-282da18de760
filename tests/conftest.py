"""
Pytest configuration and shared fixtures for TurdParty tests.

This module provides common test fixtures and configuration for all test types.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from collections.abc import Generator
from datetime import UTC, datetime
import os
from pathlib import Path
import tempfile
from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch
import uuid

import pytest
import asyncio
import time
import logging
import statistics
import threading
from typing import AsyncGenerator

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from fastapi.testclient import TestClient
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    TestClient = None

try:
    from minio import Minio
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    Minio = None

try:
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    AsyncElasticsearch = None

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    from api.v1.application import get_application
    APP_AVAILABLE = True
except ImportError:
    APP_AVAILABLE = False
    get_application = None

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    httpx = None

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    websockets = None

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    docker = None

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None


def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "websocket: WebSocket tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "benchmark: Benchmark tests")
    config.addinivalue_line("markers", "scalability: Scalability tests")
    config.addinivalue_line("markers", "resource: Resource utilization tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "docker: Tests requiring Docker")
    config.addinivalue_line("markers", "vagrant: Tests requiring Vagrant")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)

        # Add markers based on test name patterns
        if "websocket" in item.name.lower():
            item.add_marker(pytest.mark.websocket)
        if "benchmark" in item.name.lower():
            item.add_marker(pytest.mark.benchmark)
        if "docker" in item.name.lower():
            item.add_marker(pytest.mark.docker)
        if "vagrant" in item.name.lower():
            item.add_marker(pytest.mark.vagrant)


def pytest_addoption(parser):
    """Add custom command line options"""
    parser.addoption(
        "--runslow", action="store_true", default=False,
        help="run slow tests"
    )
    parser.addoption(
        "--runperformance", action="store_true", default=False,
        help="run performance tests"
    )
    parser.addoption(
        "--runintegration", action="store_true", default=False,
        help="run integration tests"
    )


def pytest_runtest_setup(item):
    """Setup for each test"""
    # Skip slow tests unless explicitly requested
    if "slow" in item.keywords and not item.config.getoption("--runslow", default=False):
        pytest.skip("need --runslow option to run")


# Note: Removed deprecated event_loop fixture - pytest-asyncio handles this automatically


@pytest.fixture
def app():
    """Create a FastAPI application instance for testing."""
    if not APP_AVAILABLE:
        pytest.skip("FastAPI application not available")
    return get_application()


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI application."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI TestClient not available")
    return TestClient(app)


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_file_content() -> bytes:
    """Provide sample file content for testing."""
    return b"#!/bin/bash\necho 'Hello from test file'\n"


@pytest.fixture
def sample_script_file(temp_dir: Path, sample_file_content: bytes) -> Path:
    """Create a sample script file for testing."""
    script_file = temp_dir / "test_script.sh"
    script_file.write_bytes(sample_file_content)
    script_file.chmod(0o755)
    return script_file


# DEPRECATED: Use real_elk_logger from tests.integration.conftest_real instead
# @pytest.fixture
# def mock_elk_logger() -> MagicMock:
#     """DEPRECATED: Use real ELK logger instead of mocks."""
#     raise NotImplementedError("Use real_elk_logger from tests.integration.conftest_real")


# DEPRECATED: Use real_file_injection_service from tests.integration.conftest_real instead
# @pytest.fixture
# def mock_file_injection_service() -> MagicMock:
#     """DEPRECATED: Use real file injection service instead of mocks."""
#     raise NotImplementedError("Use real_file_injection_service from tests.integration.conftest_real")


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir: Path) -> None:
    """Set up test environment variables."""
    monkeypatch.setenv("TEST_MODE", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("FILE_UPLOAD_DIR", str(temp_dir))
    monkeypatch.setenv("ELASTICSEARCH_HOST", "localhost")
    monkeypatch.setenv("ELASTICSEARCH_PORT", "9200")
    monkeypatch.setenv("LOGSTASH_HOST", "localhost")
    monkeypatch.setenv("LOGSTASH_PORT", "5000")


@pytest.fixture
def injection_data() -> dict:
    """Provide sample injection data for testing."""
    return {
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "description": "Test script for unit testing",
    }


@pytest.fixture
def injection_response_data() -> dict:
    """Provide sample injection response data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "status": "pending",
        "description": "Test script for unit testing",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "file_size": 1024,
        "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
    }


@pytest.fixture
def injection_status_data() -> dict:
    """Provide sample injection status data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "completed",
        "progress": 100,
        "message": "File injection completed successfully",
        "details": {
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        },
        "updated_at": "2024-01-15T10:35:00Z",
    }


class AsyncContextManager:
    """Helper class for testing async context managers."""

    def __init__(self, return_value=None):
        """Initialize with optional return value."""
        self.return_value = return_value

    async def __aenter__(self):
        """Async enter method."""
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit method."""
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


@pytest.fixture
def async_context_manager():
    """Provide an async context manager for testing."""
    return AsyncContextManager


# DEPRECATED: Use real_minio_client from tests.integration.conftest_real instead
# @pytest.fixture
# def mock_minio_client() -> MagicMock:
#     """DEPRECATED: Use real MinIO client instead of mocks."""
#     raise NotImplementedError("Use real_minio_client from tests.integration.conftest_real")


@pytest.fixture
def mock_elasticsearch_client() -> AsyncMock:
    """Create a mock Elasticsearch client for testing."""
    if ELASTICSEARCH_AVAILABLE:
        mock_client = AsyncMock(spec=AsyncElasticsearch)
    else:
        mock_client = AsyncMock()
    mock_client.index.return_value = {"_id": "test-doc-id", "result": "created"}
    mock_client.search.return_value = {
        "hits": {"total": {"value": 0}, "hits": []}
    }
    mock_client.indices.exists.return_value = True
    return mock_client


# DEPRECATED: Use real_redis_client from tests.integration.conftest_real instead
# @pytest.fixture
# def mock_redis_client() -> MagicMock:
#     """DEPRECATED: Use real Redis client instead of mocks."""
#     raise NotImplementedError("Use real_redis_client from tests.integration.conftest_real")


@pytest.fixture
def mock_celery_app() -> MagicMock:
    """Create a mock Celery application for testing."""
    mock_app = MagicMock()
    mock_task = MagicMock()
    mock_task.delay.return_value = MagicMock(id="test-task-id")
    mock_app.send_task.return_value = mock_task.delay.return_value
    return mock_app


@pytest.fixture
def sample_file_data() -> dict[str, Any]:
    """Provide sample file data for testing."""
    return {
        "filename": "test_script.sh",
        "content": b"#!/bin/bash\necho 'Hello World'\n",
        "size": 28,
        "content_type": "application/x-sh",
        "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
    }


@pytest.fixture
def sample_vm_data() -> dict[str, Any]:
    """Provide sample VM data for testing."""
    return {
        "vm_id": "test-vm-123",
        "name": "test-vm",
        "status": "running",
        "ip_address": "*************",
        "memory": 1024,
        "cpus": 2,
        "created_at": datetime.now(UTC).isoformat()
    }


@pytest.fixture
def sample_workflow_data() -> dict[str, Any]:
    """Provide sample workflow data for testing."""
    return {
        "workflow_id": str(uuid.uuid4()),
        "file_id": str(uuid.uuid4()),
        "vm_id": "test-vm-123",
        "status": "pending",
        "steps": ["upload", "inject", "execute", "monitor"],
        "current_step": "upload"
    }


def load_test_config() -> dict[str, Any]:
    """Load test configuration from environment and .env files."""
    config = {}

    # Load from integration test .env file if it exists
    integration_env = Path(__file__).parent / 'integration' / '.env.test'
    if integration_env.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(integration_env)
        except ImportError:
            # Parse .env file manually if python-dotenv not available
            with integration_env.open() as f:
                for raw_line in f:
                    line = raw_line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()

    # Load from main .env file if it exists
    env_file = Path(__file__).parent / '.env'
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
        except ImportError:
            pass

    # Load configuration from environment
    config.update({
        'TEST_MODE': os.getenv('TEST_MODE', 'true').lower() == 'true',
        'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'DATABASE_URL': os.getenv('DATABASE_URL', 'sqlite:///test.db'),
        'REDIS_URL': os.getenv('REDIS_URL', 'redis://redis.turdparty.localhost:6379/1'),
        'API_BASE_URL': os.getenv('API_BASE_URL', 'http://api.turdparty.localhost'),
        'API_VERSION': os.getenv('API_VERSION', 'v1'),
        'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '300')),

        # MinIO Configuration
        'MINIO_ENDPOINT': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
        'MINIO_ACCESS_KEY': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
        'MINIO_SECRET_KEY': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
        'MINIO_SECURE': os.getenv('MINIO_SECURE', 'false').lower() == 'true',
        'MINIO_TEST_BUCKET_PREFIX': os.getenv('MINIO_TEST_BUCKET_PREFIX', 'turdparty-test'),
        'MINIO_REGION': os.getenv('MINIO_REGION', 'us-east-1'),

        # ELK Configuration
        'ELASTICSEARCH_URL': os.getenv('ELASTICSEARCH_URL', 'http://localhost:9200'),
        'ELASTICSEARCH_INDEX_PREFIX': os.getenv('ELASTICSEARCH_INDEX_PREFIX', 'turdparty-test'),
        'ELASTICSEARCH_TIMEOUT': int(os.getenv('ELASTICSEARCH_TIMEOUT', '30')),
        'KIBANA_URL': os.getenv('KIBANA_URL', 'http://localhost:5601'),
        'LOGSTASH_HOST': os.getenv('LOGSTASH_HOST', 'localhost'),
        'LOGSTASH_PORT': int(os.getenv('LOGSTASH_PORT', '5044')),

        # VM Configuration
        'VAGRANT_GRPC_PORT': int(os.getenv('VAGRANT_GRPC_PORT', '40000')),
        'VM_CAPTURE_TIMEOUT_MINUTES': int(os.getenv('VM_CAPTURE_TIMEOUT_MINUTES', '5')),
        'VM_BOOT_TIMEOUT_MINUTES': int(os.getenv('VM_BOOT_TIMEOUT_MINUTES', '10')),
        'VM_TEARDOWN_TIMEOUT_MINUTES': int(os.getenv('VM_TEARDOWN_TIMEOUT_MINUTES', '5')),
        'VM_NETWORK_DISABLED': os.getenv('VM_NETWORK_DISABLED', 'true').lower() == 'true',
        'VM_MOUNTS_DISABLED': os.getenv('VM_MOUNTS_DISABLED', 'true').lower() == 'true',
        'USE_DOCKER_FALLBACK': os.getenv('USE_DOCKER_FALLBACK', 'true').lower() == 'true',

        # VirtualBox Configuration
        'VBOX_HEADLESS': os.getenv('VBOX_HEADLESS', 'true').lower() == 'true',
        'VBOX_MEMORY_MB': int(os.getenv('VBOX_MEMORY_MB', '2048')),
        'VBOX_CPUS': int(os.getenv('VBOX_CPUS', '2')),
        'VBOX_DISK_SIZE_GB': int(os.getenv('VBOX_DISK_SIZE_GB', '20')),

        # Test Data Configuration
        'TEST_DOWNLOAD_DIR': os.getenv('TEST_DOWNLOAD_DIR', '/tmp/turdparty-test-downloads'),
        'TEST_APPS_JSON': os.getenv('TEST_APPS_JSON', 'tests/data/test_applications.json'),
        'TEST_BENCHMARK_FILE': os.getenv('TEST_BENCHMARK_FILE', 'tests/data/benchmarks.json'),
        'TEST_CLEANUP_ENABLED': os.getenv('TEST_CLEANUP_ENABLED', 'true').lower() == 'true',

        # File Processing Configuration
        'BLAKE3_HASH_ENABLED': os.getenv('BLAKE3_HASH_ENABLED', 'true').lower() == 'true',
        'FILE_CHUNK_SIZE': int(os.getenv('FILE_CHUNK_SIZE', '8192')),
        'MAX_FILE_SIZE_MB': int(os.getenv('MAX_FILE_SIZE_MB', '2048')),
        'DOWNLOAD_TIMEOUT_SECONDS': int(os.getenv('DOWNLOAD_TIMEOUT_SECONDS', '300')),
        'UPLOAD_TIMEOUT_SECONDS': int(os.getenv('UPLOAD_TIMEOUT_SECONDS', '300')),

        # VM Test Configuration
        'UBUNTU_VM_COUNT': int(os.getenv('UBUNTU_VM_COUNT', '2')),
        'WINDOWS_VM_COUNT': int(os.getenv('WINDOWS_VM_COUNT', '2')),
        'UBUNTU_VM_IMAGE': os.getenv('UBUNTU_VM_IMAGE', 'ubuntu/focal64'),
        'WINDOWS_VM_IMAGE': os.getenv('WINDOWS_VM_IMAGE', 'gusztavvargadr/windows-10'),

        # Cleanup Configuration
        'AUTO_CLEANUP_DOWNLOADS': os.getenv('AUTO_CLEANUP_DOWNLOADS', 'true').lower() == 'true',
        'AUTO_CLEANUP_VMS': os.getenv('AUTO_CLEANUP_VMS', 'true').lower() == 'true',
        'AUTO_CLEANUP_MINIO': os.getenv('AUTO_CLEANUP_MINIO', 'true').lower() == 'true',
        'KEEP_FAILED_TEST_DATA': os.getenv('KEEP_FAILED_TEST_DATA', 'true').lower() == 'true',

        # Security Configuration
        'DISABLE_VM_NETWORKING': os.getenv('DISABLE_VM_NETWORKING', 'true').lower() == 'true',
        'DISABLE_VM_SHARED_FOLDERS': os.getenv('DISABLE_VM_SHARED_FOLDERS', 'true').lower() == 'true',
        'ENABLE_VM_ISOLATION': os.getenv('ENABLE_VM_ISOLATION', 'true').lower() == 'true',
        'SANDBOX_MODE': os.getenv('SANDBOX_MODE', 'true').lower() == 'true',
    })

    return config


# Enhanced VM Testing Fixtures

@pytest.fixture(scope="session")
def api_server_url():
    """API server URL for testing"""
    return os.getenv("TEST_API_URL", "http://localhost:8000")


@pytest.fixture(scope="session")
def websocket_server_url():
    """WebSocket server URL for testing"""
    return os.getenv("TEST_WS_URL", "ws://localhost:8000")


@pytest.fixture
async def api_client(api_server_url) -> AsyncGenerator:
    """Async HTTP client for API testing"""
    if not HTTPX_AVAILABLE:
        pytest.skip("httpx not available")

    async with httpx.AsyncClient(base_url=api_server_url, timeout=30.0) as client:
        yield client


@pytest.fixture
async def test_vm_data():
    """Standard test VM data"""
    return {
        "name": f"test-vm-{int(time.time() * 1000)}",
        "template": "ubuntu:20.04",
        "vm_type": "docker",
        "memory_mb": 512,
        "cpus": 1,
        "domain": "TurdParty",
        "description": "Test VM for automated testing"
    }


@pytest.fixture
async def created_test_vm(api_client, test_vm_data):
    """Create a test VM and clean it up after test"""
    if not HTTPX_AVAILABLE:
        pytest.skip("httpx not available")

    # Create VM
    response = await api_client.post("/api/v1/vms/", json=test_vm_data)
    if response.status_code != 201:
        pytest.skip(f"Failed to create test VM: {response.status_code}")

    vm_info = response.json()
    vm_id = vm_info["vm_id"]

    yield vm_info

    # Cleanup
    try:
        await api_client.delete(f"/api/v1/vms/{vm_id}?force=true")
    except Exception as e:
        logger.warning(f"Failed to cleanup test VM {vm_id}: {e}")


@pytest.fixture
def mock_docker_client():
    """Mock Docker client for testing"""
    if not DOCKER_AVAILABLE:
        pytest.skip("docker not available")

    mock_client = MagicMock()

    # Mock container
    mock_container = MagicMock()
    mock_container.id = "test_container_123"
    mock_container.status = "running"
    mock_container.attrs = {
        'State': {
            'StartedAt': '2023-11-04T10:30:00.000000000Z'
        },
        'NetworkSettings': {
            'Networks': {
                'bridge': {
                    'IPAddress': '**********'
                }
            }
        }
    }

    # Mock stats
    mock_stats = {
        'cpu_stats': {
            'cpu_usage': {
                'total_usage': 1000000000,
                'percpu_usage': [500000000, 500000000]
            },
            'system_cpu_usage': 2000000000
        },
        'precpu_stats': {
            'cpu_usage': {
                'total_usage': 500000000
            },
            'system_cpu_usage': 1000000000
        },
        'memory_stats': {
            'usage': 1073741824,  # 1GB
            'limit': 2147483648   # 2GB
        },
        'networks': {
            'eth0': {
                'rx_bytes': 1048576,
                'tx_bytes': 524288,
                'rx_packets': 1000,
                'tx_packets': 800
            }
        }
    }

    mock_container.stats.return_value = mock_stats
    mock_container.exec_run.return_value = MagicMock(
        exit_code=0,
        output=(
            b"root      1234  15.2  25.6  123456  789012 ?        R    10:30   0:01 python app.py\n",
            None
        )
    )
    mock_container.put_archive.return_value = True

    mock_client.containers.get.return_value = mock_container
    mock_client.containers.list.return_value = [mock_container]
    mock_client.containers.run.return_value = mock_container

    return mock_client


@pytest.fixture
def mock_vm_metrics_service():
    """Mock VM metrics service"""
    with patch('api.services.vm_metrics_service.vm_metrics_service') as mock_service:
        # Mock initialization
        mock_service.initialize = AsyncMock()

        # Mock metrics collection
        mock_service.get_vm_metrics = AsyncMock(return_value={
            "vm_id": "test_vm",
            "vm_type": "docker",
            "timestamp": int(time.time() * 1000),
            "status": "running",
            "cpu_percent": 25.5,
            "memory_percent": 45.2,
            "memory_used_mb": 1024.0,
            "network_rx_bytes": 1048576,
            "network_tx_bytes": 524288,
            "top_processes": [
                {"pid": 1234, "name": "python", "cpu_percent": 15.2, "memory_mb": 256.5}
            ],
            "uptime_seconds": 3600
        })

        # Mock streaming
        async def mock_stream():
            for i in range(5):
                yield {
                    "vm_id": "test_vm",
                    "timestamp": int(time.time() * 1000) + i * 1000,
                    "cpu_percent": 25.5 + i,
                    "memory_percent": 45.2 + i,
                    "status": "running"
                }
                await asyncio.sleep(0.1)

        mock_service.stream_vm_metrics.return_value = mock_stream()
        mock_service.stop_stream = MagicMock()

        yield mock_service


@pytest.fixture
async def websocket_test_helper():
    """Helper for WebSocket testing"""
    if not WEBSOCKETS_AVAILABLE:
        pytest.skip("websockets not available")

    class WebSocketTestHelper:
        def __init__(self):
            self.connections = []

        async def connect(self, uri, **kwargs):
            """Connect to WebSocket and track connection"""
            websocket = await websockets.connect(uri, **kwargs)
            self.connections.append(websocket)
            return websocket

        async def close_all(self):
            """Close all tracked connections"""
            for ws in self.connections:
                try:
                    await ws.close()
                except:
                    pass
            self.connections.clear()

        async def send_and_receive(self, websocket, message, timeout=5.0):
            """Send message and receive response"""
            await websocket.send(message)
            return await asyncio.wait_for(websocket.recv(), timeout=timeout)

    helper = WebSocketTestHelper()
    yield helper
    await helper.close_all()


@pytest.fixture
def performance_monitor():
    """Monitor performance metrics during test execution"""
    if not PSUTIL_AVAILABLE:
        pytest.skip("psutil not available")

    metrics = {
        'cpu_samples': [],
        'memory_samples': [],
        'start_time': time.time(),
        'monitoring': True
    }

    def monitor():
        process = psutil.Process()
        while metrics['monitoring']:
            try:
                cpu_percent = psutil.cpu_percent()
                memory_info = process.memory_info()

                metrics['cpu_samples'].append(cpu_percent)
                metrics['memory_samples'].append(memory_info.rss)

                time.sleep(0.5)
            except:
                break

    # Start monitoring
    monitor_thread = threading.Thread(target=monitor, daemon=True)
    monitor_thread.start()

    yield metrics

    # Stop monitoring
    metrics['monitoring'] = False
    metrics['end_time'] = time.time()

    # Calculate summary statistics
    if metrics['cpu_samples']:
        metrics['avg_cpu'] = statistics.mean(metrics['cpu_samples'])
        metrics['max_cpu'] = max(metrics['cpu_samples'])
        metrics['avg_memory'] = statistics.mean(metrics['memory_samples'])
        metrics['max_memory'] = max(metrics['memory_samples'])
        metrics['duration'] = metrics['end_time'] - metrics['start_time']


@pytest.fixture(scope="session")
def docker_available():
    """Check if Docker is available for testing"""
    if not DOCKER_AVAILABLE:
        return False
    try:
        client = docker.from_env()
        client.ping()
        return True
    except Exception:
        return False


@pytest.fixture(scope="session")
def vagrant_available():
    """Check if Vagrant is available for testing"""
    import subprocess
    try:
        result = subprocess.run(['vagrant', '--version'],
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except Exception:
        return False


@pytest.fixture
def skip_if_no_docker(docker_available):
    """Skip test if Docker is not available"""
    if not docker_available:
        pytest.skip("Docker not available")


@pytest.fixture
def skip_if_no_vagrant(vagrant_available):
    """Skip test if Vagrant is not available"""
    if not vagrant_available:
        pytest.skip("Vagrant not available")


@pytest.fixture
def test_data_generator():
    """Generate test data for various scenarios"""
    class TestDataGenerator:
        @staticmethod
        def vm_creation_data(name_suffix="", **overrides):
            """Generate VM creation data"""
            base_data = {
                "name": f"test-vm-{int(time.time() * 1000)}{name_suffix}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty",
                "description": "Generated test VM"
            }
            base_data.update(overrides)
            return base_data

        @staticmethod
        def command_execution_data(command="echo 'test'", **overrides):
            """Generate command execution data"""
            base_data = {
                "command": command,
                "working_directory": "/tmp",
                "timeout_seconds": 30
            }
            base_data.update(overrides)
            return base_data

        @staticmethod
        def file_upload_data(target_path="/tmp/test_file.txt", **overrides):
            """Generate file upload data"""
            base_data = {
                "target_path": target_path,
                "permissions": "644"
            }
            base_data.update(overrides)
            return base_data

    return TestDataGenerator()
