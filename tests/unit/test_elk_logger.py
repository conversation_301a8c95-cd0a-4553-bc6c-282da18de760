"""
DEPRECATED: Unit tests for ELK logger service using MOCKS.

⚠️  DEPRECATED: This file uses mocks instead of real implementations.
Use test_elk_logger_real.py for real Elasticsearch integration testing.

Tests the ELK logging functionality with MOCKS (deprecated).
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from datetime import datetime
import os
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio

from services.api.src.services.elk_logger import ELKLogger

# Import real fixtures
pytest_plugins = ["tests.integration.conftest_real"]


class TestELKLogger:
    """Test suite for ELKLogger."""

    @pytest.fixture
    def mock_elasticsearch(self) -> MagicMock:
        """Create a mock Elasticsearch client."""
        mock_es = MagicMock()
        mock_es.index = AsyncMock()
        mock_es.search = AsyncMock()
        mock_es.close = AsyncMock()
        return mock_es

    @pytest.fixture
    def mock_logstash_handler(self) -> MagicMock:
        """Create a mock logstash handler."""
        mock_handler = MagicMock()
        mock_handler.level = 20  # INFO level
        return mock_handler

    @pytest.fixture
    def elk_logger(self, mock_elasticsearch: MagicMock, mock_logstash_handler: MagicMock) -> ELKLogger:
        """Create an ELKLogger instance with mocked dependencies."""
        with patch("services.api.src.services.elk_logger.AsyncElasticsearch") as mock_es_class, \
             patch("services.api.src.services.elk_logger.logstash.TCPLogstashHandler") as mock_handler_class:

            mock_es_class.return_value = mock_elasticsearch
            mock_handler_class.return_value = mock_logstash_handler

            return ELKLogger()

    @pytest.mark.asyncio
    async def test_log_file_injection_event_success(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test successful file injection event logging."""
        # Arrange
        injection_id = "test-injection-123"
        event_type = "injection_created"
        filename = "test.sh"
        target_path = "/app/test.sh"
        status = "pending"

        # Act
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type=event_type,
            filename=filename,
            target_path=target_path,
            status=status,
        )

        # Assert
        mock_elasticsearch.index.assert_called_once()
        call_args = mock_elasticsearch.index.call_args
        assert "index" in call_args.kwargs
        assert "document" in call_args.kwargs

        document = call_args.kwargs["document"]
        assert document["injection_id"] == injection_id
        assert document["event_type"] == event_type
        assert document["details"]["filename"] == filename
        assert document["details"]["target_path"] == target_path
        assert document["details"]["status"] == status

    @pytest.mark.asyncio
    async def test_log_file_injection_event_with_error(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test file injection event logging with error message."""
        # Arrange
        injection_id = "test-injection-123"
        event_type = "injection_failed"
        error_message = "File not found"

        # Act
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type=event_type,
            error_message=error_message,
        )

        # Assert
        mock_elasticsearch.index.assert_called_once()
        call_args = mock_elasticsearch.index.call_args
        document = call_args.kwargs["document"]
        assert document["details"]["error_message"] == error_message

    @pytest.mark.asyncio
    async def test_log_installation_base_success(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test successful installation base logging."""
        # Arrange
        injection_id = "test-injection-123"
        file_path = "/tmp/test.sh"
        target_path = "/app/test.sh"
        permissions = "0755"

        # Act
        with patch.object(elk_logger, "_get_system_info", return_value={"platform": "Linux"}):
            await elk_logger.log_installation_base(
                injection_id=injection_id,
                file_path=file_path,
                target_path=target_path,
                permissions=permissions,
            )

        # Assert
        mock_elasticsearch.index.assert_called_once()
        call_args = mock_elasticsearch.index.call_args
        assert "turdparty-installation-base" in call_args.kwargs["index"]

        document = call_args.kwargs["document"]
        assert document["injection_id"] == injection_id
        assert document["details"]["file_path"] == file_path
        assert document["details"]["target_path"] == target_path
        assert document["details"]["permissions"] == permissions

    @pytest.mark.asyncio
    async def test_log_system_event_success(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test successful system event logging."""
        # Arrange
        event_type = "system_startup"
        message = "System started successfully"
        level = "INFO"
        details = {"component": "api", "version": "1.0.0"}

        # Act
        await elk_logger.log_system_event(
            event_type=event_type,
            message=message,
            level=level,
            details=details,
        )

        # Assert
        mock_elasticsearch.index.assert_called_once()
        call_args = mock_elasticsearch.index.call_args
        assert "turdparty-system-events" in call_args.kwargs["index"]

        document = call_args.kwargs["document"]
        assert document["event_type"] == event_type
        assert document["message"] == message
        assert document["level"] == level
        assert document["details"] == details

    @pytest.mark.asyncio
    async def test_get_system_info_success(self, elk_logger: ELKLogger) -> None:
        """Test successful system info retrieval."""
        # Act
        with patch("platform.platform", return_value="Linux-5.4.0"), \
             patch("platform.python_version", return_value="3.10.0"), \
             patch("platform.node", return_value="test-host"), \
             patch("psutil.cpu_count", return_value=4), \
             patch("psutil.virtual_memory") as mock_memory, \
             patch("psutil.disk_usage") as mock_disk:

            mock_memory.return_value.total = 8589934592  # 8GB
            mock_disk.return_value.total = 1000000000000  # 1TB
            mock_disk.return_value.used = 500000000000   # 500GB
            mock_disk.return_value.free = 500000000000   # 500GB

            result = await elk_logger._get_system_info()

        # Assert
        assert result["platform"] == "Linux-5.4.0"
        assert result["python_version"] == "3.10.0"
        assert result["hostname"] == "test-host"
        assert result["cpu_count"] == 4
        assert result["memory_total"] == 8589934592

    @pytest.mark.asyncio
    async def test_get_system_info_error_handling(self, elk_logger: ELKLogger) -> None:
        """Test system info retrieval with errors."""
        # Act
        with patch("platform.platform", side_effect=Exception("Platform error")):
            result = await elk_logger._get_system_info()

        # Assert
        assert "error" in result
        assert "Failed to retrieve system information" in result["error"]

    @pytest.mark.asyncio
    async def test_search_logs_success(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test successful log searching."""
        # Arrange
        query = "injection_created"
        mock_response = {
            "hits": {
                "total": {"value": 1},
                "hits": [
                    {
                        "_source": {
                            "event_type": "injection_created",
                            "injection_id": "test-123",
                            "timestamp": "2024-01-15T10:30:00Z",
                        }
                    }
                ]
            }
        }
        mock_elasticsearch.search.return_value = mock_response

        # Act
        result = await elk_logger.search_logs(query)

        # Assert
        assert result == mock_response
        mock_elasticsearch.search.assert_called_once()
        call_args = mock_elasticsearch.search.call_args
        assert call_args.kwargs["index"] == "turdparty-*"
        assert "query" in call_args.kwargs["body"]

    @pytest.mark.asyncio
    async def test_search_logs_with_date_range(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test log searching with date range filter."""
        # Arrange
        query = "injection_created"
        from_date = datetime(2024, 1, 1)
        to_date = datetime(2024, 1, 31)
        mock_elasticsearch.search.return_value = {"hits": {"hits": []}}

        # Act
        await elk_logger.search_logs(query, from_date=from_date, to_date=to_date)

        # Assert
        call_args = mock_elasticsearch.search.call_args
        search_body = call_args.kwargs["body"]
        assert "filter" in search_body["query"]["bool"]
        date_filter = search_body["query"]["bool"]["filter"][0]
        assert "range" in date_filter
        assert "timestamp" in date_filter["range"]

    @pytest.mark.asyncio
    async def test_search_logs_error_handling(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test log searching with Elasticsearch error."""
        # Arrange
        query = "test_query"
        mock_elasticsearch.search.side_effect = Exception("Search failed")

        # Act
        result = await elk_logger.search_logs(query)

        # Assert
        assert "error" in result
        assert "Search failed" in result["error"]

    @pytest.mark.asyncio
    async def test_elasticsearch_index_error_handling(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test error handling when Elasticsearch indexing fails."""
        # Arrange
        mock_elasticsearch.index.side_effect = Exception("Index failed")
        injection_id = "test-injection-123"
        event_type = "injection_created"

        # Act (should not raise exception)
        await elk_logger.log_file_injection_event(
            injection_id=injection_id,
            event_type=event_type,
        )

        # Assert
        mock_elasticsearch.index.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_connection(
        self,
        elk_logger: ELKLogger,
        mock_elasticsearch: MagicMock,
    ) -> None:
        """Test closing ELK logger connections."""
        # Act
        await elk_logger.close()

        # Assert
        mock_elasticsearch.close.assert_called_once()
