"""
DEPRECATED: Unit tests for worker services using MOCKS.

⚠️  DEPRECATED: This file uses mocks instead of real implementations.
Use test_worker_services_real.py for real service integration testing.

Tests the Celery worker tasks with MOCKS (deprecated).
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from pathlib import Path
import tempfile
from typing import Any
from unittest.mock import MagicMock, patch

import pytest

# Mock the Celery imports since they might not be available in test environment
with patch.dict('sys.modules', {
    'celery': MagicMock(),
    'services.workers.celery_app': MagicMock(),
}):
    from services.workers.tasks.file_operations import (
        download_file_from_minio,
        prepare_file_for_injection,
        validate_file,
    )
    from services.workers.tasks.injection_tasks import (
        inject_file,
        execute_injected_file,
    )
    from services.workers.tasks.vm_management import (
        create_vm,
        delete_vm,
        get_vm_status,
        start_vm,
    )


class TestFileOperationTasks:
    """Test suite for file operation worker tasks."""

    @pytest.fixture
    def mock_minio_client(self) -> MagicMock:
        """Create a mock MinIO client."""
        mock_client = MagicMock()
        mock_client.get_object.return_value = MagicMock()
        mock_client.bucket_exists.return_value = True
        return mock_client

    @pytest.fixture
    def sample_file_data(self) -> dict[str, Any]:
        """Provide sample file data."""
        return {
            "file_id": "test-file-123",
            "filename": "test_script.sh",
            "bucket": "turdparty-files",
            "object_key": "uploads/test-file-123/test_script.sh",
            "expected_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
            "content": b"#!/bin/bash\necho 'Hello World'\n"
        }

    @patch('services.workers.tasks.file_operations.Minio')
    def test_download_file_from_minio_success(
        self,
        mock_minio_class: MagicMock,
        mock_minio_client: MagicMock,
        sample_file_data: dict[str, Any],
    ) -> None:
        """Test successful file download from MinIO."""
        # Arrange
        mock_minio_class.return_value = mock_minio_client
        mock_response = MagicMock()
        mock_response.read.return_value = sample_file_data["content"]
        mock_minio_client.get_object.return_value = mock_response

        # Act
        result = download_file_from_minio(
            bucket=sample_file_data["bucket"],
            object_key=sample_file_data["object_key"],
            file_id=sample_file_data["file_id"]
        )

        # Assert
        assert result["success"] is True
        assert result["file_path"] is not None
        assert result["file_size"] == len(sample_file_data["content"])
        mock_minio_client.get_object.assert_called_once_with(
            sample_file_data["bucket"],
            sample_file_data["object_key"]
        )

    @patch('services.workers.tasks.file_operations.Minio')
    def test_download_file_from_minio_failure(
        self,
        mock_minio_class: MagicMock,
        mock_minio_client: MagicMock,
        sample_file_data: dict[str, Any],
    ) -> None:
        """Test file download failure from MinIO."""
        # Arrange
        mock_minio_class.return_value = mock_minio_client
        mock_minio_client.get_object.side_effect = Exception("Connection failed")

        # Act
        result = download_file_from_minio(
            bucket=sample_file_data["bucket"],
            object_key=sample_file_data["object_key"],
            file_id=sample_file_data["file_id"]
        )

        # Assert
        assert result["success"] is False
        assert "error" in result
        assert "Connection failed" in result["error"]

    def test_validate_file_success(self, sample_file_data: dict[str, Any]) -> None:
        """Test successful file validation."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(sample_file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == len(sample_file_data["content"])
            assert result["file_hash"] == sample_file_data["expected_hash"]

        finally:
            Path(temp_file_path).unlink()

    def test_validate_file_hash_mismatch(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with hash mismatch."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"different content")
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is False
            assert "hash mismatch" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_validate_file_too_large(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with size limit exceeded."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(sample_file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=0.000001  # Very small limit
            )

            # Assert
            assert result["valid"] is False
            assert "too large" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_prepare_file_for_injection_success(self, sample_file_data: dict[str, Any]) -> None:
        """Test successful file preparation for injection."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(sample_file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Act
            result = prepare_file_for_injection(
                file_path=temp_file_path,
                target_path="/app/scripts/test_script.sh",
                permissions="0755"
            )

            # Assert
            assert result["success"] is True
            assert result["prepared_path"] is not None
            assert result["target_path"] == "/app/scripts/test_script.sh"
            assert result["permissions"] == "0755"

        finally:
            Path(temp_file_path).unlink()


class TestVMManagementTasks:
    """Test suite for VM management worker tasks."""

    @pytest.fixture
    def vm_config(self) -> dict[str, Any]:
        """Provide VM configuration data."""
        return {
            "vm_id": "test-vm-123",
            "name": "test-vm",
            "memory": 1024,
            "cpus": 2,
            "disk_size": "20GB",
            "os_type": "ubuntu",
            "network": "turdpartycollab_net"
        }

    @patch('services.workers.tasks.vm_management.subprocess.run')
    def test_create_vm_success(
        self,
        mock_subprocess: MagicMock,
        vm_config: dict[str, Any],
    ) -> None:
        """Test successful VM creation."""
        # Arrange
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "VM created successfully"

        # Act
        result = create_vm(vm_config)

        # Assert
        assert result["success"] is True
        assert result["vm_id"] == vm_config["vm_id"]
        assert "created successfully" in result["message"]
        mock_subprocess.assert_called_once()

    @patch('services.workers.tasks.vm_management.subprocess.run')
    def test_create_vm_failure(
        self,
        mock_subprocess: MagicMock,
        vm_config: dict[str, Any],
    ) -> None:
        """Test VM creation failure."""
        # Arrange
        mock_subprocess.return_value.returncode = 1
        mock_subprocess.return_value.stderr = "VM creation failed"

        # Act
        result = create_vm(vm_config)

        # Assert
        assert result["success"] is False
        assert "error" in result
        assert "VM creation failed" in result["error"]

    @patch('services.workers.tasks.vm_management.subprocess.run')
    def test_start_vm_success(
        self,
        mock_subprocess: MagicMock,
        vm_config: dict[str, Any],
    ) -> None:
        """Test successful VM start."""
        # Arrange
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "VM started"

        # Act
        result = start_vm(vm_config["vm_id"])

        # Assert
        assert result["success"] is True
        assert result["status"] == "running"
        mock_subprocess.assert_called_once()

    @patch('services.workers.tasks.vm_management.subprocess.run')
    def test_get_vm_status_running(
        self,
        mock_subprocess: MagicMock,
        vm_config: dict[str, Any],
    ) -> None:
        """Test getting VM status when running."""
        # Arrange
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "running"

        # Act
        result = get_vm_status(vm_config["vm_id"])

        # Assert
        assert result["status"] == "running"
        assert result["vm_id"] == vm_config["vm_id"]

    @patch('services.workers.tasks.vm_management.delete_vm')
    def test_delete_vm_success(
        self,
        mock_delete_vm: MagicMock,
        vm_config: dict[str, Any],
    ) -> None:
        """Test successful VM deletion."""
        # Arrange
        mock_delete_vm.return_value = {
            "success": True,
            "vm_id": vm_config["vm_id"],
            "message": "VM deleted successfully"
        }

        # Act
        result = delete_vm(vm_config["vm_id"])

        # Assert
        assert result["success"] is True
        assert result["vm_id"] == vm_config["vm_id"]
        mock_delete_vm.assert_called_once()


class TestInjectionTasks:
    """Test suite for injection worker tasks."""

    @pytest.fixture
    def injection_config(self) -> dict[str, Any]:
        """Provide injection configuration data."""
        return {
            "injection_id": "test-injection-123",
            "vm_id": "test-vm-123",
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755"
        }

    @patch('services.workers.tasks.injection_tasks.inject_file')
    def test_inject_file_success(
        self,
        mock_inject_file: MagicMock,
        injection_config: dict[str, Any],
    ) -> None:
        """Test successful file injection to VM."""
        # Arrange
        mock_inject_file.return_value = {
            "success": True,
            "message": "File injected successfully",
            "injection_id": injection_config["injection_id"]
        }

        # Act
        result = inject_file(
            workflow_job_id=injection_config["injection_id"],
            file_upload_id="test-file-123"
        )

        # Assert
        assert result["success"] is True
        assert result["injection_id"] == injection_config["injection_id"]
        assert "injected successfully" in result["message"]

    @patch('services.workers.tasks.injection_tasks.inject_file')
    def test_inject_file_failure(
        self,
        mock_inject_file: MagicMock,
        injection_config: dict[str, Any],
    ) -> None:
        """Test file injection failure."""
        # Arrange
        mock_inject_file.side_effect = Exception("Permission denied")

        # Act & Assert
        try:
            inject_file(
                workflow_job_id=injection_config["injection_id"],
                file_upload_id="test-file-123"
            )
            assert False, "Expected exception was not raised"
        except Exception as e:
            assert "Permission denied" in str(e)

    @patch('services.workers.tasks.injection_tasks.execute_injected_file')
    def test_execute_injected_file_success(
        self,
        mock_execute: MagicMock,
        injection_config: dict[str, Any],
    ) -> None:
        """Test successful file execution."""
        # Arrange
        mock_execute.return_value = {
            "success": True,
            "status": "completed",
            "execution_result": "File executed successfully"
        }

        # Act
        result = execute_injected_file(
            workflow_job_id=injection_config["injection_id"],
            execution_config={"timeout": 300}
        )

        # Assert
        assert result["success"] is True
        assert result["status"] == "completed"


