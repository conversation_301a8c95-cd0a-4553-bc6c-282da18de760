"""
DEPRECATED: Unit tests for worker services using MOCKS.

⚠️  DEPRECATED: This file uses mocks instead of real implementations.
Use test_worker_services_real.py for real service integration testing.

Tests the Celery worker tasks with MOCKS (deprecated).
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import sys
import tempfile
from pathlib import Path
from typing import Any
from unittest.mock import MagicMock, patch

import pytest

# Create mock modules to avoid Celery import issues
mock_celery = MagicMock()
mock_celery.shared_task = lambda *args, **kwargs: lambda func: func
mock_celery.utils.log.get_task_logger = lambda name: MagicMock()

# Patch sys.modules before importing worker tasks
with patch.dict('sys.modules', {
    'celery': mock_celery,
    'celery.utils': MagicMock(),
    'celery.utils.log': MagicMock(),
}):
    # Mock the get_task_logger function
    with patch('services.workers.tasks.file_operations.get_task_logger', return_value=MagicMock()):
        # Import only the validate_file function which doesn't use Celery
        from services.workers.tasks.file_operations import validate_file


class TestFileValidation:
    """Test suite for file validation functions (non-Celery)."""

    @pytest.fixture
    def sample_file_data(self) -> dict[str, Any]:
        """Provide sample file data."""
        import hashlib
        content = b"#!/bin/bash\necho 'Hello World'\n"
        expected_hash = hashlib.sha256(content).hexdigest()
        return {
            "expected_hash": expected_hash,
            "content": content
        }

    def test_validate_file_success(self, sample_file_data: dict[str, Any]) -> None:
        """Test successful file validation."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(sample_file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == len(sample_file_data["content"])
            assert result["file_hash"] == sample_file_data["expected_hash"]

        finally:
            Path(temp_file_path).unlink()

    def test_validate_file_hash_mismatch(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with hash mismatch."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"different content")
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is False
            assert "hash mismatch" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_validate_file_too_large(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with size limit exceeded."""
        # Arrange
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(sample_file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=sample_file_data["expected_hash"],
                max_size_mb=0.000001  # Very small limit
            )

            # Assert
            assert result["valid"] is False
            assert "too large" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_validate_file_not_found(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with non-existent file."""
        # Act
        result = validate_file(
            file_path="/non/existent/file.txt",
            expected_hash=sample_file_data["expected_hash"],
            max_size_mb=10
        )

        # Assert
        assert result["valid"] is False
        assert "not found" in result["error"].lower()

    def test_validate_file_empty_path(self, sample_file_data: dict[str, Any]) -> None:
        """Test file validation with empty file path."""
        # Act
        result = validate_file(
            file_path="",
            expected_hash=sample_file_data["expected_hash"],
            max_size_mb=10
        )

        # Assert
        assert result["valid"] is False
        assert "not found" in result["error"].lower()


class TestWorkerUtilities:
    """Test suite for worker utility functions."""

    def test_file_size_calculation(self) -> None:
        """Test file size calculation accuracy."""
        # Arrange
        test_content = b"Test content for size calculation"

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            import os
            calculated_size = os.path.getsize(temp_file_path)

            # Assert
            assert calculated_size == len(test_content)

        finally:
            Path(temp_file_path).unlink()

    def test_hash_consistency(self) -> None:
        """Test that hash calculation is consistent."""
        # Arrange
        import hashlib
        test_content = b"Consistent hash test content"

        # Act
        hash1 = hashlib.sha256(test_content).hexdigest()
        hash2 = hashlib.sha256(test_content).hexdigest()

        # Assert
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA256 produces 64-character hex string

    def test_file_operations_with_permissions(self) -> None:
        """Test file operations with different permissions."""
        # Arrange
        test_content = b"Permission test content"

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act - Change file permissions
            import os
            import stat

            # Make file read-only
            os.chmod(temp_file_path, stat.S_IRUSR | stat.S_IRGRP | stat.S_IROTH)

            # Verify we can still read the file
            with open(temp_file_path, 'rb') as f:
                read_content = f.read()

            # Assert
            assert read_content == test_content

        finally:
            # Restore write permissions before deletion
            import os
            import stat
            os.chmod(temp_file_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
            Path(temp_file_path).unlink()




