"""
Real worker services unit tests.

Tests the Celery worker tasks with REAL implementations.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import tempfile
from pathlib import Path
from typing import Any
import uuid

import pytest

from services.workers.tasks.file_operations import validate_file

# Import real fixtures
pytest_plugins = ["tests.integration.conftest_real"]


@pytest.mark.real
class TestRealFileOperationTasks:
    """Test suite for file operation worker tasks with real implementations."""

    def test_real_validate_file_success(self) -> None:
        """Test real file validation with actual file operations."""
        # Arrange
        import hashlib
        test_content = b"#!/bin/bash\necho 'Hello World'\n"
        expected_hash = hashlib.sha256(test_content).hexdigest()
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=expected_hash,
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == len(test_content)
            assert result["file_hash"] == expected_hash

        finally:
            Path(temp_file_path).unlink()

    def test_real_validate_file_hash_mismatch(self) -> None:
        """Test real file validation with hash mismatch."""
        # Arrange
        test_content = b"different content"
        wrong_hash = "0000000000000000000000000000000000000000000000000000000000000000"  # Wrong hash
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=wrong_hash,
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is False
            assert "hash mismatch" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_real_validate_file_too_large(self) -> None:
        """Test real file validation with size limit exceeded."""
        # Arrange
        test_content = b"A" * 1000  # 1KB content
        expected_hash = "b35e5a0e0e8c7b6c3f8b8f8b8f8b8f8b8f8b8f8b8f8b8f8b8f8b8f8b8f8b8f8b"
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=expected_hash,
                max_size_mb=0.0001  # Very small limit (0.1KB)
            )

            # Assert
            assert result["valid"] is False
            assert "too large" in result["error"].lower()

        finally:
            Path(temp_file_path).unlink()

    def test_real_validate_file_not_found(self) -> None:
        """Test real file validation with non-existent file."""
        # Arrange
        non_existent_path = f"/tmp/non_existent_{uuid.uuid4().hex}.txt"
        expected_hash = "dummy_hash"

        # Act
        result = validate_file(
            file_path=non_existent_path,
            expected_hash=expected_hash,
            max_size_mb=10
        )

        # Assert
        assert result["valid"] is False
        assert "not found" in result["error"].lower()

    def test_real_validate_file_empty_file(self) -> None:
        """Test real file validation with empty file."""
        # Arrange
        import hashlib
        test_content = b""
        expected_hash = hashlib.sha256(test_content).hexdigest()
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=expected_hash,
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == 0
            assert result["file_hash"] == expected_hash

        finally:
            Path(temp_file_path).unlink()

    def test_real_validate_file_binary_content(self) -> None:
        """Test real file validation with binary content."""
        # Arrange
        import hashlib
        test_content = b"\x00\x01\x02\x03\xFF\xFE\xFD"  # Binary content
        expected_hash = hashlib.sha256(test_content).hexdigest()
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=expected_hash,
                max_size_mb=10
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == len(test_content)
            assert result["file_hash"] == expected_hash

        finally:
            Path(temp_file_path).unlink()

    def test_real_validate_file_large_file(self) -> None:
        """Test real file validation with larger file."""
        # Arrange
        import hashlib
        test_content = b"A" * 10000  # 10KB content
        expected_hash = hashlib.sha256(test_content).hexdigest()
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            result = validate_file(
                file_path=temp_file_path,
                expected_hash=expected_hash,
                max_size_mb=1  # 1MB limit
            )

            # Assert
            assert result["valid"] is True
            assert result["file_size"] == len(test_content)
            assert result["file_hash"] == expected_hash

        finally:
            Path(temp_file_path).unlink()


@pytest.mark.real
class TestRealFileOperationsIntegration:
    """Integration tests for file operations with real MinIO."""

    async def test_real_file_download_and_validation(
        self,
        real_minio_client,
        real_test_bucket: str,
    ) -> None:
        """Test real file download from MinIO and validation."""
        # Arrange
        import hashlib
        test_content = b"#!/bin/bash\necho 'Real integration test'\n"
        expected_hash = hashlib.sha256(test_content).hexdigest()
        object_name = f"test-files/{uuid.uuid4().hex}.sh"
        
        # Upload test file to MinIO
        from io import BytesIO
        real_minio_client.put_object(
            real_test_bucket, object_name, BytesIO(test_content), len(test_content)
        )

        try:
            # Act - Download file
            response = real_minio_client.get_object(real_test_bucket, object_name)
            downloaded_content = response.read()
            
            # Create temporary file for validation
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(downloaded_content)
                temp_file_path = temp_file.name

            try:
                # Validate downloaded file
                result = validate_file(
                    file_path=temp_file_path,
                    expected_hash=expected_hash,
                    max_size_mb=10
                )

                # Assert
                assert result["valid"] is True
                assert result["file_size"] == len(test_content)
                assert result["file_hash"] == expected_hash
                assert downloaded_content == test_content

            finally:
                Path(temp_file_path).unlink()

        finally:
            # Cleanup MinIO object
            real_minio_client.remove_object(real_test_bucket, object_name)

    async def test_real_concurrent_file_validation(self) -> None:
        """Test real concurrent file validation operations."""
        import asyncio
        import hashlib
        
        # Arrange
        num_files = 5
        test_files = []
        
        for i in range(num_files):
            content = f"Test file {i} content\n".encode()
            expected_hash = hashlib.sha256(content).hexdigest()
            
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(content)
                test_files.append((temp_file.name, expected_hash))

        try:
            # Act - Validate files concurrently
            async def validate_single_file(file_path: str, expected_hash: str) -> dict:
                # Run validation in thread pool since it's CPU-bound
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(
                    None, validate_file, file_path, expected_hash, 10
                )

            tasks = [
                validate_single_file(file_path, expected_hash)
                for file_path, expected_hash in test_files
            ]
            
            results = await asyncio.gather(*tasks)

            # Assert
            assert len(results) == num_files
            for result in results:
                assert result["valid"] is True

        finally:
            # Cleanup
            for file_path, _ in test_files:
                Path(file_path).unlink()

    def test_real_file_validation_performance(self) -> None:
        """Test real file validation performance."""
        import time
        import hashlib
        
        # Arrange
        test_content = b"Performance test content\n" * 100  # Larger content
        expected_hash = hashlib.sha256(test_content).hexdigest()
        
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name

        try:
            # Act
            start_time = time.time()
            
            # Validate file multiple times
            for _ in range(10):
                result = validate_file(
                    file_path=temp_file_path,
                    expected_hash=expected_hash,
                    max_size_mb=10
                )
                assert result["valid"] is True
            
            end_time = time.time()
            
            # Assert
            duration = end_time - start_time
            validations_per_second = 10 / duration
            
            # Should be able to validate at least 5 files per second
            assert validations_per_second >= 5, f"Performance too slow: {validations_per_second:.2f} validations/sec"

        finally:
            Path(temp_file_path).unlink()
