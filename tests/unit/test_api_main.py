"""
Unit tests for the main API module
Tests the FastAPI application creation and configuration
"""

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

# Import the API main module
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from services.api.src.main import create_application


class TestAPIMain:
    """Test suite for API main module"""
    
    def test_create_application_returns_fastapi_instance(self):
        """Test that create_application returns a FastAPI instance"""
        app = create_application()
        assert isinstance(app, FastAPI)
        assert app.title == "TurdParty API"
        assert app.version == "1.0.0"
    
    def test_application_has_cors_middleware(self):
        """Test that the application has CORS middleware configured"""
        app = create_application()
        
        # Check that middleware is configured
        middleware_types = [type(middleware.cls) for middleware in app.user_middleware]
        from starlette.middleware.cors import CORSMiddleware
        assert CORSMiddleware in middleware_types
    
    def test_application_has_health_routes(self):
        """Test that health routes are included"""
        app = create_application()
        
        # Get all routes
        routes = [route.path for route in app.routes]
        
        # Check for health routes
        health_routes = [route for route in routes if '/health' in route]
        assert len(health_routes) > 0
    
    def test_application_has_v1_routes(self):
        """Test that v1 API routes are included"""
        app = create_application()
        
        # Get all routes
        routes = [route.path for route in app.routes]
        
        # Check for v1 routes
        v1_routes = [route for route in routes if '/api/v1' in route]
        assert len(v1_routes) > 0
    
    def test_health_endpoint_responds(self):
        """Test that the health endpoint responds correctly"""
        app = create_application()
        client = TestClient(app)
        
        response = client.get("/health/")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "service" in data
        assert "timestamp" in data
    
    def test_docs_endpoint_exists(self):
        """Test that the docs endpoint exists"""
        app = create_application()
        client = TestClient(app)
        
        response = client.get("/docs")
        # Should either return 200 (docs found) or redirect
        assert response.status_code in [200, 307, 404]  # 404 if docs not built
    
    def test_openapi_json_endpoint(self):
        """Test that the OpenAPI JSON endpoint works"""
        app = create_application()
        client = TestClient(app)
        
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "TurdParty API"


class TestAPIConfiguration:
    """Test suite for API configuration"""
    
    def test_application_title_and_description(self):
        """Test application metadata"""
        app = create_application()
        
        assert app.title == "TurdParty API"
        assert "File analysis workflow API" in app.description
        assert app.version == "1.0.0"
    
    def test_docs_and_redoc_urls(self):
        """Test documentation URLs are configured"""
        app = create_application()
        
        assert app.docs_url == "/docs"
        assert app.redoc_url == "/redoc"
    
    def test_exception_handler_configured(self):
        """Test that global exception handler is configured"""
        app = create_application()
        
        # Check that exception handlers are configured
        assert Exception in app.exception_handlers
    
    def test_static_files_mount_configuration(self):
        """Test static files mounting logic"""
        app = create_application()
        
        # Check if docs mount exists (depends on whether docs are built)
        mount_paths = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'app'):
                mount_paths.append(route.path)
        
        # The mount may or may not exist depending on docs build status
        # This test just ensures the mounting logic doesn't crash
        assert isinstance(mount_paths, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
