# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

import os
import sys

# Add project root and services to Python path for autodoc
project_root = os.path.abspath('../..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'services'))
sys.path.insert(0, os.path.join(project_root, 'services', 'api', 'src'))
sys.path.insert(0, os.path.join(project_root, 'tests'))

project = '💩🎉TurdParty🎉💩 Testing Framework'
copyright = '2025, TurdParty Security Team'
author = 'TurdParty Security Team'
release = '1.0.0'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.ifconfig',
    'sphinx.ext.githubpages',
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']

# Theme options
html_theme_options = {
    'canonical_url': '',
    'analytics_id': '',
    'logo_only': False,
    'display_version': True,
    'prev_next_buttons_location': 'bottom',
    'style_external_links': False,
    'vcs_pageview_mode': '',
    'style_nav_header_background': '#2980B9',
    # Toc options
    'collapse_navigation': True,
    'sticky_navigation': True,
    'navigation_depth': 4,
    'includehidden': True,
    'titles_only': False
}

# Custom CSS (if available)
html_css_files = [
    'custom.css',
]

# Custom JavaScript (if available)
html_js_files = [
    'custom.js',
]

# -- Extension configuration -------------------------------------------------

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = False
napoleon_type_aliases = None
napoleon_attr_annotations = True

# Autodoc settings
autodoc_default_options = {
    'members': True,
    'member-order': 'bysource',
    'special-members': '__init__',
    'undoc-members': True,
    'exclude-members': '__weakref__'
}

# Intersphinx mapping
intersphinx_mapping = {
    'python': ('https://docs.python.org/3/', None),
    'pytest': ('https://docs.pytest.org/en/stable/', None),
    'hypothesis': ('https://hypothesis.readthedocs.io/en/latest/', None),
    'locust': ('https://docs.locust.io/en/stable/', None),
}

# MyST parser settings
myst_enable_extensions = [
    "amsmath",
    "colon_fence",
    "deflist",
    "dollarmath",
    "html_admonition",
    "html_image",
    "linkify",
    "replacements",
    "smartquotes",
    "substitution",
    "tasklist",
]

# Todo extension settings
todo_include_todos = True

# Copy button settings (if extension available)
copybutton_prompt_text = r">>> |\.\.\. |\$ |In \[\d*\]: | {2,5}\.\.\.: | {5,8}: "
copybutton_prompt_is_regexp = True

# Mermaid settings (if extension available)
mermaid_output_format = 'png'
mermaid_params = ['--theme', 'default', '--width', '600', '--backgroundColor', 'transparent']

# -- Custom configuration ---------------------------------------------------

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = ['_static']

# Add custom roles
def setup(app):
    app.add_css_file('custom.css')
    app.add_js_file('custom.js')

# Source file suffixes
source_suffix = '.rst'

# Master document
master_doc = 'index'

# Language
language = 'en'

# Pygments style
pygments_style = 'sphinx'

# HTML title
html_title = f'{project} v{release}'

# HTML short title
html_short_title = '💩🎉TurdParty🎉💩 Testing'

# HTML logo (if available)
html_logo = '_static/logo.png'

# HTML favicon (if available)
html_favicon = '_static/favicon.ico'

# Show source link
html_show_sourcelink = True

# Show copyright
html_show_copyright = True

# Show sphinx
html_show_sphinx = True

# Search language
html_search_language = 'en'

# Search options
html_search_options = {'type': 'default'}

# Output file base name for HTML help builder
htmlhelp_basename = 'TurdPartyTestingDoc'

# LaTeX elements
latex_elements = {
    'papersize': 'letterpaper',
    'pointsize': '10pt',
    'preamble': '',
    'fncychap': '',
    'maketitle': '',
    'printindex': '',
}

# LaTeX documents
latex_documents = [
    (master_doc, 'TurdPartyTesting.tex', 'TurdParty Testing Framework Documentation',
     'TurdParty Team', 'manual'),
]

# Manual pages
man_pages = [
    (master_doc, 'turdpartytesting', 'TurdParty Testing Framework Documentation',
     [author], 1)
]

# Texinfo documents
texinfo_documents = [
    (master_doc, 'TurdPartyTesting', 'TurdParty Testing Framework Documentation',
     author, 'TurdPartyTesting', 'Comprehensive testing framework for TurdParty.',
     'Miscellaneous'),
]

# Epub
epub_title = project
epub_author = author
epub_publisher = author
epub_copyright = copyright

# Exclude patterns for epub
epub_exclude_files = ['search.html']

# -- Custom directives and roles --------------------------------------------

# Add custom CSS classes
html_css_files = [
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'custom.css',
]

# Version info
version = '1.0'
release = '1.0.0'

# Build info
html_last_updated_fmt = '%b %d, %Y'

# Show "Edit on GitHub" links
html_context = {
    "display_github": True,
    "github_user": "tenbahtsecurity",
    "github_repo": "turdparty",
    "github_version": "master",
    "conf_py_path": "/tests/docs/",
}
