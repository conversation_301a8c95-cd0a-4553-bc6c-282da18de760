<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Real VM Testing Documentation &mdash; 💩🎉TurdParty🎉💩 Testing Framework v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />
      <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=67647e4a" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
        <script src="_static/custom.js?v=3a49a312"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Property-Based Testing" href="property-testing.html" />
    <link rel="prev" title="Unit Testing" href="unit-testing.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            💩🎉TurdParty🎉💩 Testing Framework
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Quick Start</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation &amp; Setup</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Core Testing</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="unit-testing.html">Unit Testing</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Real VM Testing Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#test-architecture">Test Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vm-test-components">VM Test Components</a></li>
<li class="toctree-l3"><a class="reference internal" href="#test-categories">Test Categories</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#quick-start">Quick Start</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="#core-vm-tests">Core VM Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#comprehensive-test-suite">Comprehensive Test Suite</a></li>
<li class="toctree-l3"><a class="reference internal" href="#python-integration-tests">Python Integration Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#test-implementation">Test Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#docker-container-tests">Docker Container Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vagrant-vm-tests">Vagrant VM Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#file-injection-tests">File Injection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#test-results">Test Results</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#successful-execution">Successful Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#validation-coverage">Validation Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#test-markers">Test Markers</a></li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#debug-commands">Debug Commands</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-design">Test Design</a></li>
<li class="toctree-l3"><a class="reference internal" href="#resource-management">Resource Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#related-documentation">Related Documentation</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="property-testing.html">Property-Based Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="performance-testing.html">Performance Testing</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">💩🎉TurdParty🎉💩 Testing Framework</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Real VM Testing Documentation</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/tenbahtsecurity/turdparty/blob/master/tests/docs/real-vm-testing.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="real-vm-testing-documentation">
<h1>Real VM Testing Documentation<a class="headerlink" href="#real-vm-testing-documentation" title="Link to this heading"></a></h1>
<p>This document provides comprehensive guidance for TurdParty’s real VM testing suite - a zero-mock testing approach using actual Docker containers and Vagrant VMs.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>TurdParty implements <strong>zero-mock VM testing</strong> to ensure production-equivalent validation:</p>
<ul class="simple">
<li><p><strong>Real Docker Containers</strong> - Actual container creation and management</p></li>
<li><p><strong>Real Vagrant VMs</strong> - Full virtual machines with VirtualBox</p></li>
<li><p><strong>Production Equivalence</strong> - Tests mirror production environments exactly</p></li>
<li><p><strong>No Simulations</strong> - All operations use real infrastructure components</p></li>
</ul>
</section>
<section id="test-architecture">
<h2>Test Architecture<a class="headerlink" href="#test-architecture" title="Link to this heading"></a></h2>
<section id="vm-test-components">
<h3>VM Test Components<a class="headerlink" href="#vm-test-components" title="Link to this heading"></a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>tests/integration/
├── test_real_vm_operations.py      # Docker container tests
├── test_real_vagrant_vms.py        # Vagrant VM tests
└── test_real_vm_workflow.py        # End-to-end workflow tests

scripts/
├── test-real-vm-core.sh           # Core VM functionality
├── test-real-docker-vms.sh        # Docker-specific tests
└── run-real-vm-tests.sh           # Comprehensive test runner
</pre></div>
</div>
</section>
<section id="test-categories">
<h3>Test Categories<a class="headerlink" href="#test-categories" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Docker VM Tests</strong></dt><dd><ul class="simple">
<li><p>Ubuntu 20.04/22.04 container testing</p></li>
<li><p>Alpine Linux minimal container validation</p></li>
<li><p>CentOS enterprise container testing</p></li>
<li><p>Resource limit enforcement (memory/CPU)</p></li>
<li><p>Network connectivity validation</p></li>
<li><p>File operations and injection testing</p></li>
</ul>
</dd>
<dt><strong>Vagrant VM Tests</strong></dt><dd><ul class="simple">
<li><p>VirtualBox VM creation and management</p></li>
<li><p>SSH connectivity and command execution</p></li>
<li><p>VM provisioning with custom scripts</p></li>
<li><p>Windows VM testing (optional)</p></li>
<li><p>Complete VM lifecycle operations</p></li>
</ul>
</dd>
<dt><strong>Workflow Integration Tests</strong></dt><dd><ul class="simple">
<li><p>MinIO file storage operations</p></li>
<li><p>File injection into running VMs</p></li>
<li><p>Concurrent VM processing</p></li>
<li><p>Resource monitoring and metrics</p></li>
<li><p>Auto-termination after 30 minutes</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="quick-start">
<h2>Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Ensure required software is installed and running:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check Docker availability</span>
docker<span class="w"> </span>--version<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>docker<span class="w"> </span>info

<span class="c1"># Check Vagrant availability</span>
vagrant<span class="w"> </span>--version

<span class="c1"># Check VirtualBox availability</span>
VBoxManage<span class="w"> </span>--version

<span class="c1"># Check TurdParty API availability</span>
curl<span class="w"> </span>http://localhost:8000/health/
</pre></div>
</div>
</section>
<section id="core-vm-tests">
<h3>Core VM Tests<a class="headerlink" href="#core-vm-tests" title="Link to this heading"></a></h3>
<p>Run the essential VM functionality tests:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Execute core VM tests (recommended)</span>
./scripts/test-real-vm-core.sh
</pre></div>
</div>
<p>Expected output:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>🎯<span class="w"> </span>Core<span class="w"> </span>Real<span class="w"> </span>VM<span class="w"> </span>Tests<span class="w"> </span>-<span class="w"> </span>PASSED<span class="w"> </span><span class="m">6</span>/6
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">1</span>:<span class="w"> </span>Ubuntu<span class="w"> </span>Docker<span class="w"> </span>VM<span class="w"> </span>Creation<span class="w"> </span>-<span class="w"> </span>PASSED
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">2</span>:<span class="w"> </span>Alpine<span class="w"> </span>Linux<span class="w"> </span>Docker<span class="w"> </span>VM<span class="w"> </span>Creation<span class="w"> </span>-<span class="w"> </span>PASSED
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">3</span>:<span class="w"> </span>File<span class="w"> </span>Operations<span class="w"> </span><span class="k">in</span><span class="w"> </span>VM<span class="w"> </span>-<span class="w"> </span>PASSED
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">4</span>:<span class="w"> </span>Network<span class="w"> </span>Connectivity<span class="w"> </span>-<span class="w"> </span>PASSED
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">5</span>:<span class="w"> </span>VM<span class="w"> </span>Lifecycle<span class="w"> </span>Operations<span class="w"> </span>-<span class="w"> </span>PASSED
✅<span class="w"> </span>Test<span class="w"> </span><span class="m">6</span>:<span class="w"> </span>Resource<span class="w"> </span>Monitoring<span class="w"> </span>-<span class="w"> </span>PASSED
</pre></div>
</div>
</section>
<section id="comprehensive-test-suite">
<h3>Comprehensive Test Suite<a class="headerlink" href="#comprehensive-test-suite" title="Link to this heading"></a></h3>
<p>Run all VM tests including Vagrant VMs:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run complete VM test suite</span>
./scripts/run-real-vm-tests.sh

<span class="c1"># Include Windows VM tests (requires Windows Vagrant box)</span>
./scripts/run-real-vm-tests.sh<span class="w"> </span>--windows
</pre></div>
</div>
</section>
<section id="python-integration-tests">
<h3>Python Integration Tests<a class="headerlink" href="#python-integration-tests" title="Link to this heading"></a></h3>
<p>Execute Python-based integration tests:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run with nix-shell for dependencies</span>
nix-shell<span class="w"> </span>-p<span class="w"> </span>python3Full<span class="w"> </span>python3Packages.pytest<span class="w"> </span>python3Packages.httpx<span class="w"> </span>python3Packages.docker<span class="w"> </span>python3Packages.minio<span class="w"> </span>--run<span class="w"> </span><span class="s2">&quot;python3 -m pytest tests/integration/ -v&quot;</span>

<span class="c1"># Run specific test modules</span>
python3<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/integration/test_real_vm_operations.py<span class="w"> </span>-v
python3<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/integration/test_real_vagrant_vms.py<span class="w"> </span>-v
python3<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/integration/test_real_vm_workflow.py<span class="w"> </span>-v
</pre></div>
</div>
</section>
</section>
<section id="test-implementation">
<h2>Test Implementation<a class="headerlink" href="#test-implementation" title="Link to this heading"></a></h2>
<section id="docker-container-tests">
<h3>Docker Container Tests<a class="headerlink" href="#docker-container-tests" title="Link to this heading"></a></h3>
<p>Real Docker container creation and management:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_docker_ubuntu_vm_lifecycle</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test complete Docker Ubuntu VM lifecycle.&quot;&quot;&quot;</span>

    <span class="c1"># Create VM via API</span>
    <span class="n">vm_data</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;test-ubuntu-</span><span class="si">{</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">())</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="s2">&quot;template&quot;</span><span class="p">:</span> <span class="s2">&quot;ubuntu:20.04&quot;</span><span class="p">,</span>
        <span class="s2">&quot;vm_type&quot;</span><span class="p">:</span> <span class="s2">&quot;docker&quot;</span><span class="p">,</span>
        <span class="s2">&quot;memory_mb&quot;</span><span class="p">:</span> <span class="mi">512</span><span class="p">,</span>
        <span class="s2">&quot;cpus&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
        <span class="s2">&quot;domain&quot;</span><span class="p">:</span> <span class="s2">&quot;TurdParty&quot;</span>
    <span class="p">}</span>

    <span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">API_BASE_URL</span><span class="si">}</span><span class="s2">/api/v1/vms/&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">vm_data</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">201</span>

    <span class="c1"># Verify actual Docker container exists</span>
    <span class="n">containers</span> <span class="o">=</span> <span class="n">DOCKER_CLIENT</span><span class="o">.</span><span class="n">containers</span><span class="o">.</span><span class="n">list</span><span class="p">(</span>
        <span class="n">filters</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;label&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;turdparty.vm.name=</span><span class="si">{</span><span class="n">vm_data</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">}</span>
    <span class="p">)</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">containers</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="vagrant-vm-tests">
<h3>Vagrant VM Tests<a class="headerlink" href="#vagrant-vm-tests" title="Link to this heading"></a></h3>
<p>Real VirtualBox VM operations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_vagrant_ubuntu_vm_lifecycle</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test complete Vagrant Ubuntu VM lifecycle.&quot;&quot;&quot;</span>

    <span class="c1"># Create Vagrantfile</span>
    <span class="n">create_vagrantfile</span><span class="p">(</span><span class="n">vm_dir</span><span class="p">,</span> <span class="s2">&quot;ubuntu/focal64&quot;</span><span class="p">,</span> <span class="n">memory_mb</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span> <span class="n">cpus</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

    <span class="c1"># Execute vagrant up</span>
    <span class="n">result</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">(</span>
        <span class="p">[</span><span class="s2">&quot;vagrant&quot;</span><span class="p">,</span> <span class="s2">&quot;up&quot;</span><span class="p">],</span>
        <span class="n">cwd</span><span class="o">=</span><span class="n">vm_dir</span><span class="p">,</span>
        <span class="n">capture_output</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">timeout</span><span class="o">=</span><span class="mi">600</span>
    <span class="p">)</span>

    <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span>

    <span class="c1"># Test SSH connectivity</span>
    <span class="n">ssh_result</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">(</span>
        <span class="p">[</span><span class="s2">&quot;vagrant&quot;</span><span class="p">,</span> <span class="s2">&quot;ssh&quot;</span><span class="p">,</span> <span class="s2">&quot;-c&quot;</span><span class="p">,</span> <span class="s2">&quot;cat /etc/os-release&quot;</span><span class="p">],</span>
        <span class="n">cwd</span><span class="o">=</span><span class="n">vm_dir</span><span class="p">,</span>
        <span class="n">capture_output</span><span class="o">=</span><span class="kc">True</span>
    <span class="p">)</span>
    <span class="k">assert</span> <span class="n">ssh_result</span><span class="o">.</span><span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="file-injection-tests">
<h3>File Injection Tests<a class="headerlink" href="#file-injection-tests" title="Link to this heading"></a></h3>
<p>Real file operations in VMs:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test_file_injection_to_docker_vm</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test injecting a real file into a Docker VM.&quot;&quot;&quot;</span>

    <span class="c1"># Create test file</span>
    <span class="n">test_content</span> <span class="o">=</span> <span class="s2">&quot;TurdParty VM test file&quot;</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;/tmp/test_file.txt&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">test_content</span><span class="p">)</span>

    <span class="c1"># Inject into container using docker cp</span>
    <span class="n">container</span><span class="o">.</span><span class="n">put_archive</span><span class="p">(</span><span class="s2">&quot;/tmp/&quot;</span><span class="p">,</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;/tmp/test_file.txt&quot;</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>

    <span class="c1"># Verify file exists in container</span>
    <span class="n">exec_result</span> <span class="o">=</span> <span class="n">container</span><span class="o">.</span><span class="n">exec_run</span><span class="p">(</span><span class="s2">&quot;cat /tmp/test_file.txt&quot;</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">exec_result</span><span class="o">.</span><span class="n">exit_code</span> <span class="o">==</span> <span class="mi">0</span>
    <span class="k">assert</span> <span class="n">test_content</span> <span class="ow">in</span> <span class="n">exec_result</span><span class="o">.</span><span class="n">output</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="test-results">
<h2>Test Results<a class="headerlink" href="#test-results" title="Link to this heading"></a></h2>
<section id="successful-execution">
<h3>Successful Execution<a class="headerlink" href="#successful-execution" title="Link to this heading"></a></h3>
<p>Core VM tests demonstrate production-ready functionality:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>🐳 Testing real Docker VM creation...
✅ Docker connected
📊 API Response: 201
✅ VM created: 43e0f9e2-c073-4efc-bd99-96704614205c
✅ Docker container found: turdparty_vm_real-test-1749559242
📊 Container status: running
✅ Operating System: Ubuntu 20.04.6 LTS
✅ TurdParty environment configured
✅ Memory limit: 512MB
✅ Alpine version: 3.21.3
✅ File creation successful
✅ Network connectivity validated
✅ Container lifecycle operations successful
</pre></div>
</div>
</section>
<section id="validation-coverage">
<h3>Validation Coverage<a class="headerlink" href="#validation-coverage" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Container Operations</strong></dt><dd><ul class="simple">
<li><p>Real Docker container creation and management</p></li>
<li><p>Ubuntu, Alpine, CentOS image support</p></li>
<li><p>Resource limit enforcement (memory/CPU)</p></li>
<li><p>TurdParty labeling and environment configuration</p></li>
</ul>
</dd>
<dt><strong>File System Operations</strong></dt><dd><ul class="simple">
<li><p>File creation, reading, and permissions</p></li>
<li><p>File injection using docker cp</p></li>
<li><p>Volume mounting and access validation</p></li>
</ul>
</dd>
<dt><strong>Network Operations</strong></dt><dd><ul class="simple">
<li><p>Container IP assignment and networking</p></li>
<li><p>External connectivity testing</p></li>
<li><p>Network isolation validation</p></li>
</ul>
</dd>
<dt><strong>Lifecycle Operations</strong></dt><dd><ul class="simple">
<li><p>Container start, stop, restart, pause</p></li>
<li><p>Graceful and forced termination</p></li>
<li><p>Resource cleanup and management</p></li>
</ul>
</dd>
<dt><strong>Resource Monitoring</strong></dt><dd><ul class="simple">
<li><p>Real-time CPU and memory usage</p></li>
<li><p>Container statistics collection</p></li>
<li><p>Resource limit validation</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="test-markers">
<h2>Test Markers<a class="headerlink" href="#test-markers" title="Link to this heading"></a></h2>
<p>Use pytest markers to run specific test categories:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run only Docker VM tests</span>
pytest<span class="w"> </span>tests/integration/<span class="w"> </span>-m<span class="w"> </span>docker

<span class="c1"># Run only Vagrant VM tests</span>
pytest<span class="w"> </span>tests/integration/<span class="w"> </span>-m<span class="w"> </span>vagrant

<span class="c1"># Run slow VM tests (full VMs)</span>
pytest<span class="w"> </span>tests/integration/<span class="w"> </span>-m<span class="w"> </span>slow

<span class="c1"># Skip Windows VM tests</span>
pytest<span class="w"> </span>tests/integration/<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;not windows&quot;</span>
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Docker daemon not running</strong></dt><dd><p>Start Docker service and verify with <code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">info</span></code></p>
</dd>
<dt><strong>Vagrant box not found</strong></dt><dd><p>Download required boxes: <code class="docutils literal notranslate"><span class="pre">vagrant</span> <span class="pre">box</span> <span class="pre">add</span> <span class="pre">ubuntu/focal64</span></code></p>
</dd>
<dt><strong>VirtualBox not available</strong></dt><dd><p>Install VirtualBox and verify with <code class="docutils literal notranslate"><span class="pre">VBoxManage</span> <span class="pre">--version</span></code></p>
</dd>
<dt><strong>Permission denied</strong></dt><dd><p>Add user to docker group: <code class="docutils literal notranslate"><span class="pre">sudo</span> <span class="pre">usermod</span> <span class="pre">-aG</span> <span class="pre">docker</span> <span class="pre">$USER</span></code></p>
</dd>
<dt><strong>VM creation timeout</strong></dt><dd><p>Increase timeout values for slow systems</p>
</dd>
</dl>
</section>
<section id="debug-commands">
<h3>Debug Commands<a class="headerlink" href="#debug-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check Docker containers</span>
docker<span class="w"> </span>ps<span class="w"> </span>-a<span class="w"> </span>--filter<span class="w"> </span><span class="s2">&quot;label=turdparty.vm=true&quot;</span>

<span class="c1"># Check Vagrant VMs</span>
vagrant<span class="w"> </span>global-status

<span class="c1"># View container logs</span>
docker<span class="w"> </span>logs<span class="w"> </span>&lt;container_id&gt;

<span class="c1"># Check VM test logs</span>
tail<span class="w"> </span>-f<span class="w"> </span>/tmp/turdparty_vm_tests/vm_tests_*.log

<span class="c1"># Monitor container resources</span>
docker<span class="w"> </span>stats<span class="w"> </span><span class="k">$(</span>docker<span class="w"> </span>ps<span class="w"> </span>--filter<span class="w"> </span><span class="s2">&quot;label=turdparty.vm=true&quot;</span><span class="w"> </span>-q<span class="k">)</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="test-design">
<h3>Test Design<a class="headerlink" href="#test-design" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Use real infrastructure</strong> - No mocks for VM operations</p></li>
<li><p><strong>Test resource limits</strong> - Verify memory/CPU enforcement</p></li>
<li><p><strong>Validate cleanup</strong> - Ensure proper resource cleanup</p></li>
<li><p><strong>Test error conditions</strong> - Handle failures gracefully</p></li>
<li><p><strong>Monitor performance</strong> - Track test execution times</p></li>
</ol>
</section>
<section id="resource-management">
<h3>Resource Management<a class="headerlink" href="#resource-management" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Cleanup after tests</strong> - Remove containers and VMs</p></li>
<li><p><strong>Use minimal resources</strong> - Start with small allocations</p></li>
<li><p><strong>Parallel execution</strong> - Run independent tests concurrently</p></li>
<li><p><strong>Timeout handling</strong> - Set appropriate timeouts for operations</p></li>
<li><p><strong>Resource monitoring</strong> - Track system resource usage</p></li>
</ol>
</section>
<section id="security-considerations">
<h3>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Isolated networks</strong> - Use dedicated test networks</p></li>
<li><p><strong>Temporary data</strong> - Use temporary files and directories</p></li>
<li><p><strong>Access controls</strong> - Limit VM access and permissions</p></li>
<li><p><strong>Clean environments</strong> - Start with fresh VM instances</p></li>
<li><p><strong>Audit logging</strong> - Log all VM operations for security</p></li>
</ol>
</section>
</section>
<section id="related-documentation">
<h2>Related Documentation<a class="headerlink" href="#related-documentation" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="unit-testing.html"><span class="doc">Unit Testing</span></a> - Unit testing guidelines</p></li>
<li><p><span class="xref std std-doc">integration-testing</span> - Integration testing approach</p></li>
<li><p><a class="reference internal" href="performance-testing.html"><span class="doc">Performance Testing</span></a> - Performance testing guide</p></li>
<li><p><a class="reference external" href="http://localhost:8000/docs">VM Management API</a> - Interactive API reference</p></li>
<li><p><a class="reference external" href="../TESTING.md">Main Testing Guide</a> - Comprehensive testing documentation</p></li>
</ul>
<p>This real VM testing approach ensures that TurdParty’s VM management system is thoroughly validated with production-equivalent infrastructure, providing confidence in deployment and operation.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="unit-testing.html" class="btn btn-neutral float-left" title="Unit Testing" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="property-testing.html" class="btn btn-neutral float-right" title="Property-Based Testing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Security Team.
      <span class="lastupdated">Last updated on Jan 01, 1980.
      </span></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>