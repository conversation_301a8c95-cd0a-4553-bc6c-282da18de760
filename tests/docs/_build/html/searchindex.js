Search.setIndex({"alltitles": {"API Reference": [[0, "api-reference"]], "Advanced Hypothesis Features": [[3, "advanced-hypothesis-features"]], "Advanced Load Testing": [[2, "advanced-load-testing"]], "Advanced Topics": [[0, "advanced-topics"]], "Architecture Overview": [[0, "architecture-overview"]], "Assertion Strategies": [[6, "assertion-strategies"]], "Assumptions": [[3, "assumptions"]], "Async Performance Testing": [[2, "async-performance-testing"]], "Basic Execution": [[2, "basic-execution"], [3, "basic-execution"], [6, "basic-execution"]], "Basic Functionality Tests": [[6, "basic-functionality-tests"]], "Basic Strategies": [[3, "basic-strategies"]], "Benchmark Comparison": [[2, "benchmark-comparison"]], "Benchmark Design": [[2, "benchmark-design"]], "Best Practices": [[2, "best-practices"], [3, "best-practices"], [5, "best-practices"], [6, "best-practices"]], "Code Quality Checks": [[4, "code-quality-checks"]], "Common Fixtures": [[6, "common-fixtures"]], "Common Issues": [[1, "common-issues"], [4, "common-issues"], [5, "common-issues"]], "Common Workflows": [[4, "common-workflows"]], "Complex Performance Scenarios": [[2, "complex-performance-scenarios"]], "Composite Strategies": [[3, "composite-strategies"]], "Comprehensive Test Suite": [[5, "comprehensive-test-suite"]], "Configuration": [[1, "configuration"], [3, "configuration"]], "Configuration Examples": [[4, "configuration-examples"]], "Continuous Integration": [[6, "continuous-integration"]], "Continuous Monitoring": [[2, "continuous-monitoring"]], "Continuous Performance Monitoring": [[2, "continuous-performance-monitoring"]], "Contributing": [[0, null]], "Core Concepts": [[3, "core-concepts"]], "Core Testing": [[0, null]], "Core VM Tests": [[5, "core-vm-tests"]], "Current Performance Metrics": [[2, "current-performance-metrics"]], "Current Test Status": [[0, "id1"]], "Custom Strategies": [[3, "custom-strategies"]], "Data Integrity Properties": [[3, "data-integrity-properties"]], "Debug Commands": [[5, "debug-commands"]], "Debugging Failures": [[3, "debugging-failures"]], "Debugging Tests": [[6, "debugging-tests"]], "Development": [[0, "development"]], "Development Environment": [[1, "development-environment"]], "Development Environment Setup": [[4, "development-environment-setup"]], "Development Workflow": [[4, "development-workflow"]], "Docker Container Tests": [[5, "docker-container-tests"]], "Docker Services (Optional)": [[1, "docker-services-optional"]], "Environment Setup": [[1, "environment-setup"]], "Example Test Structure": [[6, "example-test-structure"]], "Examples & Tutorials": [[0, "examples-tutorials"]], "Examples and Targeting": [[3, "examples-and-targeting"]], "Fast Test Execution": [[6, "fast-test-execution"]], "File Injection Model Properties": [[3, "file-injection-model-properties"]], "File Injection Tests": [[5, "file-injection-tests"]], "Fixtures and Test Data": [[6, "fixtures-and-test-data"]], "Getting Help": [[1, "getting-help"], [4, "getting-help"]], "Getting Started": [[0, "getting-started"]], "Hypothesis Configuration": [[4, "hypothesis-configuration"]], "Hypothesis Database": [[3, "hypothesis-database"]], "Hypothesis Strategies": [[3, "hypothesis-strategies"]], "IDE Configuration": [[1, "ide-configuration"]], "Indices and tables": [[0, "indices-and-tables"]], "Installation": [[4, "installation"]], "Installation & Setup": [[1, null]], "Installation Methods": [[1, "installation-methods"]], "Integration with CI/CD": [[3, "integration-with-ci-cd"]], "Key Features": [[0, "key-features"]], "Key Testing Patterns": [[6, "key-testing-patterns"]], "Linting and Formatting": [[4, "linting-and-formatting"]], "Load Test Strategy": [[2, "load-test-strategy"]], "Load Testing": [[4, "load-testing"]], "Load Testing with Locust": [[2, "load-testing-with-locust"]], "Locust Configuration": [[2, "locust-configuration"]], "Memory Monitoring": [[2, "memory-monitoring"]], "Memory Profiling": [[2, "memory-profiling"]], "Memory Usage Testing": [[2, "memory-usage-testing"]], "Method 1: Nix Development Environment (Recommended)": [[1, "method-1-nix-development-environment-recommended"]], "Method 2: pip Installation": [[1, "method-2-pip-installation"]], "Method 3: Development Dependencies Only": [[1, "method-3-development-dependencies-only"]], "Micro-Benchmarking with pytest-benchmark": [[2, "micro-benchmarking-with-pytest-benchmark"]], "Model Performance Testing": [[2, "model-performance-testing"]], "Model Validation Tests": [[6, "model-validation-tests"]], "Monitoring Test Performance": [[6, "monitoring-test-performance"]], "Monitoring and Metrics": [[3, "monitoring-and-metrics"]], "Need Help?": [[0, null]], "Next Steps": [[1, "next-steps"], [2, "next-steps"], [3, "next-steps"], [4, "next-steps"], [6, "next-steps"]], "Nix Environment Tools": [[1, "id1"]], "Our Property-Based Tests": [[3, "our-property-based-tests"]], "Output Formats": [[2, "output-formats"]], "Overview": [[2, "overview"], [3, "overview"], [5, "overview"], [6, "overview"]], "Parallel Execution": [[6, "parallel-execution"]], "Parameterised Performance Tests": [[2, "parameterised-performance-tests"]], "Performance Alerts": [[2, "performance-alerts"]], "Performance Benchmarks": [[2, "id1"], [4, "performance-benchmarks"]], "Performance Considerations": [[6, "performance-considerations"]], "Performance Optimisation": [[3, "performance-optimisation"]], "Performance Regression Testing": [[2, "performance-regression-testing"]], "Performance Testing": [[2, null], [4, "performance-testing"]], "Performance Thresholds": [[2, "performance-thresholds"]], "Platform-Specific Notes": [[1, "platform-specific-notes"]], "Pre-commit Hooks": [[4, "pre-commit-hooks"]], "Pre-commit Hooks Setup": [[1, "pre-commit-hooks-setup"]], "Prerequisites": [[1, "prerequisites"], [4, "prerequisites"], [5, "prerequisites"]], "Property-Based Testing": [[3, null]], "Property-Based Tests": [[4, "property-based-tests"]], "Pydantic Model Testing": [[6, "pydantic-model-testing"]], "Python Integration Tests": [[5, "python-integration-tests"]], "Quality Gates": [[6, "quality-gates"]], "Quick Commands": [[0, "quick-commands"]], "Quick Start": [[0, null], [5, "quick-start"]], "Quick Start Guide": [[4, null]], "Real VM Testing Documentation": [[5, null]], "Related Documentation": [[5, "related-documentation"]], "Resource Management": [[5, "resource-management"]], "Resources": [[3, "resources"]], "Ruff Configuration": [[4, "ruff-configuration"]], "Running Load Tests": [[2, "running-load-tests"]], "Running Marked Tests": [[6, "running-marked-tests"]], "Running Performance Tests": [[2, "running-performance-tests"]], "Running Property-Based Tests": [[3, "running-property-based-tests"]], "Running Unit Tests": [[6, "running-unit-tests"]], "Running Your First Tests": [[4, "running-your-first-tests"]], "Security Considerations": [[5, "security-considerations"]], "Security Scanning": [[4, "security-scanning"]], "Settings and Profiles": [[3, "settings-and-profiles"]], "Shell Aliases": [[1, "shell-aliases"]], "Stateful Testing": [[3, "stateful-testing"]], "Strategy Design": [[3, "strategy-design"]], "Successful Execution": [[5, "successful-execution"]], "Support & Contributing": [[0, "support-contributing"]], "Supported Platforms": [[1, "supported-platforms"]], "System Requirements": [[1, "system-requirements"]], "Test All Components": [[1, "test-all-components"]], "Test Architecture": [[5, "test-architecture"]], "Test Categories": [[5, "test-categories"]], "Test Coverage": [[4, "test-coverage"]], "Test Data Management": [[6, "test-data-management"]], "Test Design": [[5, "test-design"]], "Test Implementation": [[5, "test-implementation"]], "Test Installation": [[1, "test-installation"]], "Test Markers": [[5, "test-markers"], [6, "test-markers"]], "Test Organization": [[6, "test-organization"]], "Test Results": [[5, "test-results"]], "Test Results Dashboard": [[0, "test-results-dashboard"]], "Test Structure": [[2, "test-structure"], [3, "test-structure"], [6, "test-structure"]], "Testing Framework": [[0, "testing-framework"]], "Testing New Features": [[4, "testing-new-features"]], "Tool Verification": [[1, "tool-verification"]], "Tools & Configuration": [[0, "tools-configuration"]], "Traditional Testing vs Property-Based Testing": [[3, "traditional-testing-vs-property-based-testing"]], "Troubleshooting": [[1, "troubleshooting"], [4, "troubleshooting"], [5, "troubleshooting"]], "Type Checking": [[4, "type-checking"]], "Unit Testing": [[6, null]], "Unit Tests": [[4, "unit-tests"]], "User Scenarios": [[2, "user-scenarios"]], "Using Fixtures": [[6, "using-fixtures"]], "Using the Modern Test Runner": [[4, "using-the-modern-test-runner"]], "VM Test Components": [[5, "vm-test-components"]], "Vagrant VM Tests": [[5, "vagrant-vm-tests"]], "Validation Coverage": [[5, "validation-coverage"]], "Validation Properties": [[3, "validation-properties"]], "Verification": [[1, "verification"]], "What is Property-Based Testing?": [[3, "what-is-property-based-testing"]], "With Coverage": [[6, "with-coverage"]], "Writing Good Properties": [[3, "writing-good-properties"]], "pytest Configuration": [[1, "pytest-configuration"], [4, "pytest-configuration"]], "\ud83d\udca9\ud83c\udf89TurdParty\ud83c\udf89\ud83d\udca9 Testing Framework Documentation": [[0, null]]}, "docnames": ["index", "installation", "performance-testing", "property-testing", "quickstart", "real-vm-testing", "unit-testing"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["index.rst", "installation.rst", "performance-testing.rst", "property-testing.rst", "quickstart.rst", "real-vm-testing.rst", "unit-testing.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [0, 1, 3, 4, 5, 6], "0": [0, 1, 2, 3, 4, 5, 6], "00": 2, "001": 2, "00z": 2, "01": 2, "0123456789abcdef": [3, 6], "01t10": 2, "02": [0, 3, 4], "03": 1, "04": [1, 5], "06": [0, 4, 6], "0600": [3, 6], "0640": 3, "0644": [3, 6], "0700": 3, "0750": 3, "0755": [2, 3, 6], "0777": [3, 6], "1": [0, 2, 3, 4, 5, 6], "10": [0, 2, 3, 4, 6], "100": [2, 3, 4], "1000": [2, 3, 4], "10000": 3, "100000": 2, "1000000": [2, 3], "100kb": 2, "1024": [2, 5, 6], "10240": 2, "102400": 2, "1048576": 2, "10\u03bc": 2, "11": [0, 1, 2, 4], "12": [1, 6], "12345": 3, "15": 1, "16": [0, 6], "1749559242": 5, "18\u03bc": 2, "1kb": 2, "1m": 2, "1mb": 2, "2": [2, 4, 5, 6], "20": [1, 5], "200": [2, 3], "201": [2, 5], "2024": [0, 2], "21": 5, "22": [2, 5], "24": 6, "25": 6, "255": 3, "2f": 2, "2m": 2, "3": [2, 4, 5, 6], "30": [2, 4, 5], "33": 4, "36": [0, 4, 6], "37": 0, "38": 0, "3\u03bc": 2, "4": [1, 4, 5, 6], "43e0f9e2": 5, "44": 2, "45": 2, "45k": 2, "484": 0, "4efc": 5, "5": [1, 2, 3, 4, 5, 6], "50": [2, 3], "5000": 3, "50\u03bc": 2, "512": 5, "512mb": 5, "5432": 1, "54m": [0, 2], "59": 0, "6": [0, 1, 4, 5], "600": 5, "6379": 1, "64": [2, 3, 6], "6400": 4, "64\u03bc": 2, "6500": 4, "67": 0, "7": [1, 4], "7\u03bc": 2, "8": 1, "80": [1, 6], "8000": [1, 2, 4, 5], "8089": 2, "850k": 2, "88": 4, "8m": 2, "9": [0, 3, 4, 6], "90": 0, "91": 0, "92": [0, 1], "9200": 1, "95": [], "96704614205c": 5, "97": 0, "9999": 2, "A": 2, "For": 1, "If": [1, 3], "In": [4, 6], "It": 3, "No": [0, 2, 3, 5, 6], "On": 1, "One": 6, "Or": [1, 4], "The": [1, 2, 6], "Then": 2, "These": 3, "_": [2, 3], "__init__": 3, "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3": 2, "aaa": 6, "abc123": 2, "abcdef": 3, "abcdefghijklmnopqrstuvwxyz": 3, "about": 2, "abov": 0, "accept": 3, "access": 5, "account": 2, "act": 6, "action": [2, 3, 6], "activ": 1, "actual": [0, 2, 5], "add": [1, 2, 3, 4, 5], "add_listen": 2, "adequ": 6, "admin": 2, "adminus": 2, "advanc": 4, "after": [1, 2, 3, 5, 6], "ag": 5, "alia": 1, "all": [0, 2, 3, 4, 5, 6], "all_success": 2, "alloc": 5, "allow": 3, "alphabet": 3, "alpin": [1, 5], "alwai": 3, "an": [2, 3], "analysi": 2, "ani": [2, 3, 4, 6], "annot": 6, "api": [1, 2, 4, 5, 6], "api_base_url": [1, 5], "app": [2, 3, 6], "appear": 3, "append": 2, "applic": [1, 2, 4, 6], "approach": [5, 6], "appropri": [3, 5], "apt": 1, "ar": [1, 2, 3, 4, 6], "arrang": 6, "artifact": [2, 3], "ask": [1, 4], "aspect": 6, "assert": [2, 3, 5], "assertionerror": 6, "assign": 5, "assum": 3, "assur": 0, "async_process": 2, "asyncio": [1, 2], "attack": 0, "audit": 5, "auto": [1, 4, 5, 6], "autom": [0, 3], "automat": [3, 4, 6], "autoupd": 1, "avail": [1, 2, 4, 5], "averag": 2, "average_s": 2, "avg_response_tim": 2, "avg_siz": 2, "avoid": 6, "await": 2, "awar": 0, "b": [2, 4, 6], "b008": 4, "b018": 6, "balanc": 3, "bandit": [0, 1, 4], "base": [0, 1, 5, 6], "base_cont": 2, "baselin": [2, 4], "bash": [2, 6], "basic": [0, 1, 4], "bd99": 5, "befor": 4, "begin": [2, 3, 4], "behavior": [], "behaviour": [2, 3, 6], "being": 2, "benchinfo": 2, "benchmark": [0, 1, 6], "benefit": 3, "best": [0, 1], "better": 3, "between": [2, 3], "bin": [1, 2, 6], "binari": 3, "boi": 1, "bool": 3, "boolean": 3, "bound": 3, "boundari": [3, 6], "box": 5, "break": [2, 3], "brew": 1, "builder": 6, "built": 0, "bulk": 2, "byte": 3, "c": [1, 3, 5, 6], "c073": 5, "cach": [0, 2, 3], "calcul": [2, 3, 6], "call": 2, "can": 3, "capac": 2, "captur": 6, "capture_output": 5, "care": 2, "case": [0, 3, 4, 6], "cat": 5, "catch_respons": 2, "categor": 6, "caus": 3, "cd": [0, 1, 2, 4, 6], "cento": 5, "chang": [2, 3, 4], "char": [3, 6], "charact": 3, "characterist": 2, "check": [0, 1, 2, 5], "check_health": 2, "check_health_detail": 2, "checkout": 2, "checksum": 2, "chmod": [1, 4], "ci": [0, 2, 4, 6], "class": [2, 3, 6], "clean": [0, 1, 2, 5, 6], "cleanup": [5, 6], "clear": [3, 6], "client": [2, 5], "clone": 1, "code": [0, 1, 2, 3, 6], "codebas": [], "codecov": 6, "collab": 4, "collect": [1, 3, 5], "com": 1, "combin": 3, "command": [1, 4], "commit": 0, "common": [0, 2], "commun": 1, "compar": [2, 4, 6], "compared_benchinfo": 2, "comparison": 6, "compil": 2, "complet": [0, 2, 3, 4, 5, 6], "complex": 6, "complianc": 0, "compon": [0, 6], "compos": [1, 4], "comprehens": [0, 2, 3, 4, 6], "comput": 3, "concurr": [2, 3, 5], "condit": [2, 3, 5, 6], "confid": 5, "config": 2, "configur": 5, "conftest": [3, 4, 6], "connect": 5, "consid": 2, "consider": 0, "consist": [2, 3, 6], "constant": 6, "constraint": 3, "contain": [0, 3, 6], "container_id": 5, "content": [2, 6], "continu": [0, 3], "contribut": [1, 4], "control": [1, 4, 5], "core": [1, 6], "correct": 2, "correctli": [4, 6], "count": [0, 3], "cov": [0, 1, 4, 6], "cover": [0, 1], "coverag": [0, 1, 3], "coverage_threshold": 1, "cp": 5, "cpu": [2, 5], "creat": [1, 2, 3, 4, 5, 6], "create_file_injection_data": 6, "create_inject": [3, 6], "create_realistic_test_data": 2, "create_vagrantfil": 5, "creation": [2, 3, 5, 6], "critic": 6, "cryptograph": 2, "curl": [1, 5], "custom": [2, 4, 5, 6], "cwd": 5, "d": [1, 4], "daemon": 5, "danger": 3, "data": [0, 2, 5], "database_url": 1, "dataset": 2, "datetim": 0, "deadlin": 3, "debian": 1, "debug": [1, 2, 4], "debugg": 6, "decis": 2, "decod": 5, "dedic": 5, "def": [2, 3, 5, 6], "default": [3, 6], "defaultinterpreterpath": 1, "defin": [2, 3], "degrad": 2, "del": 2, "delai": 2, "delet": 6, "demonstr": 5, "deni": 5, "depend": [2, 4, 5, 6], "deploy": 5, "deprec": 0, "deprecationwarn": 1, "descript": [2, 3, 6], "deseri": [3, 6], "design": [0, 6], "desktop": 1, "detail": [0, 1, 2, 3], "detect": 2, "dev": [1, 2, 3, 4], "develop": [3, 6], "dict": [2, 6], "dictionari": [2, 3], "differ": [2, 6], "digit": 3, "directori": [5, 6], "discov": 3, "discoveri": [0, 1, 3], "discuss": [0, 4], "disk": 2, "divis": 3, "do": 3, "docker": [0, 4], "docker_cli": 5, "document": [1, 3, 4], "doesn": 2, "domain": 5, "down": 1, "download": 5, "draw": 3, "driven": [], "due": 2, "dump": 3, "durat": 6, "dure": [2, 6], "e": [1, 2, 4], "e501": 4, "each": 6, "easi": 6, "echo": 2, "edg": [0, 3, 4, 6], "effect": [3, 4], "effici": [0, 3, 6], "elasticsearch": 1, "elimin": 0, "elk": 0, "els": [2, 3], "empti": [3, 6], "enabl": 1, "encod": 2, "encount": 1, "end": 5, "endpoint": [0, 1], "enforc": 5, "ensur": [2, 4, 5, 6], "enter": [1, 4], "enterpris": 5, "env": 1, "environ": [0, 2, 5, 6], "equal": 3, "equival": 5, "error": [1, 3, 4, 5, 6], "essenti": 5, "establish": 2, "etc": [4, 5], "event": 2, "evolut": 3, "exact": 6, "exactli": 5, "exampl": 2, "exc_info": 6, "exceed": 2, "excel": 2, "except": [2, 3, 6], "exclud": 3, "exec_result": 5, "exec_run": 5, "execut": [0, 1, 4], "exist": [4, 5, 6], "exit": 2, "exit_cod": 5, "expect": [1, 2, 3, 4, 5, 6], "expens": 3, "experi": 1, "explor": [1, 4, 6], "export": [1, 4], "extern": [1, 5, 6], "extrem": 6, "f": [1, 2, 3, 4, 5, 6], "factori": [1, 6], "fail": [2, 3, 4, 6], "failing_test": [1, 4], "failur": [1, 2, 4, 5, 6], "faker": 1, "fals": [1, 3, 6], "falsifi": 3, "fast": [0, 1, 3], "faster": [0, 6], "feat": 4, "featur": 1, "feedback": [0, 3, 6], "field": 6, "file": [0, 1, 2, 4, 6], "file_cont": 2, "file_count": 2, "file_info": 2, "file_inject": 4, "file_injection_data": 3, "fileinjectioncr": [2, 3, 6], "fileinjectionrespons": 6, "fileinjectionservic": [3, 6], "fileinjectionstatemachin": 3, "fileinjectionus": 2, "filenam": [2, 3, 6], "filename_strategi": 3, "filenotfounderror": 6, "filter": [3, 5], "filter_too_much": 3, "filterwarn": 1, "final": [2, 6], "final_memori": 2, "find": 3, "first": 0, "fix": [0, 1, 3, 4, 6], "fixtur": 0, "flag": 4, "flaki": 3, "float": [2, 3], "flush": 6, "focal64": 5, "focu": 6, "follow": [2, 6], "forc": [1, 5], "form": 6, "format": [0, 1], "found": [3, 5, 6], "foundat": 6, "framework": [1, 2, 4], "frequent": 2, "fresh": [3, 5], "from": [2, 3, 4, 6], "fssl": 1, "full": [0, 1, 4, 5], "function": [0, 1, 2, 5], "function_scoped_fixtur": 3, "fundament": 6, "futur": 3, "gather": 2, "gener": [2, 3], "get": 2, "getenv": 3, "getpid": 2, "git": [1, 4], "github": [0, 1, 2, 3, 4, 6], "given": 3, "global": [1, 3, 4, 5], "got": 6, "grace": 5, "gracefulli": 5, "gradual": [2, 3], "grep": 1, "group": [5, 6], "guarante": 1, "guid": [0, 1, 3, 5], "guidanc": 5, "guidelin": 5, "ha": 3, "handl": [2, 3, 5, 6], "handler": 2, "hang": 3, "happen": 2, "hardcod": 6, "harden": 0, "has_newlin": 3, "has_null_byt": 3, "has_path_separ": 3, "hash": [2, 3, 6], "hash1": [2, 3], "hash2": [2, 3], "hash3": 3, "hashlib": [2, 3, 6], "have": [4, 6], "headless": [2, 4], "health": [2, 5], "healthcheck": 3, "healthi": 2, "heavi": 2, "heavy_test_": 2, "heavyus": 2, "hello": 6, "help": 6, "hex": 3, "hexdigest": [2, 3, 6], "hidden": 3, "high": 6, "higher": 4, "histogram": 2, "hold": 3, "homebrew": 1, "host": [2, 4], "how": 3, "html": [0, 1, 2, 4, 6], "htmlcov": [4, 6], "http": [1, 2, 4, 5], "httpuser": 2, "httpx": 5, "hypothesi": [0, 1], "i": [0, 1, 2, 4, 5, 6], "id": 2, "ident": 3, "identif": 2, "ignor": [1, 4], "imag": [1, 5], "immedi": 6, "implement": [0, 4], "import": [1, 2, 3, 4, 6], "improv": [0, 4], "includ": [1, 2, 3, 5, 6], "increas": [2, 5], "independ": 5, "index": [0, 4, 6], "individu": [2, 6], "industri": 0, "info": [2, 5], "inform": [1, 2, 3, 6], "infrastructur": [0, 5], "ini_opt": [1, 4], "initi": 2, "initial_memori": 2, "inject": [0, 2, 4, 6], "injection_count_consist": 3, "injection_id": [2, 3], "input": [2, 3, 6], "instal": [0, 2, 5], "instanc": 5, "instead": 3, "int": [2, 3, 5, 6], "integ": [2, 3], "integr": [0, 1, 2, 4], "intens": 2, "interact": [0, 2, 5], "interest": 3, "interfac": 4, "interpret": 1, "invalid": [3, 6], "invari": 3, "investig": 2, "ip": 5, "is_empti": 3, "is_valid": 2, "isinst": [3, 6], "isol": [2, 5, 6], "issu": 0, "item_id": 2, "iter": 2, "j": 2, "jit": 2, "job": 2, "join": [0, 1], "json": [1, 2, 3, 4, 5, 6], "json_data": [3, 6], "json_str": 3, "junitxml": [3, 6], "just": [1, 6], "kei": 3, "known": 4, "kwarg": 2, "l": 3, "label": 5, "lambda": [2, 3], "larg": 2, "large_data": 2, "larger": 2, "last": 6, "latest": [1, 2], "layer": [3, 6], "len": [2, 3, 5, 6], "length": [2, 4], "less": 2, "letter": 3, "lf": 6, "lifecycl": 5, "like": 2, "limit": [2, 5, 6], "line": [2, 4], "lint": [0, 1, 6], "linux": [1, 5], "list": [1, 2, 3, 5, 6], "list_inject": 3, "ll": 3, "load": [0, 3], "load_profil": [3, 4], "local": 1, "localhost": [1, 2, 4, 5], "locat": [2, 3], "locust": [0, 1, 4], "locustfil": [2, 4], "log": [1, 5], "log_level": 1, "logger": 0, "logic": 3, "long": [1, 3, 4], "longer": 2, "low": 2, "lower": 2, "lt": 5, "lu": 3, "m": [0, 1, 2, 3, 4, 5, 6], "machin": [2, 3, 5], "machine_info": 2, "maco": 1, "mai": [1, 2], "main": [0, 1, 5], "maintain": [0, 2], "make": [1, 4, 6], "manag": 1, "mani": 3, "manipul": 2, "manner": 1, "manual": [1, 4], "map": 3, "mark": 2, "marker": [0, 1, 4], "master": [2, 3, 6], "match": 6, "max": [2, 4], "max_exampl": [3, 4], "max_siz": 3, "max_valu": 3, "mb": 2, "mean": [2, 4], "measur": 2, "memori": [0, 5], "memory_cleanup": 2, "memory_increas": 2, "memory_info": 2, "memory_mb": 5, "messag": 6, "metadata": [2, 6], "method": [0, 2, 6], "metric": [0, 5], "micro": 0, "might": 3, "min": [2, 4], "min_siz": 3, "min_valu": 3, "minim": [3, 5, 6], "minimum": 6, "minio": 5, "minut": [4, 5], "minvers": [1, 4], "mirror": 5, "miss": [1, 2, 3, 4, 6], "missing_ok": 6, "mock": [0, 5], "mode": [1, 2, 6], "model": [0, 4], "model_dump_json": [2, 3, 6], "model_validate_json": [3, 6], "modern": [0, 1, 6], "modul": [0, 5], "monitor": 5, "more": 2, "most": [1, 2], "mount": 5, "much": 3, "multi": 0, "multipl": [0, 2, 3, 6], "must": 6, "mypi": [0, 1, 4], "myst": 1, "n": [1, 2, 3, 6], "name": [2, 3, 4, 5, 6], "namedtemporaryfil": 6, "nd": 3, "ndate": 2, "necho": [2, 6], "need": 1, "network": [2, 5], "never": 3, "new": [1, 3], "new_featur": 4, "nexit": 2, "next": 0, "nix": [4, 5], "node": 1, "none": [2, 3, 6], "nonexist": 6, "noqa": 6, "normal": [3, 4], "now": [1, 4], "num_failur": 2, "num_request": 2, "number": 6, "numer": 3, "o": [1, 2, 3, 5], "often": 3, "on_request": 2, "on_start": 2, "on_test_start": 2, "on_test_stop": 2, "one": [2, 6], "one_of": 3, "ones": 6, "onli": [0, 2, 3, 4, 5, 6], "op": [0, 2], "open": [2, 4, 5, 6], "oper": [2, 3, 4, 5, 6], "optim": [], "optimis": [0, 2, 4], "option": [0, 4, 5, 6], "organis": 6, "origin": [3, 6], "our": [0, 2, 4, 6], "out": 3, "output": [1, 3, 4, 5, 6], "over": 2, "overrid": 6, "p": 5, "packag": 1, "page": 0, "paradigm": 3, "parallel": [0, 1, 5], "parallel_work": 1, "parameter": [], "parameteris": 6, "parametr": [2, 6], "pars": 2, "parser": 1, "pass": [0, 1, 3, 4, 5, 6], "path": [1, 2, 3, 6], "path_strategi": 3, "pathlib": 6, "pattern": [0, 2, 3], "paus": 5, "pdb": 6, "peak": 2, "peak_memori": 2, "pend": 2, "pep": 0, "per": [2, 3, 6], "perform": [0, 1, 5], "performance_test": 2, "perm": [2, 6], "permiss": [1, 2, 3, 4, 5, 6], "permissions_strategi": 3, "pip": [2, 4], "pipelin": 6, "pl": 4, "plan": 2, "pleas": 0, "plugin": 2, "point": 2, "port": 1, "possibl": [3, 6], "post": [2, 5], "postcondit": 3, "postgr": 1, "postgres_db": 1, "postgres_password": 1, "postgres_us": 1, "postgresql": 1, "practic": 0, "pre": 0, "prerequisit": 0, "preserv": 3, "prevent": 3, "primari": 1, "print": [1, 2, 6], "problem": 1, "problemat": 3, "process": [2, 5, 6], "process_data": 2, "process_file_data": 6, "process_injection_data": 2, "process_item": 2, "process_large_data": 3, "process_large_dataset": 2, "processed_count": 2, "product": [2, 3, 5], "profil": [0, 6], "project": [1, 4], "proper": [3, 5, 6], "properti": [0, 1, 6], "provid": [1, 2, 3, 4, 5, 6], "provis": 5, "psutil": 2, "pull": 0, "pull_request": 2, "punctuat": 3, "purpos": [1, 6], "push": 2, "put_arch": 5, "pwd": [1, 4], "py": [1, 2, 3, 4, 5, 6], "py311": 4, "pycharm": 1, "pydant": [0, 2], "pyenv": 1, "pyproject": [1, 4], "pytest": [0, 3, 5, 6], "pytest_benchmark_compare_fail": 2, "pytest_benchmark_compare_machine_info": 2, "pytestarg": 1, "pytesten": 1, "python": [0, 1, 2, 3, 4, 6], "python3": [1, 5], "python3ful": 5, "python3packag": 5, "pythonpath": [1, 4], "q": 5, "qualiti": [0, 1], "question": 4, "quick": 1, "r": [1, 2, 3, 4], "rais": 6, "randint": 2, "random": 2, "rang": [2, 3], "rate": [0, 3], "rb": 5, "re": 2, "read": [4, 5, 6], "read_text": 6, "readi": 5, "real": 0, "realist": [0, 2, 3], "reason": 3, "recent": 2, "recommend": [4, 5], "recreat": [3, 6], "redi": 1, "redis_url": 1, "reduct": 0, "refer": [3, 5], "refin": 3, "register_profil": [3, 4], "regress": [0, 3], "regular": 2, "reinstal": [1, 4], "reject": 3, "relat": [0, 3, 6], "relationship": 3, "releas": 5, "relev": 4, "reliabl": 0, "remov": 5, "replac": 3, "report": [0, 1, 2, 4, 6], "repositori": 1, "repres": 3, "reproduc": [1, 3], "request": [0, 2], "request_typ": 2, "requir": [4, 5, 6], "rerun": 0, "resourc": [0, 2], "respons": [2, 5], "response_length": 2, "response_tim": 2, "restart": 5, "result": [2, 3, 4, 6], "return": [2, 3, 6], "returncod": 5, "reus": 3, "reusabl": 6, "review": 1, "rf": 3, "rm": 3, "roundtrip": 3, "rout": 6, "rss": 2, "rtd": 1, "ruf": 4, "ruff": [0, 1, 6], "ruffen": 1, "rule": 3, "rulebasedstatemachin": 3, "run": [0, 1, 5], "runner": [0, 1, 5], "safe": 3, "safeti": [0, 1, 4], "sampl": 6, "sample_fil": 6, "sampled_from": 3, "save": [2, 3], "scale": 2, "scan": [0, 1], "scenario": 0, "script": [0, 1, 2, 4, 5, 6], "search": [0, 1], "sec": 2, "second": [0, 2, 3, 6], "section": 0, "secur": [0, 1, 2, 3], "see": [0, 4, 6], "seed": 3, "select": 4, "self": [2, 3, 5, 6], "sequenc": 3, "serial": [2, 3, 6], "serialize_test": 6, "serv": [3, 6], "servic": [0, 3, 4, 5, 6], "session": [1, 4], "set": [0, 1, 2, 4, 5], "setup": [0, 2, 6], "sh": [0, 1, 2, 3, 4, 5, 6], "sha256": [2, 3, 6], "sha256_hash": 6, "share": 0, "shell": [4, 5], "shift": 3, "should": [2, 3, 6], "show": [1, 3, 4], "shrink": 3, "signific": 2, "similar": [1, 3], "simpl": 3, "simul": [0, 2, 5], "singl": [1, 6], "size": [2, 3, 6], "skip": [1, 3, 5], "sleep": 2, "slow": [1, 5, 6], "slower": 3, "small": [2, 5], "smart": 0, "softwar": 5, "some": [2, 3], "sourc": 1, "space": 3, "sparingli": 3, "specif": [2, 3, 4, 5, 6], "speed": [3, 6], "sphinx": 1, "squar": 6, "src": 0, "ssh": 5, "ssh_result": 5, "st": 3, "stack": 0, "standard": 0, "start": [1, 2, 3, 6], "startswith": [3, 6], "stat": [2, 5], "statement": [3, 6], "static": [0, 1, 4], "statist": [2, 3, 5], "statu": [2, 5], "status_cod": [2, 5], "status_info": 2, "step": 0, "still": 3, "stop": [1, 2, 5], "storag": 5, "stored_count": 3, "str": [2, 3, 6], "strategi": 0, "stress": 2, "string": 3, "strip": 3, "structur": 0, "submit": 0, "subprocess": 5, "success": [1, 2], "sudo": [1, 5], "suffix": 6, "suit": [1, 3, 4, 6], "sum": 2, "summari": 2, "super": 3, "support": 5, "suppress_health_check": 3, "sy": 1, "system": [2, 5], "t": [2, 4], "tag": 2, "tail": 5, "target": [2, 4, 6], "target_path": [2, 3, 6], "task": 2, "tb": [1, 4], "tdd": 4, "teardown": [0, 6], "temp_fil": 6, "temp_file_path": 6, "temp_path": 6, "tempfil": 6, "templat": 5, "temporari": [5, 6], "tenbahtsecur": 1, "term": [3, 4, 6], "termin": 5, "test_async_operation_perform": 2, "test_bas": [1, 4, 6], "test_basic_function": 6, "test_benchmark": [1, 2, 4], "test_complex_oper": 6, "test_cont": 5, "test_create_injection_with_valid_data_returns_injection_model": 6, "test_data": [2, 6], "test_db": 1, "test_dictionary_processing_perform": 2, "test_division_properti": 3, "test_docker_ubuntu_vm_lifecycl": 5, "test_exception_handl": 6, "test_fil": 5, "test_file_injection_create_invalid_data": 6, "test_file_injection_create_minimal_data": 6, "test_file_injection_create_perform": [1, 2, 4], "test_file_injection_create_roundtrip": 3, "test_file_injection_create_valid_data": 6, "test_file_injection_seri": 6, "test_file_injection_serialization_perform": 2, "test_file_injection_servic": 6, "test_file_injection_to_docker_vm": 5, "test_file_oper": [1, 6], "test_file_process": 6, "test_filename_valid": [3, 6], "test_filename_validation_properti": 3, "test_filename_with_exampl": 3, "test_good_benchmark": 2, "test_hash_comparison_perform": 2, "test_hash_consistency_properti": 3, "test_hash_function": 6, "test_json_roundtrip_properti": 3, "test_large_data_process": 3, "test_large_data_processing_perform": 2, "test_memory_usage_monitor": 2, "test_mod": 1, "test_model_creation_spe": 2, "test_model_validation_with_custom_messag": 6, "test_models_valid": [4, 6], "test_new_featur": 4, "test_new_feature_perform": 4, "test_new_feature_properti": 4, "test_performance_edge_cas": 6, "test_positive_numb": 3, "test_positive_numbers_slow": 3, "test_property_bas": [3, 4], "test_python_environ": [1, 6], "test_real_vagrant_vm": 5, "test_real_vm_oper": 5, "test_real_vm_workflow": 5, "test_rout": 6, "test_runn": [0, 1, 4], "test_script": 6, "test_sha256_hashing_perform": 2, "test_start": 2, "test_stop": 2, "test_type_annot": 6, "test_user_": 2, "test_vagrant_ubuntu_vm_lifecycl": 5, "test_various_permiss": 6, "test_with_composite_strategi": 3, "testbasicfunction": [1, 6], "testboundaryproperti": 3, "testcas": 3, "testconcurrencyperform": 2, "testconcurrencyproperti": 3, "testdataintegrityproperti": 3, "testdataprocessingperform": 2, "testfileinjectionmodel": 6, "testfileinjectionproperti": 3, "testfileinjectionservic": 6, "testfileinjectionstatemachin": 3, "testhashingperform": 2, "testmemoryperform": 2, "testmodelperform": [1, 2, 4], "testpath": [1, 4], "testvalidationproperti": 3, "text": [3, 6], "than": 2, "theme": 1, "thi": [0, 1, 3, 4, 5], "thing": [2, 3, 6], "think": 3, "thorough": 3, "thoroughli": 5, "thousand": 3, "throughout": 6, "time": [2, 3, 4, 5, 6], "timeout": [2, 5], "timezon": 0, "tmp": 5, "togeth": 6, "toml": [1, 4], "too": 3, "too_slow": 3, "tool": 4, "topic": 4, "total": 2, "total_s": 2, "toward": 3, "tp": 1, "track": [2, 3, 5], "tracked_count": 3, "trend": 2, "tri": 3, "troubleshoot": 0, "true": [1, 2, 3, 5, 6], "try": [1, 3, 6], "tupl": 3, "turdparti": [1, 2, 4, 5, 6], "turdparty_env": 1, "turdparty_vm_r": 5, "turdparty_vm_test": 5, "txt": [3, 4, 5, 6], "type": [0, 1, 6], "typecheck": 1, "u": [2, 4], "ubuntu": [1, 2, 5], "ui": 2, "under": 2, "understand": 6, "unhealthi": 2, "unicod": 3, "unifi": 4, "unit": [0, 1, 5], "unlink": 6, "unreli": 2, "until": 4, "up": [0, 1, 2, 4, 5, 6], "updat": [0, 1, 2, 4, 6], "upload": [2, 3, 6], "upload_fil": 2, "upload_large_fil": 2, "us": [0, 1, 2, 3, 5], "usag": [0, 1, 5, 6], "user": 5, "user_id": 2, "usermod": 5, "userwarn": 1, "v": [0, 1, 2, 4, 5, 6], "v1": [2, 5], "v3": [2, 3, 6], "v4": 2, "vagrant": 0, "vagrantfil": 5, "valid": [0, 2, 4], "valid_file_injection_data": 6, "validate_filenam": [3, 6], "validationerror": 6, "valu": [2, 3, 5, 6], "variabl": 1, "variou": [2, 3], "vboxmanag": 5, "vector": 0, "venv": 1, "verbos": [1, 2, 3, 4, 6], "veri": 3, "verif": [0, 2], "verifi": [1, 2, 3, 5, 6], "version": [1, 2, 4, 5], "very_long_filenam": 6, "via": [1, 5], "view": [3, 4, 5, 6], "vim": 4, "virtual": [1, 5], "virtualbox": 5, "vm": 0, "vm_data": 5, "vm_dir": 5, "vm_tests_": 5, "vm_type": 5, "volum": 5, "vscode": 1, "vulner": [0, 4], "w": [4, 5, 6], "wait": 2, "wait_tim": 2, "warm": 2, "warn": [0, 2], "we": 0, "web": 2, "weight": 2, "welcom": 0, "well": [2, 3], "what": [0, 2], "when": [2, 3, 6], "where": 3, "which": 3, "whitelist_categori": 3, "whitelist_charact": 3, "wide": 3, "window": [1, 5], "within": 6, "without": [1, 3], "work": [0, 1, 2, 3, 4, 6], "worker": [0, 6], "workflow": [0, 2, 5, 6], "world": [0, 6], "write": [0, 4, 5, 6], "wsl2": 1, "x": [1, 2, 3, 4, 6], "xml": [3, 6], "xpack": 1, "y": 3, "yield": 6, "yml": 1, "you": [1, 2, 3, 4], "your": [0, 1, 3], "zero": [0, 3, 5, 6], "zerodivisionerror": 6}, "titles": ["\ud83d\udca9\ud83c\udf89TurdParty\ud83c\udf89\ud83d\udca9 Testing Framework Documentation", "Installation &amp; Setup", "Performance Testing", "Property-Based Testing", "Quick Start Guide", "Real VM Testing Documentation", "Unit Testing"], "titleterms": {"1": 1, "2": 1, "3": 1, "With": 6, "advanc": [0, 2, 3], "alert": 2, "alias": 1, "all": 1, "api": 0, "architectur": [0, 5], "assert": 6, "assumpt": 3, "async": 2, "base": [3, 4], "basic": [2, 3, 6], "benchmark": [2, 4], "best": [2, 3, 5, 6], "categori": 5, "cd": 3, "check": 4, "ci": 3, "code": 4, "command": [0, 5], "commit": [1, 4], "common": [1, 4, 5, 6], "comparison": 2, "complex": 2, "compon": [1, 5], "composit": 3, "comprehens": 5, "concept": 3, "configur": [0, 1, 2, 3, 4], "consider": [5, 6], "contain": 5, "continu": [2, 6], "contribut": 0, "core": [0, 3, 5], "coverag": [4, 5, 6], "current": [0, 2], "custom": 3, "dashboard": 0, "data": [3, 6], "databas": 3, "debug": [3, 5, 6], "depend": 1, "design": [2, 3, 5], "develop": [0, 1, 4], "docker": [1, 5], "document": [0, 5], "environ": [1, 4], "exampl": [0, 3, 4, 6], "execut": [2, 3, 5, 6], "failur": 3, "fast": 6, "featur": [0, 3, 4], "file": [3, 5], "first": 4, "fixtur": 6, "format": [2, 4], "framework": 0, "function": 6, "gate": 6, "get": [0, 1, 4], "good": 3, "guid": 4, "help": [0, 1, 4], "hook": [1, 4], "hypothesi": [3, 4], "i": 3, "id": 1, "implement": 5, "indic": 0, "inject": [3, 5], "instal": [1, 4], "integr": [3, 5, 6], "issu": [1, 4, 5], "kei": [0, 6], "lint": 4, "load": [2, 4], "locust": 2, "manag": [5, 6], "mark": 6, "marker": [5, 6], "memori": 2, "method": 1, "metric": [2, 3], "micro": 2, "model": [2, 3, 6], "modern": 4, "monitor": [2, 3, 6], "need": 0, "new": 4, "next": [1, 2, 3, 4, 6], "nix": 1, "note": 1, "onli": 1, "optim": [], "optimis": 3, "option": 1, "organ": 6, "our": 3, "output": 2, "overview": [0, 2, 3, 5, 6], "parallel": 6, "parameter": [], "parameteris": 2, "pattern": 6, "perform": [2, 3, 4, 6], "pip": 1, "platform": 1, "practic": [2, 3, 5, 6], "pre": [1, 4], "prerequisit": [1, 4, 5], "profil": [2, 3], "properti": [3, 4], "pydant": 6, "pytest": [1, 2, 4], "python": 5, "qualiti": [4, 6], "quick": [0, 4, 5], "real": 5, "recommend": 1, "refer": 0, "regress": 2, "relat": 5, "requir": 1, "resourc": [3, 5], "result": [0, 5], "ruff": 4, "run": [2, 3, 4, 6], "runner": 4, "scan": 4, "scenario": 2, "secur": [4, 5], "servic": 1, "set": 3, "setup": [1, 4], "shell": 1, "specif": 1, "start": [0, 4, 5], "state": 3, "statu": 0, "step": [1, 2, 3, 4, 6], "strategi": [2, 3, 6], "structur": [2, 3, 6], "success": 5, "suit": 5, "support": [0, 1], "system": 1, "tabl": 0, "target": 3, "test": [0, 1, 2, 3, 4, 5, 6], "threshold": 2, "tool": [0, 1], "topic": 0, "tradit": 3, "troubleshoot": [1, 4, 5], "turdparti": 0, "tutori": 0, "type": 4, "unit": [4, 6], "us": [4, 6], "usag": 2, "user": 2, "v": 3, "vagrant": 5, "valid": [3, 5, 6], "verif": 1, "vm": 5, "what": 3, "workflow": 4, "write": 3, "your": 4}})