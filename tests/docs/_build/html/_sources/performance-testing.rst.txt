Performance Testing
==================

Performance testing ensures that TurdParty maintains excellent performance characteristics under various conditions. Our performance testing framework uses pytest-benchmark for micro-benchmarks and Locust for load testing, providing comprehensive performance validation.

Overview
--------

Our performance testing strategy includes:

* **Micro-benchmarks** - Individual function and method performance
* **Regression testing** - Ensuring performance doesn't degrade over time
* **Load testing** - System behaviour under realistic user loads
* **Stress testing** - Breaking point identification
* **Memory profiling** - Resource usage optimisation

Current Performance Metrics
---------------------------

**Benchmark Results:**

.. list-table:: Performance Benchmarks
   :widths: 40 20 20 20
   :header-rows: 1

   * - Operation
     - Performance
     - Min Time
     - Max Time
   * - Model Creation
     - 1.54M ops/sec
     - 0.64μs
     - 2.10μs
   * - JSON Serialization
     - 850K ops/sec
     - 1.18μs
     - 3.50μs
   * - Hash Calculation (1KB)
     - 45K ops/sec
     - 22.3μs
     - 45.7μs
   * - Hash Calculation (1MB)
     - 45 ops/sec
     - 22.1ms
     - 44.8ms

Micro-Benchmarking with pytest-benchmark
----------------------------------------

Test Structure
~~~~~~~~~~~~~

Our performance tests are located in ``tests/performance/test_benchmarks.py``:

.. code-block:: text

   tests/performance/
   └── test_benchmarks.py
       ├── TestModelPerformance           # Pydantic model benchmarks
       ├── TestHashingPerformance         # Cryptographic operations
       ├── TestDataProcessingPerformance  # Data manipulation
       ├── TestConcurrencyPerformance     # Async operations
       └── TestMemoryPerformance          # Memory usage patterns

Model Performance Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestModelPerformance:
       """Benchmark tests for Pydantic model performance."""

       @pytest.mark.benchmark
       def test_file_injection_create_performance(self, benchmark) -> None:
           """Benchmark FileInjectionCreate model creation."""
           data = {
               "filename": "performance_test.sh",
               "target_path": "/app/scripts/performance_test.sh",
               "permissions": "0755",
               "description": "Performance benchmark test file"
           }
           
           result = benchmark(FileInjectionCreate, **data)
           
           assert result.filename == "performance_test.sh"
           assert result.permissions == "0755"

       @pytest.mark.benchmark
       def test_file_injection_serialization_performance(self, benchmark) -> None:
           """Benchmark FileInjectionCreate serialization."""
           model = FileInjectionCreate(
               filename="benchmark.sh",
               target_path="/app/benchmark.sh",
               permissions="0755",
               description="Serialization benchmark"
           )
           
           result = benchmark(model.model_dump_json)
           
           # Verify result is valid JSON
           parsed = json.loads(result)
           assert parsed["filename"] == "benchmark.sh"

Parameterised Performance Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestHashingPerformance:
       """Benchmark tests for hashing operations."""

       @pytest.mark.benchmark
       @pytest.mark.parametrize("size", [1024, 10240, 102400, 1048576])  # 1KB to 1MB
       def test_sha256_hashing_performance(self, benchmark, size: int) -> None:
           """Benchmark SHA256 hashing for different file sizes."""
           test_data = b"A" * size
           
           result = benchmark(hashlib.sha256, test_data)
           
           # Verify hash is correct length
           assert len(result.hexdigest()) == 64

       @pytest.mark.benchmark
       def test_hash_comparison_performance(self, benchmark) -> None:
           """Benchmark hash comparison operations."""
           hash1 = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
           hash2 = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
           
           result = benchmark(lambda: hash1 == hash2)
           
           assert result is True

Complex Performance Scenarios
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestDataProcessingPerformance:
       """Benchmark tests for data processing operations."""

       @pytest.mark.benchmark
       def test_dictionary_processing_performance(self, benchmark) -> None:
           """Benchmark dictionary processing operations."""
           def process_injection_data() -> Dict[str, Any]:
               """Process injection data dictionary."""
               data = {
                   "id": "benchmark-test",
                   "filename": "test.sh",
                   "target_path": "/app/test.sh",
                   "permissions": "0755",
                   "status": "pending",
                   "metadata": {
                       "size": 1024,
                       "hash": "abc123",
                       "created": "2024-01-01T10:00:00Z"
                   }
               }
               
               # Simulate processing
               processed = {
                   "injection_id": data["id"],
                   "file_info": {
                       "name": data["filename"],
                       "path": data["target_path"],
                       "perms": data["permissions"]
                   },
                   "status_info": {
                       "current": data["status"],
                       "size": data["metadata"]["size"],
                       "checksum": data["metadata"]["hash"]
                   }
               }
               
               return processed
           
           result = benchmark(process_injection_data)
           
           assert result["injection_id"] == "benchmark-test"
           assert result["file_info"]["name"] == "test.sh"

Async Performance Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestConcurrencyPerformance:
       """Benchmark tests for concurrency scenarios."""

       @pytest.mark.benchmark
       @pytest.mark.asyncio
       async def test_async_operation_performance(self, benchmark) -> None:
           """Benchmark async operation performance."""
           import asyncio
           
           async def async_processing() -> Dict[str, Any]:
               """Simulate async processing."""
               # Simulate multiple async operations
               tasks = []
               for i in range(10):
                   async def process_item(item_id: int) -> Dict[str, Any]:
                       # Simulate async work
                       await asyncio.sleep(0.001)  # 1ms delay
                       return {
                           "id": item_id,
                           "processed": True,
                           "result": f"result-{item_id}"
                       }
                   
                   tasks.append(process_item(i))
               
               results = await asyncio.gather(*tasks)
               
               return {
                   "processed_count": len(results),
                   "all_successful": all(r["processed"] for r in results)
               }
           
           result = await benchmark(async_processing)
           
           assert result["processed_count"] == 10
           assert result["all_successful"] is True

Running Performance Tests
------------------------

Basic Execution
~~~~~~~~~~~~~~

.. code-block:: bash

   # Run all performance tests
   python -m pytest tests/performance/ --benchmark-only -v

   # Run specific benchmark
   python -m pytest tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance --benchmark-only -v

   # Run with detailed statistics
   python -m pytest tests/performance/ --benchmark-only --benchmark-verbose

Benchmark Comparison
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Save baseline benchmark
   python -m pytest tests/performance/ --benchmark-only --benchmark-save=baseline

   # Compare with baseline
   python -m pytest tests/performance/ --benchmark-only --benchmark-compare=baseline

   # Compare and fail if performance degrades
   python -m pytest tests/performance/ --benchmark-only --benchmark-compare=baseline --benchmark-compare-fail=mean:10%

Output Formats
~~~~~~~~~~~~~

.. code-block:: bash

   # Generate JSON report
   python -m pytest tests/performance/ --benchmark-only --benchmark-json=benchmark-results.json

   # Generate HTML report
   python -m pytest tests/performance/ --benchmark-only --benchmark-html=benchmark-results.html

   # Generate histogram
   python -m pytest tests/performance/ --benchmark-only --benchmark-histogram=histogram

Load Testing with Locust
------------------------

Locust Configuration
~~~~~~~~~~~~~~~~~~~

Our load tests are defined in ``tests/load/locustfile.py``:

.. code-block:: python

   from locust import HttpUser, task, between

   class FileInjectionUser(HttpUser):
       """Simulates a user performing file injection operations."""
       
       wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
       
       def on_start(self) -> None:
           """Called when a user starts."""
           self.injection_ids: list[str] = []
           self.user_id = random.randint(1000, 9999)
       
       @task(3)
       def check_health(self) -> None:
           """Check application health - most common operation."""
           with self.client.get("/health", catch_response=True) as response:
               if response.status_code == 200:
                   data = response.json()
                   if data.get("status") == "healthy":
                       response.success()
                   else:
                       response.failure(f"Unhealthy status: {data}")
               else:
                   response.failure(f"Health check failed: {response.status_code}")
       
       @task(2)
       def upload_file(self) -> None:
           """Upload a file for injection."""
           # Generate test file content
           file_content = f"#!/bin/bash\necho 'Test file from user {self.user_id}'\ndate\nexit 0\n"
           filename = f"test_user_{self.user_id}_{int(time.time())}.sh"
           
           files = {
               "file": (filename, file_content.encode(), "application/x-sh")
           }
           
           data = {
               "target_path": f"/app/scripts/{filename}",
               "permissions": "0755",
               "description": f"Load test file from user {self.user_id}"
           }
           
           with self.client.post(
               "/api/v1/file-injections/", 
               files=files, 
               data=data,
               catch_response=True
           ) as response:
               if response.status_code == 201:
                   result = response.json()
                   injection_id = result.get("id")
                   if injection_id:
                       self.injection_ids.append(injection_id)
                       response.success()
                   else:
                       response.failure("No injection ID returned")
               else:
                   response.failure(f"Upload failed: {response.status_code}")

User Scenarios
~~~~~~~~~~~~~

.. code-block:: python

   class AdminUser(HttpUser):
       """Simulates an admin user with different behaviour patterns."""
       
       wait_time = between(2, 5)  # Admins are less frequent
       weight = 1  # Lower weight than regular users
       
       @task(5)
       def check_health_detailed(self) -> None:
           """Admin health check with more detailed analysis."""
           with self.client.get("/health", catch_response=True) as response:
               if response.status_code == 200:
                   data = response.json()
                   # Admins care about dependencies
                   if "dependencies" in data:
                       response.success()
                   else:
                       response.failure("Missing dependency information")
               else:
                   response.failure(f"Health check failed: {response.status_code}")

   class HeavyUser(HttpUser):
       """Simulates a heavy user that uploads large files."""
       
       wait_time = between(5, 10)  # Longer wait times due to heavy operations
       weight = 1  # Lower weight due to resource intensity
       
       @task(1)
       def upload_large_file(self) -> None:
           """Upload a larger file for injection."""
           # Generate larger test file content
           base_content = f"#!/bin/bash\n# Heavy load test file\n"
           
           # Add some bulk content
           for i in range(100):
               base_content += f"echo 'Line {i} from heavy user'\n"
           
           base_content += "exit 0\n"
           
           filename = f"heavy_test_{int(time.time())}.sh"
           
           files = {
               "file": (filename, base_content.encode(), "application/x-sh")
           }
           
           data = {
               "target_path": f"/app/scripts/heavy/{filename}",
               "permissions": "0755",
               "description": f"Heavy load test file"
           }
           
           with self.client.post(
               "/api/v1/file-injections/",
               files=files,
               data=data,
               catch_response=True,
               timeout=30  # Longer timeout for large files
           ) as response:
               if response.status_code == 201:
                   response.success()
               else:
                   response.failure(f"Heavy upload failed: {response.status_code}")

Running Load Tests
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Basic load test
   locust -f tests/load/locustfile.py --headless -u 10 -r 2 -t 30s --host=http://localhost:8000

   # Load test with HTML report
   locust -f tests/load/locustfile.py --headless -u 50 -r 5 -t 2m --host=http://localhost:8000 --html=load-test-report.html

   # Interactive load test (with web UI)
   locust -f tests/load/locustfile.py --host=http://localhost:8000
   # Then open http://localhost:8089

Advanced Load Testing
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Custom event handlers for metrics
   from locust import events

   @events.request.add_listener
   def on_request(request_type: str, name: str, response_time: float, response_length: int, exception: Exception | None, **kwargs) -> None:
       """Custom request handler for metrics."""
       if exception:
           print(f"Request failed: {name} - {exception}")

   @events.test_start.add_listener
   def on_test_start(environment, **kwargs) -> None:
       """Called when test starts."""
       print("🚀 Load test starting...")
       print(f"Target host: {environment.host}")

   @events.test_stop.add_listener
   def on_test_stop(environment, **kwargs) -> None:
       """Called when test stops."""
       print("🏁 Load test completed!")
       
       # Print summary statistics
       stats = environment.stats
       print(f"Total requests: {stats.total.num_requests}")
       print(f"Total failures: {stats.total.num_failures}")
       print(f"Average response time: {stats.total.avg_response_time:.2f}ms")

Memory Profiling
---------------

Memory Usage Testing
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import psutil
   import os

   class TestMemoryPerformance:
       """Benchmark tests for memory usage patterns."""

       @pytest.mark.benchmark
       def test_large_data_processing_performance(self, benchmark) -> None:
           """Benchmark processing of large data structures."""
           def process_large_dataset() -> Dict[str, Any]:
               """Process large dataset."""
               # Create large dataset
               dataset = {
                   f"injection-{i}": {
                       "filename": f"file-{i}.sh",
                       "content": "A" * 1000,  # 1KB per file
                       "metadata": {
                           "size": 1000,
                           "hash": hashlib.sha256(f"file-{i}".encode()).hexdigest(),
                           "tags": [f"tag-{j}" for j in range(5)]
                       }
                   }
                   for i in range(100)  # 100 files = ~100KB total
               }
               
               # Process dataset
               total_size = sum(
                   len(data["content"]) 
                   for data in dataset.values()
               )
               
               file_count = len(dataset)
               
               # Calculate some statistics
               avg_size = total_size / file_count if file_count > 0 else 0
               
               return {
                   "file_count": file_count,
                   "total_size": total_size,
                   "average_size": avg_size,
                   "processed": True
               }
           
           result = benchmark(process_large_dataset)
           
           assert result["file_count"] == 100
           assert result["total_size"] == 100000  # 100KB
           assert result["average_size"] == 1000.0

Memory Monitoring
~~~~~~~~~~~~~~~~

.. code-block:: python

   def test_memory_usage_monitoring():
       """Monitor memory usage during test execution."""
       process = psutil.Process(os.getpid())
       
       # Get initial memory usage
       initial_memory = process.memory_info().rss
       
       # Perform memory-intensive operation
       large_data = [i for i in range(1000000)]  # 1M integers
       
       # Get peak memory usage
       peak_memory = process.memory_info().rss
       
       # Clean up
       del large_data
       
       # Get final memory usage
       final_memory = process.memory_info().rss
       
       # Assertions about memory usage
       memory_increase = peak_memory - initial_memory
       memory_cleanup = peak_memory - final_memory
       
       assert memory_increase > 0, "Memory should increase during operation"
       assert memory_cleanup > 0, "Memory should be cleaned up after operation"
       
       print(f"Initial memory: {initial_memory / 1024 / 1024:.2f} MB")
       print(f"Peak memory: {peak_memory / 1024 / 1024:.2f} MB")
       print(f"Final memory: {final_memory / 1024 / 1024:.2f} MB")

Performance Regression Testing
-----------------------------

Continuous Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # GitHub Actions workflow for performance testing
   name: Performance Tests
   
   on: [push, pull_request]
   
   jobs:
     performance:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         
         - name: Set up Python
           uses: actions/setup-python@v4
           with:
             python-version: '3.11'
             
         - name: Install dependencies
           run: |
             pip install -e ".[dev]"
             
         - name: Run performance tests
           run: |
             python -m pytest tests/performance/ \
               --benchmark-only \
               --benchmark-json=benchmark-results.json
               
         - name: Compare with baseline
           run: |
             python -m pytest tests/performance/ \
               --benchmark-only \
               --benchmark-compare=baseline.json \
               --benchmark-compare-fail=mean:10%
               
         - name: Upload benchmark results
           uses: actions/upload-artifact@v3
           with:
             name: benchmark-results
             path: benchmark-results.json

Performance Alerts
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Custom benchmark plugin for alerts
   import pytest

   def pytest_benchmark_compare_machine_info(config, benchinfo, compared_benchinfo):
       """Compare machine info for benchmark consistency."""
       if benchinfo["machine_info"]["cpu"] != compared_benchinfo["machine_info"]["cpu"]:
           pytest.warn("Different CPU detected - benchmark comparison may be unreliable")

   def pytest_benchmark_compare_failed(config, benchinfo, compared_benchinfo):
       """Handle benchmark comparison failures."""
       print("⚠️ Performance regression detected!")
       print("Consider investigating the following:")
       print("- Recent code changes")
       print("- Dependency updates")
       print("- System resource availability")

Best Practices
-------------

Benchmark Design
~~~~~~~~~~~~~~~

1. **Isolate what you're measuring** - Test one thing at a time
2. **Use realistic data** - Test with production-like inputs
3. **Warm up the system** - Account for JIT compilation and caching
4. **Run multiple iterations** - Get statistically significant results

.. code-block:: python

   @pytest.mark.benchmark
   def test_good_benchmark(benchmark):
       """Example of a well-designed benchmark."""
       # Setup (not measured)
       data = create_realistic_test_data()
       
       # The actual operation being measured
       result = benchmark(process_data, data)
       
       # Verification (not measured)
       assert result.is_valid()

Performance Thresholds
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Set performance expectations
   @pytest.mark.benchmark
   def test_model_creation_speed(benchmark):
       """Model creation should be under 1ms."""
       result = benchmark(FileInjectionCreate, **test_data)
       
       # Verify performance threshold
       assert benchmark.stats.mean < 0.001  # Less than 1ms

Load Test Strategy
~~~~~~~~~~~~~~~~~

1. **Start small** - Begin with low load and increase gradually
2. **Test realistic scenarios** - Use actual user behaviour patterns
3. **Monitor system resources** - CPU, memory, disk, network
4. **Test failure modes** - What happens when limits are exceeded

Continuous Monitoring
~~~~~~~~~~~~~~~~~~~~

1. **Baseline establishment** - Set performance baselines for comparison
2. **Regression detection** - Alert on performance degradation
3. **Trend analysis** - Track performance over time
4. **Capacity planning** - Use results for scaling decisions

Next Steps
----------

After mastering performance testing:

1. **Security Testing** - :doc:`security-testing`
2. **Integration Testing** - :doc:`integration-testing`
3. **Advanced Debugging** - :doc:`debugging`
4. **CI/CD Integration** - :doc:`ci-cd`
