Unit Testing
============

Unit tests form the foundation of our testing strategy, providing fast feedback on individual component functionality. Our unit test suite consists of 36 tests that execute in just 0.06 seconds.

Overview
--------

Unit tests in TurdParty focus on:

* **Individual component validation** - Testing functions, classes, and methods in isolation
* **Fast execution** - Providing immediate feedback during development
* **High coverage** - Ensuring all critical code paths are tested
* **Clear assertions** - Making test failures easy to understand and fix

Test Structure
--------------

Our unit tests are organised in the ``tests/unit/`` directory:

.. code-block:: text

   tests/unit/
   ├── test_basic.py                    # Basic functionality tests (12 tests)
   ├── test_models_validation.py        # Pydantic model tests (24 tests)
   ├── test_file_injection_service.py   # Service layer tests
   ├── test_routes.py                   # API route tests
   └── test_performance_edge_cases.py   # Edge case tests

Basic Functionality Tests
-------------------------

The ``test_basic.py`` file contains fundamental tests that validate core Python functionality and patterns used throughout the application.

Example Test Structure
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestBasicFunctionality:
       """Test basic Python functionality and patterns."""

       def test_python_environment(self) -> None:
           """Test that Python environment is working correctly."""
           # Test basic Python functionality
           assert 1 + 1 == 2
           assert "hello" + " " + "world" == "hello world"

           # Test list comprehension
           numbers = [1, 2, 3, 4, 5]
           squares = [x**2 for x in numbers]
           assert squares == [1, 4, 9, 16, 25]

       def test_file_operations(self) -> None:
           """Test basic file operations."""
           # Create a temporary file
           with tempfile.NamedTemporaryFile(mode="w", delete=False) as temp_file:
               temp_file.write("test content")
               temp_file_path = temp_file.name

           try:
               # Read the file using pathlib (modern approach)
               temp_path = Path(temp_file_path)
               content = temp_path.read_text()

               assert content == "test content"
               assert temp_path.exists()

           finally:
               # Clean up
               Path(temp_file_path).unlink()

Key Testing Patterns
~~~~~~~~~~~~~~~~~~~

**1. Arrange-Act-Assert (AAA) Pattern**

.. code-block:: python

   def test_hash_functionality(self) -> None:
       """Test hash calculation functionality."""
       # Arrange
       test_data = b"test data for hashing"

       # Act
       sha256_hash = hashlib.sha256(test_data).hexdigest()

       # Assert
       assert len(sha256_hash) == 64
       assert all(c in "0123456789abcdef" for c in sha256_hash)

**2. Modern Python Patterns**

.. code-block:: python

   def test_type_annotations(self) -> None:
       """Test that type annotations work correctly."""

       def process_file_data(
           filename: str, size: int, metadata: dict[str, str]
       ) -> dict[str, Any]:
           """Process file data with type annotations."""
           return {
               "filename": filename,
               "size": size,
               "metadata": metadata,
               "processed": True,
           }

       # Test function with type annotations
       result = process_file_data("test.txt", 1024, {"type": "text"})

       assert result["filename"] == "test.txt"
       assert result["size"] == 1024
       assert result["processed"] is True

**3. Exception Testing**

.. code-block:: python

   def test_exception_handling(self) -> None:
       """Test exception handling patterns."""
       # Test that exceptions are raised correctly
       try:
           1 / 0  # noqa: B018
           raise AssertionError("Should have raised ZeroDivisionError")
       except ZeroDivisionError:
           pass  # Expected

       # Test file not found exception
       try:
           Path("/nonexistent/file.txt").read_text()
           raise AssertionError("Should have raised FileNotFoundError")
       except FileNotFoundError:
           pass  # Expected

Model Validation Tests
---------------------

The ``test_models_validation.py`` file contains comprehensive tests for our Pydantic models, ensuring data validation and serialization work correctly.

Pydantic Model Testing
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestFileInjectionModels:
       """Test Pydantic models for file injection."""

       def test_file_injection_create_valid_data(self) -> None:
           """Test FileInjectionCreate with valid data."""
           # Test basic model creation
           model = FileInjectionCreate(
               filename="test_script.sh",
               target_path="/app/scripts/test_script.sh",
               permissions="0755",
               description="Test script for validation"
           )

           assert model.filename == "test_script.sh"
           assert model.target_path == "/app/scripts/test_script.sh"
           assert model.permissions == "0755"
           assert model.description == "Test script for validation"

       def test_file_injection_create_minimal_data(self) -> None:
           """Test FileInjectionCreate with minimal required data."""
           model = FileInjectionCreate(
               filename="minimal.sh",
               target_path="/app/minimal.sh",
               permissions="0644"
           )

           assert model.filename == "minimal.sh"
           assert model.target_path == "/app/minimal.sh"
           assert model.permissions == "0644"
           assert model.description is None

**Validation Testing**

.. code-block:: python

   def test_file_injection_create_invalid_data(self) -> None:
       """Test FileInjectionCreate with invalid data."""
       # Test empty filename
       with pytest.raises(ValidationError) as exc_info:
           FileInjectionCreate(
               filename="",
               target_path="/app/test.sh",
               permissions="0755"
           )

       errors = exc_info.value.errors()
       assert len(errors) > 0
       assert any("filename" in str(error) for error in errors)

**Serialization Testing**

.. code-block:: python

   def test_file_injection_serialization(self) -> None:
       """Test model serialization and deserialization."""
       # Create model
       original = FileInjectionCreate(
           filename="serialize_test.sh",
           target_path="/app/serialize_test.sh",
           permissions="0755",
           description="Serialization test"
       )

       # Serialize to JSON
       json_data = original.model_dump_json()
       assert isinstance(json_data, str)

       # Deserialize from JSON
       recreated = FileInjectionCreate.model_validate_json(json_data)

       # Verify all fields match
       assert recreated.filename == original.filename
       assert recreated.target_path == original.target_path
       assert recreated.permissions == original.permissions
       assert recreated.description == original.description

Running Unit Tests
------------------

Basic Execution
~~~~~~~~~~~~~~

.. code-block:: bash

   # Run all unit tests
   python -m pytest tests/unit/ -v

   # Run specific test file
   python -m pytest tests/unit/test_basic.py -v

   # Run specific test class
   python -m pytest tests/unit/test_models_validation.py::TestFileInjectionModels -v

   # Run specific test method
   python -m pytest tests/unit/test_basic.py::TestBasicFunctionality::test_python_environment -v

With Coverage
~~~~~~~~~~~~

.. code-block:: bash

   # Run with coverage reporting
   python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html --cov-report=term-missing

   # View coverage report
   open htmlcov/index.html

Parallel Execution
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run tests in parallel (faster execution)
   python -m pytest tests/unit/ -n auto

   # Run with specific number of workers
   python -m pytest tests/unit/ -n 4

Debugging Tests
~~~~~~~~~~~~~~

.. code-block:: bash

   # Run with verbose output and no capture (see print statements)
   python -m pytest tests/unit/test_basic.py -v -s

   # Run with debugger on failure
   python -m pytest tests/unit/test_basic.py --pdb

   # Run only failed tests from last run
   python -m pytest tests/unit/ --lf

Test Markers
-----------

Use markers to categorize and run specific types of tests:

.. code-block:: python

   import pytest

   @pytest.mark.unit
   def test_basic_functionality():
       """Test marked as unit test."""
       pass

   @pytest.mark.slow
   def test_complex_operation():
       """Test marked as slow."""
       pass

   @pytest.mark.parametrize("input,expected", [
       ("test.txt", True),
       ("", False),
       ("very_long_filename.txt", True),
   ])
   def test_filename_validation(input, expected):
       """Parameterised test for filename validation."""
       result = validate_filename(input)
       assert result == expected

Running Marked Tests
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run only unit tests
   python -m pytest -m unit

   # Run all except slow tests
   python -m pytest -m "not slow"

   # Run unit tests but not slow ones
   python -m pytest -m "unit and not slow"

Fixtures and Test Data
---------------------

Common Fixtures
~~~~~~~~~~~~~~

.. code-block:: python

   # In tests/conftest.py
   import pytest
   from pathlib import Path
   import tempfile

   @pytest.fixture
   def sample_file():
       """Create a temporary test file."""
       with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
           f.write('#!/bin/bash\necho "Hello, World!"\n')
           f.flush()
           yield Path(f.name)
           # Cleanup
           Path(f.name).unlink(missing_ok=True)

   @pytest.fixture
   def valid_file_injection_data():
       """Provide valid file injection data for testing."""
       return {
           "filename": "test_script.sh",
           "target_path": "/app/scripts/test_script.sh",
           "permissions": "0755",
           "description": "Test script for unit testing"
       }

Using Fixtures
~~~~~~~~~~~~~

.. code-block:: python

   def test_file_processing(sample_file, valid_file_injection_data):
       """Test file processing with fixtures."""
       # Use the sample file
       content = sample_file.read_text()
       assert "Hello, World!" in content

       # Use the valid data
       model = FileInjectionCreate(**valid_file_injection_data)
       assert model.filename == "test_script.sh"

Best Practices
-------------

Test Organization
~~~~~~~~~~~~~~~~

1. **One test class per component** - Group related tests together
2. **Descriptive test names** - Make test purpose clear from the name
3. **Arrange-Act-Assert** - Follow the AAA pattern consistently
4. **Test one thing** - Each test should verify a single behaviour

.. code-block:: python

   class TestFileInjectionService:
       """Tests for the FileInjectionService class."""

       def test_create_injection_with_valid_data_returns_injection_model(self):
           """Test that creating injection with valid data returns proper model."""
           # Arrange
           service = FileInjectionService()
           data = {"filename": "test.sh", "target_path": "/app/test.sh"}

           # Act
           result = service.create_injection(data)

           # Assert
           assert isinstance(result, FileInjectionResponse)
           assert result.filename == "test.sh"

Test Data Management
~~~~~~~~~~~~~~~~~~

1. **Use factories for complex data** - Create reusable data builders
2. **Avoid hardcoded values** - Use constants or fixtures
3. **Test edge cases** - Include boundary values and error conditions

.. code-block:: python

   # Test data factory
   def create_file_injection_data(**overrides):
       """Create file injection data with optional overrides."""
       defaults = {
           "filename": "default.sh",
           "target_path": "/app/default.sh",
           "permissions": "0755"
       }
       defaults.update(overrides)
       return defaults

   def test_various_permissions():
       """Test different permission values."""
       permissions = ["0644", "0755", "0777", "0600"]
       for perm in permissions:
           data = create_file_injection_data(permissions=perm)
           model = FileInjectionCreate(**data)
           assert model.permissions == perm

Assertion Strategies
~~~~~~~~~~~~~~~~~~

1. **Specific assertions** - Test exact values when possible
2. **Multiple assertions** - Verify all important aspects
3. **Custom error messages** - Provide helpful failure information

.. code-block:: python

   def test_model_validation_with_custom_messages():
       """Test with helpful error messages."""
       model = FileInjectionCreate(
           filename="test.sh",
           target_path="/app/test.sh",
           permissions="0755"
       )

       assert model.filename == "test.sh", f"Expected 'test.sh', got '{model.filename}'"
       assert model.target_path.startswith("/app/"), "Target path should start with /app/"
       assert len(model.permissions) == 4, f"Permissions should be 4 chars, got {len(model.permissions)}"

Performance Considerations
-------------------------

Fast Test Execution
~~~~~~~~~~~~~~~~~~

Our unit tests are designed for speed:

* **36 tests in 0.06 seconds** - Extremely fast feedback
* **No external dependencies** - Tests run in isolation
* **Minimal setup/teardown** - Efficient fixture usage
* **Parallel execution** - Multiple workers for faster runs

Monitoring Test Performance
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run with timing information
   python -m pytest tests/unit/ --durations=10

   # Profile slow tests
   python -m pytest tests/unit/ --profile

   # Run with benchmark comparison
   python -m pytest tests/unit/ --benchmark-only --benchmark-compare

Continuous Integration
---------------------

Our unit tests run automatically in CI/CD pipelines:

.. code-block:: yaml

   # Example GitHub Actions workflow
   - name: Run Unit Tests
     run: |
       python -m pytest tests/unit/ \
         --cov=api \
         --cov=services \
         --cov-report=xml \
         --junitxml=test-results.xml

   - name: Upload Coverage
     uses: codecov/codecov-action@v3
     with:
       file: ./coverage.xml

Quality Gates
~~~~~~~~~~~~

Unit tests serve as quality gates:

* **Minimum 80% coverage** - Ensure adequate test coverage
* **Zero test failures** - All tests must pass
* **Fast execution** - Tests complete within time limits
* **Clean code** - Ruff linting passes

Next Steps
----------

After mastering unit testing, explore:

1. **Property-Based Testing** - :doc:`property-testing`
2. **Performance Testing** - :doc:`performance-testing`
3. **Integration Testing** - :doc:`integration-testing`
4. **Test Debugging** - :doc:`debugging`
