# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
docs/reports/_build/
docs/reports/reports/*-analysis.rst
!docs/reports/reports/notepadpp-analysis.rst

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ============================================================================
# TurdParty Test Coverage and Reporting - EXCLUDE FROM GIT
# ============================================================================

# Test results and coverage reports (exclude all, keep latest via symlinks)
test-results/
test_results/
test-reports/
test_reports/
htmlcov/
coverage-reports/
allure-results/
allure-report/
playwright-report/

# Coverage files
.coverage*
coverage-*.xml
coverage-*.json
coverage-*.html
coverage.xml
coverage.json
coverage.html

# Test result files
junit-*.xml
pytest-*.xml
behave-*.xml
behave-*.json
*-test-results.xml
*-test-report.html

# Performance test results
locust-*.html
locust-*.csv
performance-*.json
load-test-*.log

# Security scan results
bandit-*.json
safety-*.json
security-*.html

# Test artifacts and temporary files
*.log
*.tmp
*.temp
*_test_*
*_temp_*
test_*.db
test_*.sqlite
test_*.sqlite3

# Allow specific test configuration files
!tests/**/*.json
!tests/**/*.html
!tests/**/config/*.json
!tests/**/config/*.html
!tests/**/fixtures/*.json
!tests/**/fixtures/*.html

# Keep latest coverage report (symlinks to latest)
!coverage-latest.html
!coverage-latest.xml
!test-results-latest/

# ============================================================================
# TurdParty Application Specific - Updated for Clean Structure
# ============================================================================

# Runtime data directories (now in archive/runtime-data/)
data/
uploads/
logs/
reports/
db/

# Archive directory - track structure but not large files
archive/runtime-data/data/
archive/runtime-data/uploads/
archive/runtime-data/logs/
archive/runtime-data/db/
archive/runtime-data/reports/
archive/debug-temp/*.log
archive/debug-temp/*.tmp

# Keep archive structure files
!archive/**/*.md
!archive/**/*.rst
!archive/**/*.txt
!archive/**/README*

# Docker volumes and data
docker-data/
.docker/
docker-compose.override.yml

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Frontend build artifacts
frontend/build/
frontend/dist/
frontend/.next/
frontend/out/

# MinIO data
minio-data/

# Elasticsearch data
elasticsearch-data/

# Redis data
redis-data/

# Temporary development files
*.dev
*.local
*.backup
*.bak
*.orig

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to ignore the whole .idea directory.
.idea/

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Docker
.dockerignore
Dockerfile.prod

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Runtime data (cleaned up - now in archive/)
tmp/
temp/
*.tmp
*.temp

# Test artifacts
test-results/
playwright-report/
test-results.xml
allure-results/
allure-report/

# Behave test artifacts
behave-reports/
*.behave

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Editor files
*.swp
*.swo
*~
.vscode/settings.json

# Local development
.local/
.cache/

# Ruff cache
.ruff_cache/

# Pre-commit
.pre-commit-config.yaml.bak
