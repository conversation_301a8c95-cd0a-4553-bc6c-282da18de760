"""
Auto-generated service configuration for staging
Generated from config/service-urls.json - DO NOT EDIT MANUALLY
"""

SERVICE_CONFIG = {
    "environments": {
        "development": {
            "domain": "turdparty.localhost",
            "protocol": "http",
            "services": {
                "api": {
                    "subdomain": "api",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                },
                "frontend": {
                    "subdomain": "frontend",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "status": {
                    "subdomain": "status",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "docs": {
                    "subdomain": "docs",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "elasticsearch": {
                    "subdomain": "elasticsearch",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/_cluster/health"
                },
                "kibana": {
                    "subdomain": "kibana",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/api/status"
                },
                "minio": {
                    "subdomain": "minio",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/minio/health/live"
                },
                "vm-monitor": {
                    "subdomain": "vm-monitor",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                }
            }
        },
        "staging": {
            "domain": "staging.turdparty.dev",
            "protocol": "https",
            "services": {
                "api": {
                    "subdomain": "api",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                },
                "frontend": {
                    "subdomain": "app",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "status": {
                    "subdomain": "status",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "docs": {
                    "subdomain": "docs",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "elasticsearch": {
                    "subdomain": "search",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/_cluster/health"
                },
                "kibana": {
                    "subdomain": "kibana",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/api/status"
                },
                "minio": {
                    "subdomain": "storage",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/minio/health/live"
                },
                "vm-monitor": {
                    "subdomain": "vms",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                }
            }
        },
        "production": {
            "domain": "turdparty.com",
            "protocol": "https",
            "services": {
                "api": {
                    "subdomain": "api",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                },
                "frontend": {
                    "subdomain": "app",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "status": {
                    "subdomain": "status",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "docs": {
                    "subdomain": "docs",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/"
                },
                "elasticsearch": {
                    "subdomain": "search",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/_cluster/health"
                },
                "kibana": {
                    "subdomain": "analytics",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/api/status"
                },
                "minio": {
                    "subdomain": "storage",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/minio/health/live"
                },
                "vm-monitor": {
                    "subdomain": "vms",
                    "port": null,
                    "path": "",
                    "health_endpoint": "/health"
                }
            }
        },
        "local": {
            "domain": "localhost",
            "protocol": "http",
            "services": {
                "api": {
                    "subdomain": null,
                    "port": 8000,
                    "path": "",
                    "health_endpoint": "/health"
                },
                "frontend": {
                    "subdomain": null,
                    "port": 3000,
                    "path": "",
                    "health_endpoint": "/"
                },
                "status": {
                    "subdomain": null,
                    "port": 8080,
                    "path": "",
                    "health_endpoint": "/"
                },
                "docs": {
                    "subdomain": null,
                    "port": 8081,
                    "path": "",
                    "health_endpoint": "/"
                },
                "elasticsearch": {
                    "subdomain": null,
                    "port": 9200,
                    "path": "",
                    "health_endpoint": "/_cluster/health"
                },
                "kibana": {
                    "subdomain": null,
                    "port": 5601,
                    "path": "",
                    "health_endpoint": "/api/status"
                },
                "minio": {
                    "subdomain": null,
                    "port": 9000,
                    "path": "",
                    "health_endpoint": "/minio/health/live"
                },
                "vm-monitor": {
                    "subdomain": null,
                    "port": 8082,
                    "path": "",
                    "health_endpoint": "/health"
                }
            }
        }
    },
    "api_endpoints": {
        "files": {
            "upload": "/api/v1/files/upload",
            "download": "/api/v1/files/{file_id}/download",
            "metadata": "/api/v1/files/{file_id}",
            "list": "/api/v1/files"
        },
        "vms": {
            "create": "/api/v1/vms/",
            "status": "/api/v1/vms/{vm_id}",
            "list": "/api/v1/vms",
            "delete": "/api/v1/vms/{vm_id}",
            "inject": "/api/v1/vms/{vm_id}/inject",
            "execute": "/api/v1/vms/{vm_id}/execute",
            "logs": "/api/v1/vms/{vm_id}/logs"
        },
        "health": {
            "system": "/health",
            "elasticsearch": "/api/v1/health/elasticsearch",
            "minio": "/api/v1/health/minio",
            "kibana": "/api/v1/health/kibana",
            "database": "/api/v1/health/database",
            "redis": "/api/v1/health/redis",
            "logstash": "/api/v1/health/logstash"
        },
        "analytics": {
            "search": "/api/v1/analytics/search",
            "metrics": "/api/v1/analytics/metrics",
            "reports": "/api/v1/analytics/reports"
        },
        "reporting": {
            "binary_report": "/api/v1/reports/binary/{file_uuid}",
            "binary_summary": "/api/v1/reports/binary/{file_uuid}/summary",
            "installation_footprint": "/api/v1/reports/binary/{file_uuid}/footprint",
            "runtime_behavior": "/api/v1/reports/binary/{file_uuid}/runtime",
            "search": "/api/v1/reports/search",
            "compare": "/api/v1/reports/compare",
            "health": "/api/v1/reports/health"
        }
    },
    "elasticsearch_indices": {
        "logs": "turdparty-logs-*",
        "metrics": "turdparty-metrics-*",
        "vm_data": "turdparty-vm-*",
        "file_metadata": "turdparty-files-*"
    },
    "minio_buckets": {
        "uploads": "turdparty-uploads",
        "processed": "turdparty-processed",
        "reports": "turdparty-reports",
        "backups": "turdparty-backups"
    }
}

ENVIRONMENT = 'staging'
ENV_CONFIG = SERVICE_CONFIG['environments'][ENVIRONMENT]

# Convenience functions
def get_service_url(service_name: str, include_health: bool = False) -> str:
    """Get service URL for current environment"""
    from utils.service_urls import get_service_url_manager
    return get_service_url_manager().get_service_url(service_name, include_health)

def get_api_endpoint(endpoint: str, params: dict = None) -> str:
    """Get API endpoint URL for current environment"""
    from utils.service_urls import get_service_url_manager
    return get_service_url_manager().get_api_endpoint(endpoint, params or {})
